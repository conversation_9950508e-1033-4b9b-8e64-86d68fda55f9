#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
// Initialize Supabase client with correct credentials
const supabaseUrl = 'https://uayivasmxhelynrkekgg.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVheWl2YXNteGhlbHlucmtla2dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4NzUyMTcsImV4cCI6MjA2MTQ1MTIxN30.hk78RkL55Y6ktbZwgylz1aNJCGQZ8coPJgzS7VesVsk';

const supabase = createClient(supabaseUrl, supabaseKey);

async function runMigration() {
  console.log('🚀 Running database migration...');

  try {
    // Define SQL statements directly
    const sqlStatements = [
      'ALTER TABLE black_cards ADD COLUMN IF NOT EXISTS pack_id TEXT',
      'ALTER TABLE black_cards ADD COLUMN IF NOT EXISTS pack_name TEXT',
      'ALTER TABLE white_cards ADD COLUMN IF NOT EXISTS pack_id TEXT',
      'ALTER TABLE white_cards ADD COLUMN IF NOT EXISTS pack_name TEXT'
    ];

    console.log('🔧 Executing migration statements...');

    // Execute each statement
    for (const statement of sqlStatements) {
      console.log(`🔧 Executing: ${statement}`);

      const { data, error } = await supabase.rpc('exec_sql', {
        sql_query: statement
      });

      if (error) {
        console.error(`❌ Statement failed: ${statement}`, error);
        // Continue with other statements
      } else {
        console.log('✅ Statement executed successfully');
      }
    }

    console.log('🎉 Migration completed!');
    
  } catch (error) {
    console.error('💥 Fatal error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration()
  .then(() => {
    console.log('🏁 Migration script completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Migration script failed:', error);
    process.exit(1);
  });
