-- Sinful Secrets Database Schema
-- This extends the existing TnT database with Sinful Secrets specific tables

-- Sinful Secrets Games table (extends the existing games table concept)
CREATE TABLE IF NOT EXISTS sinful_secrets_games (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_code VARCHAR(10) UNIQUE NOT NULL,
    host_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'waiting' CHECK (status IN ('waiting', 'active', 'completed')),
    current_round INTEGER DEFAULT 1,
    current_player_index INTEGER DEFAULT 0,
    intensity_level VARCHAR(20) DEFAULT 'Playful' CHECK (intensity_level IN ('Playful', 'Sensual', 'Daring', 'Bold')),
    safe_word VARCHAR(50),
    game_phase VARCHAR(30) DEFAULT 'card-submission' CHECK (game_phase IN ('card-submission', 'card-judging', 'sinful-secrets')),
    is_card_phase BOOLEAN DEFAULT true,
    ai_persona TEXT DEFAULT 'You are the seductive, playful host of Sinful Secrets.',
    selected_card_packs TEXT[],
    custom_deck JSONB,
    current_prompt TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sinful Secrets Players table
CREATE TABLE IF NOT EXISTS sinful_secrets_players (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID REFERENCES sinful_secrets_games(id) ON DELETE CASCADE,
    player_name VARCHAR(100) NOT NULL,
    gender VARCHAR(20) DEFAULT 'Other' CHECK (gender IN ('M', 'F', 'Other')),
    is_host BOOLEAN DEFAULT false,
    has_played BOOLEAN DEFAULT false,
    penalty_drinks INTEGER DEFAULT 0,
    safe_word_usage INTEGER DEFAULT 0,
    score INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(game_id, player_name)
);

-- Card submissions for the card game phase
CREATE TABLE IF NOT EXISTS sinful_secrets_card_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID REFERENCES sinful_secrets_games(id) ON DELETE CASCADE,
    player_id UUID REFERENCES sinful_secrets_players(id) ON DELETE CASCADE,
    round_number INTEGER NOT NULL,
    card_text TEXT NOT NULL,
    is_winner BOOLEAN DEFAULT false,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Truth/Dare/Challenge responses
CREATE TABLE IF NOT EXISTS sinful_secrets_challenges (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID REFERENCES sinful_secrets_games(id) ON DELETE CASCADE,
    player_id UUID REFERENCES sinful_secrets_players(id) ON DELETE CASCADE,
    round_number INTEGER NOT NULL,
    challenge_type VARCHAR(20) CHECK (challenge_type IN ('Truth', 'Dare', 'WouldYouRather', 'Wildcard')),
    selected_number INTEGER CHECK (selected_number BETWEEN 1 AND 20),
    challenge_text TEXT,
    player_choice VARCHAR(20), -- 'Truth' or 'Dare' when applicable
    is_completed BOOLEAN DEFAULT false,
    used_safe_word BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI chat messages for the game
CREATE TABLE IF NOT EXISTS sinful_secrets_chat (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID REFERENCES sinful_secrets_games(id) ON DELETE CASCADE,
    sender VARCHAR(20) NOT NULL, -- 'host', 'ai', or player name
    message_text TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'chat' CHECK (message_type IN ('chat', 'system', 'ai_response')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI-generated card decks
CREATE TABLE IF NOT EXISTS sinful_secrets_card_decks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    deck_name VARCHAR(200) NOT NULL,
    deck_description TEXT,
    prompt_used TEXT NOT NULL,
    intensity_level VARCHAR(20) DEFAULT 'Playful' CHECK (intensity_level IN ('Playful', 'Sensual', 'Daring', 'Bold')),
    is_public BOOLEAN DEFAULT true,
    created_by VARCHAR(100), -- host name who created it
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual cards within AI-generated decks
CREATE TABLE IF NOT EXISTS sinful_secrets_deck_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    deck_id UUID REFERENCES sinful_secrets_card_decks(id) ON DELETE CASCADE,
    card_text TEXT NOT NULL,
    card_type VARCHAR(20) DEFAULT 'white' CHECK (card_type IN ('white', 'black')),
    intensity_level VARCHAR(20) DEFAULT 'Playful' CHECK (intensity_level IN ('Playful', 'Sensual', 'Daring', 'Bold')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Game events log for debugging and analytics
CREATE TABLE IF NOT EXISTS sinful_secrets_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID REFERENCES sinful_secrets_games(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_ss_games_code ON sinful_secrets_games(game_code);
CREATE INDEX IF NOT EXISTS idx_ss_games_status ON sinful_secrets_games(status);
CREATE INDEX IF NOT EXISTS idx_ss_players_game ON sinful_secrets_players(game_id);
CREATE INDEX IF NOT EXISTS idx_ss_card_submissions_game_round ON sinful_secrets_card_submissions(game_id, round_number);
CREATE INDEX IF NOT EXISTS idx_ss_challenges_game_round ON sinful_secrets_challenges(game_id, round_number);
CREATE INDEX IF NOT EXISTS idx_ss_chat_game ON sinful_secrets_chat(game_id);
CREATE INDEX IF NOT EXISTS idx_ss_card_decks_public ON sinful_secrets_card_decks(is_public);
CREATE INDEX IF NOT EXISTS idx_ss_card_decks_intensity ON sinful_secrets_card_decks(intensity_level);
CREATE INDEX IF NOT EXISTS idx_ss_deck_cards_deck ON sinful_secrets_deck_cards(deck_id);

-- Enable Row Level Security
ALTER TABLE sinful_secrets_games ENABLE ROW LEVEL SECURITY;
ALTER TABLE sinful_secrets_players ENABLE ROW LEVEL SECURITY;
ALTER TABLE sinful_secrets_card_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE sinful_secrets_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE sinful_secrets_chat ENABLE ROW LEVEL SECURITY;
ALTER TABLE sinful_secrets_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE sinful_secrets_card_decks ENABLE ROW LEVEL SECURITY;
ALTER TABLE sinful_secrets_deck_cards ENABLE ROW LEVEL SECURITY;

-- RLS Policies (allow all for now, can be tightened later)
CREATE POLICY "Allow all operations on sinful_secrets_games" ON sinful_secrets_games FOR ALL USING (true);
CREATE POLICY "Allow all operations on sinful_secrets_players" ON sinful_secrets_players FOR ALL USING (true);
CREATE POLICY "Allow all operations on sinful_secrets_card_submissions" ON sinful_secrets_card_submissions FOR ALL USING (true);
CREATE POLICY "Allow all operations on sinful_secrets_challenges" ON sinful_secrets_challenges FOR ALL USING (true);
CREATE POLICY "Allow all operations on sinful_secrets_chat" ON sinful_secrets_chat FOR ALL USING (true);
CREATE POLICY "Allow all operations on sinful_secrets_events" ON sinful_secrets_events FOR ALL USING (true);
CREATE POLICY "Allow all operations on sinful_secrets_card_decks" ON sinful_secrets_card_decks FOR ALL USING (true);
CREATE POLICY "Allow all operations on sinful_secrets_deck_cards" ON sinful_secrets_deck_cards FOR ALL USING (true);
