-- Migration script to add missing columns to existing Sinful Secrets tables
-- Run this if you already have the tables created without the new columns

-- Add missing columns to sinful_secrets_games table
DO $$ 
BEGIN
    -- Add selected_card_packs column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'sinful_secrets_games' 
        AND column_name = 'selected_card_packs'
    ) THEN
        ALTER TABLE sinful_secrets_games ADD COLUMN selected_card_packs TEXT[];
    END IF;

    -- Add custom_deck column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'sinful_secrets_games' 
        AND column_name = 'custom_deck'
    ) THEN
        ALTER TABLE sinful_secrets_games ADD COLUMN custom_deck JSONB;
    END IF;

    -- Add current_prompt column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sinful_secrets_games'
        AND column_name = 'current_prompt'
    ) THEN
        ALTER TABLE sinful_secrets_games ADD COLUMN current_prompt TEXT;
    END IF;

    -- Add is_deck_card column to card submissions if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sinful_secrets_card_submissions'
        AND column_name = 'is_deck_card'
    ) THEN
        ALTER TABLE sinful_secrets_card_submissions ADD COLUMN is_deck_card BOOLEAN DEFAULT false;
    END IF;

    -- Add current_judge_id column to games if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sinful_secrets_games'
        AND column_name = 'current_judge_id'
    ) THEN
        ALTER TABLE sinful_secrets_games ADD COLUMN current_judge_id UUID REFERENCES sinful_secrets_players(id);
    END IF;
END $$;

-- Create AI-generated card decks table if it doesn't exist
CREATE TABLE IF NOT EXISTS sinful_secrets_card_decks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    deck_name VARCHAR(200) NOT NULL,
    deck_description TEXT,
    prompt_used TEXT NOT NULL,
    intensity_level VARCHAR(20) DEFAULT 'Playful' CHECK (intensity_level IN ('Playful', 'Sensual', 'Daring', 'Bold')),
    is_public BOOLEAN DEFAULT true,
    created_by VARCHAR(100), -- host name who created it
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create individual cards table if it doesn't exist
CREATE TABLE IF NOT EXISTS sinful_secrets_deck_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    deck_id UUID REFERENCES sinful_secrets_card_decks(id) ON DELETE CASCADE,
    card_text TEXT NOT NULL,
    card_type VARCHAR(20) DEFAULT 'white' CHECK (card_type IN ('white', 'black')),
    intensity_level VARCHAR(20) DEFAULT 'Playful' CHECK (intensity_level IN ('Playful', 'Sensual', 'Daring', 'Bold')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for new tables
CREATE INDEX IF NOT EXISTS idx_ss_card_decks_public ON sinful_secrets_card_decks(is_public);
CREATE INDEX IF NOT EXISTS idx_ss_card_decks_intensity ON sinful_secrets_card_decks(intensity_level);
CREATE INDEX IF NOT EXISTS idx_ss_deck_cards_deck ON sinful_secrets_deck_cards(deck_id);

-- Enable RLS for new tables
ALTER TABLE sinful_secrets_card_decks ENABLE ROW LEVEL SECURITY;
ALTER TABLE sinful_secrets_deck_cards ENABLE ROW LEVEL SECURITY;

-- Create player hands table to store persistent white cards
CREATE TABLE IF NOT EXISTS sinful_secrets_player_hands (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    player_id UUID REFERENCES sinful_secrets_players(id) ON DELETE CASCADE,
    game_id UUID REFERENCES sinful_secrets_games(id) ON DELETE CASCADE,
    card_text TEXT NOT NULL,
    card_position INTEGER NOT NULL, -- Position in hand (0-6 for 7 cards)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(player_id, game_id, card_position)
);

-- Add indexes for player hands
CREATE INDEX IF NOT EXISTS idx_ss_player_hands_player ON sinful_secrets_player_hands(player_id);
CREATE INDEX IF NOT EXISTS idx_ss_player_hands_game ON sinful_secrets_player_hands(game_id);

-- Enable RLS for player hands
ALTER TABLE sinful_secrets_player_hands ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for new tables (allow all for testing)
DO $$
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Allow all operations on sinful_secrets_card_decks" ON sinful_secrets_card_decks;
    DROP POLICY IF EXISTS "Allow all operations on sinful_secrets_deck_cards" ON sinful_secrets_deck_cards;
    DROP POLICY IF EXISTS "Allow all operations on sinful_secrets_player_hands" ON sinful_secrets_player_hands;

    -- Create new policies
    CREATE POLICY "Allow all operations on sinful_secrets_card_decks" ON sinful_secrets_card_decks FOR ALL USING (true);
    CREATE POLICY "Allow all operations on sinful_secrets_deck_cards" ON sinful_secrets_deck_cards FOR ALL USING (true);
    CREATE POLICY "Allow all operations on sinful_secrets_player_hands" ON sinful_secrets_player_hands FOR ALL USING (true);
END $$;
