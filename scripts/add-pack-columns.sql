-- Add pack_id and pack_name columns to black_cards and white_cards tables
-- This allows filtering cards by specific card packs

-- Add columns to black_cards table
ALTER TABLE black_cards 
ADD COLUMN pack_id TEXT,
ADD COLUMN pack_name TEXT;

-- Add columns to white_cards table  
ALTER TABLE white_cards
ADD COLUMN pack_id TEXT,
ADD COLUMN pack_name TEXT;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_black_cards_pack_id ON black_cards(pack_id);
CREATE INDEX IF NOT EXISTS idx_white_cards_pack_id ON white_cards(pack_id);

-- Add comments for documentation
COMMENT ON COLUMN black_cards.pack_id IS 'ID of the card pack this card belongs to';
COMMENT ON COLUMN black_cards.pack_name IS 'Name of the card pack this card belongs to';
COMMENT ON COLUMN white_cards.pack_id IS 'ID of the card pack this card belongs to';
COMMENT ON COLUMN white_cards.pack_name IS 'Name of the card pack this card belongs to';
