#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import originalPack from '../src/data/packs/original-pack.json';
import spicyPack from '../src/data/packs/spicy-pack.json';
import partyPack from '../src/data/packs/party-pack.json';
import afterDarkPack from '../src/data/packs/after-dark-pack.json';
import daredevilPack from '../src/data/packs/daredevil-pack.json';

// Initialize Supabase client with correct credentials
const supabaseUrl = 'https://uayivasmxhelynrkekgg.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVheWl2YXNteGhlbHlucmtla2dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4NzUyMTcsImV4cCI6MjA2MTQ1MTIxN30.hk78RkL55Y6ktbZwgylz1aNJCGQZ8coPJgzS7VesVsk';

const supabase = createClient(supabaseUrl, supabaseKey);

interface CardPack {
  id: string;
  packName: string;
  description: string;
  category: string;
  blackCards: string[];
  whiteCards: string[];
}

const cardPacks: CardPack[] = [
  originalPack,
  spicyPack,
  partyPack,
  afterDarkPack,
  daredevilPack
];

async function populateDatabase() {
  console.log('🚀 Starting database population...');
  
  try {
    // Clear existing data
    console.log('🧹 Clearing existing card data...');
    await supabase.from('black_cards').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('white_cards').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    let totalBlackCards = 0;
    let totalWhiteCards = 0;
    
    for (const pack of cardPacks) {
      console.log(`📦 Processing pack: ${pack.packName}`);
      
      // Insert black cards with pack information
      if (pack.blackCards && pack.blackCards.length > 0) {
        const blackCardsData = pack.blackCards.map(cardText => ({
          card_text: cardText,
          pack_id: pack.id,
          pack_name: pack.packName
          // category_id is optional, leaving it null for now
        }));

        const { error: blackError } = await supabase
          .from('black_cards')
          .insert(blackCardsData);

        if (blackError) {
          console.error(`❌ Error inserting black cards for ${pack.packName}:`, blackError);
        } else {
          console.log(`✅ Inserted ${pack.blackCards.length} black cards for ${pack.packName}`);
          totalBlackCards += pack.blackCards.length;
        }
      }

      // Insert white cards with pack information
      if (pack.whiteCards && pack.whiteCards.length > 0) {
        const whiteCardsData = pack.whiteCards.map(cardText => ({
          card_text: cardText,
          pack_id: pack.id,
          pack_name: pack.packName
          // category_id is optional, leaving it null for now
        }));

        const { error: whiteError } = await supabase
          .from('white_cards')
          .insert(whiteCardsData);

        if (whiteError) {
          console.error(`❌ Error inserting white cards for ${pack.packName}:`, whiteError);
        } else {
          console.log(`✅ Inserted ${pack.whiteCards.length} white cards for ${pack.packName}`);
          totalWhiteCards += pack.whiteCards.length;
        }
      }
    }
    
    console.log(`\n🎉 Database population complete!`);
    console.log(`📊 Total cards inserted:`);
    console.log(`   🖤 Black cards: ${totalBlackCards}`);
    console.log(`   🤍 White cards: ${totalWhiteCards}`);
    
    // Verify the data
    const { count: blackCount } = await supabase
      .from('black_cards')
      .select('*', { count: 'exact', head: true });
      
    const { count: whiteCount } = await supabase
      .from('white_cards')
      .select('*', { count: 'exact', head: true });
      
    console.log(`\n✅ Verification:`);
    console.log(`   🖤 Black cards in database: ${blackCount}`);
    console.log(`   🤍 White cards in database: ${whiteCount}`);
    
  } catch (error) {
    console.error('💥 Fatal error during database population:', error);
    process.exit(1);
  }
}

// Run the script
populateDatabase()
  .then(() => {
    console.log('🏁 Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
