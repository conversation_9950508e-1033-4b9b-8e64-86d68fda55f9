@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 30 30% 98%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 348 89% 47%; /* Crimson Red */
    --primary-foreground: 30 20% 96%;

    --secondary: 240 5.9% 10%; /* Deep Charcoal */
    --secondary-foreground: 30 20% 96%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 326 100% 60%; /* Brighter Electric Pink */
    --accent-foreground: 30 20% 96%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 326 100% 60%; /* Use pink for borders */
    --input: 326 100% 60%; /* Use pink for inputs */
    --ring: 326 100% 60%; /* Brighter Electric Pink */

    --radius: 0.5rem;

    --sidebar-background: 240 10% 4.9%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 326 100% 60%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 326 100% 60%;
    --sidebar-ring: 326 100% 60%;
  }

  .dark {
    --background: 240 10% 4.9%;
    --foreground: 30 20% 96%;

    --card: 240 10% 4.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 4.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 348 89% 50%; /* Slightly brighter Crimson Red for dark mode */
    --primary-foreground: 0 0% 98%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 326 100% 60%; /* Brighter Electric Pink */
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 326 100% 60%;
    --input: 326 100% 60%;
    --ring: 326 100% 60%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 326 100% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 326 100% 60%;
    --sidebar-ring: 326 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-montserrat;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-poppins font-bold;
  }

  input,
  textarea,
  select {
    @apply focus:ring-tnt-pink focus:border-tnt-pink;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .text-shadow-lg {
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.5);
  }

  .text-shadow-contrast {
    text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.7);
  }

  .text-glow {
    text-shadow: 0 0 8px rgba(255, 0, 127, 0.6);
  }

  .card-white {
    @apply bg-tnt-ivory text-tnt-charcoal rounded-lg p-4 shadow-md;
  }

  .card-black {
    @apply bg-tnt-charcoal text-tnt-ivory rounded-lg p-4 shadow-md;
  }

  .glass-panel {
    @apply bg-white/10 backdrop-blur-md border border-tnt-pink/20 rounded-xl shadow-xl;
  }

  .pink-gradient {
    @apply bg-gradient-to-r from-tnt-crimson to-tnt-pink;
  }

  .pink-glow {
    box-shadow: 0 0 15px rgba(233, 30, 67, 0.6);
  }

  /* Button specific utilities */
  .btn-tnt-primary {
    @apply bg-tnt-pink text-white hover:bg-tnt-crimson font-semibold;
  }

  .btn-tnt-secondary {
    @apply bg-tnt-charcoal text-white border border-tnt-pink hover:bg-gray-800 font-semibold;
  }

  .btn-tnt-outline {
    @apply bg-transparent border border-tnt-pink text-tnt-pink hover:bg-tnt-pink/20 hover:text-white font-semibold;
  }

  /* Dark background behind elements for better contrast */
  .dark-panel {
    @apply bg-black/70 border border-tnt-pink/30 rounded-lg shadow-lg;
  }

  /* Header and footer backgrounds */
  .header-gradient {
    @apply bg-gradient-to-r from-tnt-charcoal to-black;
  }

  .footer-gradient {
    @apply bg-gradient-to-r from-black to-tnt-charcoal;
  }

  /* For inputs and form controls */
  .tnt-input {
    @apply bg-gray-800 border-tnt-pink/30 text-white focus:ring-tnt-pink focus:border-tnt-pink;
  }
}
