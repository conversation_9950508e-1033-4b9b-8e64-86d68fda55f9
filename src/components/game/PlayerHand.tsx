
import React, { useState } from 'react';
import GameCard from "../GameCard";
import { Button } from "@/components/ui/button";
import { Send } from 'lucide-react';

interface PlayerHandProps {
  cards: string[];
  onPlayCard: (card: string) => void;
  disabled?: boolean;
  hasSubmitted?: boolean;
}

const PlayerHand: React.FC<PlayerHandProps> = ({ 
  cards, 
  onPlayCard, 
  disabled = false,
  hasSubmitted = false
}) => {
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  
  const handleSelectCard = (card: string) => {
    if (disabled || hasSubmitted) return;
    setSelectedCard(card === selectedCard ? null : card);
  };
  
  const handleSubmit = () => {
    if (!selectedCard || disabled || hasSubmitted) return;
    onPlayCard(selectedCard);
    setSelectedCard(null);
  };
  
  return (
    <div className="w-full">
      {hasSubmitted ? (
        <div className="bg-tnt-pink/20 backdrop-blur-sm rounded-lg p-6 text-center">
          <h3 className="text-xl font-bold text-white mb-4">Card Submitted!</h3>
          <p className="text-gray-200">
            Waiting for other players to submit their cards...
          </p>
        </div>
      ) : (
        <>
          <h3 className="text-xl font-poppins font-bold mb-4">Your Cards:</h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            {cards.map((card, index) => (
              <div 
                key={index} 
                onClick={() => handleSelectCard(card)}
                className={`transform transition-all duration-200 ${
                  disabled ? 'opacity-50' : 'cursor-pointer'
                } ${selectedCard === card ? 'scale-105 ring-4 ring-tnt-pink' : ''}`}
              >
                <GameCard 
                  type="white" 
                  content={card}
                  isFlipped={selectedCard === card}
                />
              </div>
            ))}
          </div>
          
          <div className="flex justify-center">
            <Button
              onClick={handleSubmit}
              disabled={!selectedCard || disabled}
              className="bg-tnt-pink hover:bg-tnt-crimson text-white font-poppins text-xl px-8 py-6 rounded-lg shadow-lg gap-2"
            >
              Submit Card <Send className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default PlayerHand;
