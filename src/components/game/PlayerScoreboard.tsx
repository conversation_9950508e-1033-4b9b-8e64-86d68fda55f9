
import React from 'react';
import { Player } from '@/services/gameStateService';
import { Trophy } from 'lucide-react';

interface PlayerScoreboardProps {
  players: Player[];
}

const PlayerScoreboard: React.FC<PlayerScoreboardProps> = ({ players }) => {
  // Sort players by score (highest first)
  const sortedPlayers = [...players].sort((a, b) => b.score - a.score);
  
  return (
    <div className="bg-gray-800/60 backdrop-blur-sm rounded-lg border border-gray-700 overflow-hidden">
      <div className="bg-gradient-to-r from-tnt-pink/20 to-tnt-gold/20 py-3 px-4">
        <h2 className="text-xl font-bold text-white">Scoreboard</h2>
      </div>
      
      <div className="p-4">
        {sortedPlayers.map((player, index) => (
          <div 
            key={player.id} 
            className={`flex justify-between items-center py-3 px-2 ${
              index !== sortedPlayers.length - 1 ? 'border-b border-gray-700' : ''
            } ${index === 0 && player.score > 0 ? 'bg-tnt-gold/10' : ''}`}
          >
            <div className="flex items-center">
              {index === 0 && player.score > 0 && (
                <Trophy className="h-5 w-5 text-tnt-gold mr-2" />
              )}
              <span className={`font-medium ${player.isHost ? 'text-tnt-pink' : 'text-white'}`}>
                {player.name} {player.isHost ? '(Host)' : ''}
              </span>
            </div>
            <span className="text-lg font-bold text-tnt-gold">{player.score}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PlayerScoreboard;
