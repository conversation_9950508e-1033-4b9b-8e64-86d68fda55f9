
import React from 'react';
import { GameState } from '@/services/gameStateService';
import BlackCard from './BlackCard';
import PlayerScoreboard from './PlayerScoreboard';
import SubmissionZone from './SubmissionZone';
import RoundInfo from './RoundInfo';

interface GameBoardProps {
  gameState: GameState;
  onSelectWinner?: (submission: {player: string; card: string}) => void;
}

const GameBoard: React.FC<GameBoardProps> = ({ 
  gameState, 
  onSelectWinner 
}) => {
  const { phase, currentPrompt, submissions, roundNumber, players } = gameState;
  
  return (
    <div className="flex flex-col items-center w-full max-w-6xl mx-auto space-y-6">
      <RoundInfo 
        roundNumber={roundNumber}
        phase={phase}
      />
      
      <div className="w-full grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {/* Main game area */}
          <div className="space-y-6">
            {/* Black card (prompt) */}
            <BlackCard prompt={currentPrompt} />
            
            {/* Submissions Zone */}
            {phase === 'judging' && (
              <SubmissionZone 
                submissions={submissions}
                onSelectWinner={onSelectWinner}
                isJudging={true}
              />
            )}
            
            {phase === 'playing' && (
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
                <h3 className="text-xl font-poppins font-bold text-tnt-pink mb-4">Waiting for players to submit...</h3>
                
                <div className="space-y-2">
                  {players.map((player, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span>{player.name}</span>
                      <div className="flex items-center">
                        {player.hasSubmitted ? (
                          <span className="text-tnt-gold flex items-center">
                            <div className="w-3 h-3 rounded-full bg-tnt-gold mr-2"></div>
                            Card submitted!
                          </span>
                        ) : (
                          <span className="text-gray-400 flex items-center">
                            <div className="w-3 h-3 rounded-full border-2 border-gray-500 mr-2"></div>
                            Choosing card...
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="lg:col-span-1">
          {/* Scoreboard */}
          <PlayerScoreboard players={players} />
        </div>
      </div>
    </div>
  );
};

export default GameBoard;
