
import React from 'react';
import GameCard from "../GameCard";

interface BlackCardProps {
  prompt: string;
}

const BlackCard: React.FC<BlackCardProps> = ({ prompt }) => {
  return (
    <div className="w-full">
      <h3 className="text-xl font-poppins font-bold text-tnt-pink mb-4">Current Prompt:</h3>
      <GameCard 
        type="black" 
        content={prompt} 
        className="w-full mx-auto max-w-lg"
      />
    </div>
  );
};

export default BlackCard;
