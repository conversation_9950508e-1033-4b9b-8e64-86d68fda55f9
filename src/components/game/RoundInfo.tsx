
import React from 'react';
import { GamePhase } from '@/services/gameStateService';

interface RoundInfoProps {
  roundNumber: number;
  phase: GamePhase;
}

const RoundInfo: React.FC<RoundInfoProps> = ({ roundNumber, phase }) => {
  const getPhaseText = (): string => {
    switch (phase) {
      case 'waiting': return 'Waiting for players';
      case 'playing': return 'Players choosing cards';
      case 'judging': return 'Judging submissions';
      case 'results': return 'Round results';
      default: return 'Unknown phase';
    }
  };
  
  const getPhaseColor = (): string => {
    switch (phase) {
      case 'waiting': return 'text-gray-300';
      case 'playing': return 'text-tnt-pink';
      case 'judging': return 'text-tnt-gold';
      case 'results': return 'text-green-500';
      default: return 'text-gray-300';
    }
  };
  
  return (
    <div className="flex flex-col sm:flex-row justify-between items-center w-full bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-700">
      <h2 className="text-2xl font-poppins font-bold">Round {roundNumber}</h2>
      
      <div className="flex items-center mt-2 sm:mt-0">
        <span className="text-gray-300 mr-2">Status:</span>
        <span className={`font-medium ${getPhaseColor()}`}>{getPhaseText()}</span>
      </div>
    </div>
  );
};

export default RoundInfo;
