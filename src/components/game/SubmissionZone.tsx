
import React from 'react';
import GameCard from "../GameCard";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle } from 'lucide-react';

interface SubmissionZoneProps {
  submissions: Array<{player: string; card: string}>;
  onSelectWinner?: (submission: {player: string; card: string}) => void;
  isJudging: boolean;
}

const SubmissionZone: React.FC<SubmissionZoneProps> = ({ 
  submissions,
  onSelectWinner,
  isJudging
}) => {
  // Shuffle submissions for judging to avoid bias
  const shuffledSubmissions = React.useMemo(() => {
    if (isJudging) {
      return [...submissions].sort(() => Math.random() - 0.5);
    }
    return submissions;
  }, [submissions, isJudging]);
  
  return (
    <div className="w-full">
      <h3 className="text-xl font-poppins font-bold text-tnt-pink mb-4">
        {isJudging 
          ? 'Choose the best answer:'
          : 'Submissions:'}
      </h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-4">
        {shuffledSubmissions.map((submission, index) => (
          <div key={index} className="flex flex-col items-center">
            <GameCard 
              type="white" 
              content={submission.card}
              onClick={isJudging ? () => onSelectWinner?.(submission) : undefined}
              className={isJudging ? "cursor-pointer transform transition-all duration-200 hover:scale-105" : ""}
            />
            
            {isJudging && onSelectWinner && (
              <Button 
                variant="tntOutline"
                onClick={() => onSelectWinner(submission)}
                className="mt-2 gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Select as Winner
              </Button>
            )}
            
            {!isJudging && (
              <p className="mt-2 text-sm text-gray-300">
                Played by: <span className="font-bold text-tnt-pink">{submission.player}</span>
              </p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SubmissionZone;
