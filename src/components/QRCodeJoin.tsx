
import React from 'react';
import { QRCodeSVG } from 'qrcode.react';
import { Button } from "@/components/ui/button";
import { Link, ExternalLink } from "lucide-react";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface QRCodeJoinProps {
  gameCode: string;
}

const QRCodeJoin = ({ gameCode }: QRCodeJoinProps) => {
  // Use the SinfulSecrets player route
  const baseJoinUrl = `${window.location.origin}/sinful-secrets-player`;
  
  // Full join URL with game code included
  const joinUrl = `${baseJoinUrl}?code=${gameCode}`;
  
  const copyJoinUrl = () => {
    navigator.clipboard.writeText(joinUrl);
    toast.success("Join link copied to clipboard!");
  };
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          className="bg-tnt-pink/10 text-tnt-pink hover:bg-tnt-pink hover:text-white border border-tnt-pink"
        >
          <Link className="mr-2 h-4 w-4" />
          Show Join QR Code
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-tnt-charcoal border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-tnt-hotPink text-xl">Join Game: {gameCode}</DialogTitle>
          <DialogDescription className="text-tnt-whiteSmoke">
            Scan this QR code with your phone camera or share the link
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex flex-col items-center justify-center p-4 bg-white rounded-lg">
          <QRCodeSVG 
            value={joinUrl}
            size={200}
            bgColor={"#FFFFFF"}
            fgColor={"#000000"}
            level={"H"}
            includeMargin={false}
          />
        </div>
        
        <div className="flex flex-col gap-2 mt-4">
          <p className="text-sm text-tnt-whiteSmoke break-all">{joinUrl}</p>
          <div className="flex gap-2">
            <Button onClick={copyJoinUrl} className="flex-1 bg-tnt-pink hover:bg-tnt-crimson">
              <Link className="mr-2 h-4 w-4" />
              Copy Link
            </Button>
            <Button variant="outline" onClick={() => window.open(joinUrl, '_blank')} className="bg-transparent">
              <ExternalLink className="mr-2 h-4 w-4" />
              Open
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QRCodeJoin;
