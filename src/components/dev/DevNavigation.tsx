
import React from 'react';
import { Button } from "@/components/ui/button";

interface DevNavigationProps {
  isLoading: boolean;
  gameExists: boolean;
  onGoToHost: () => void;
  onGoToPlayer: () => void;
}

const DevNavigation = ({
  isLoading,
  gameExists,
  onGoToHost,
  onGoToPlayer
}: DevNavigationProps) => {
  return (
    <div className="flex justify-between gap-2 w-full">
      <Button 
        size="xs" 
        variant="tntPrimary"
        onClick={onGoToHost}
        disabled={isLoading || !gameExists}
        className="flex-1"
      >
        Host View
      </Button>
      <Button 
        size="xs" 
        variant="tntPrimary"
        onClick={onGoToPlayer}
        disabled={isLoading || !gameExists}
        className="flex-1"
      >
        Player View
      </Button>
    </div>
  );
};

export default DevNavigation;
