
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import type { GameInfo } from "@/hooks/useDevGame";
interface DevGameInfoProps {
  gameInfo: GameInfo | null;
  isLoading: boolean;
  onCreateGame: () => Promise<void>;
  onDealCards: () => Promise<void>;
  onStartGame: () => Promise<void>;
  onCreateSubmissions: () => Promise<void>;
}
const DevGameInfo = ({
  gameInfo,
  isLoading,
  onCreateGame,
  onDealCards,
  onStartGame,
  onCreateSubmissions
}: DevGameInfoProps) => {
  return <>
      <div className="space-y-2">
        <p className="text-sm">
          {gameInfo ? `Game: ${gameInfo.gameCode} (${gameInfo.gameId})` : "No dev game created yet"}
        </p>
        <Button size="sm" variant="devOutline" onClick={onCreateGame} disabled={isLoading} className="w-full">
          Create Dev Game
        </Button>
      </div>
      
      {gameInfo && <div className="space-y-2 pt-2 border-t border-gray-700">
          <Button size="sm" variant="devAction" className="w-full" onClick={onDealCards} disabled={isLoading}>
            Deal Cards
          </Button>
          <Button size="sm" variant="devOutline" className="w-full" onClick={onStartGame} disabled={isLoading}>
            Start Game (Round 1)
          </Button>
          <Button size="sm" variant="devOutline" className="w-full" onClick={onCreateSubmissions} disabled={isLoading || !gameInfo.blackCardId}>
            Create Submissions
          </Button>
        </div>}
    </>;
};
export default DevGameInfo;
