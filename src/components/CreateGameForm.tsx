
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

interface CreateGameFormProps {
  onCreate: (hostName: string) => void;
  loading?: boolean;
}

const CreateGameForm = ({ onCreate, loading = false }: CreateGameFormProps) => {
  const [hostName, setHostName] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!hostName.trim()) {
      toast.error("Please enter your host name");
      return;
    }
    
    onCreate(hostName);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 w-full max-w-md">
      <div className="space-y-2">
        <Input
          placeholder="Enter Your Host Name"
          value={hostName}
          onChange={(e) => setHostName(e.target.value)}
          className="bg-tnt-ivory text-tnt-charcoal font-montserrat border-2 border-tnt-pink placeholder:text-gray-400"
          maxLength={20}
          disabled={loading}
        />
      </div>
      <Button
        type="submit"
        className="w-full bg-tnt-pink hover:bg-tnt-crimson text-white font-poppins font-bold text-xl py-6"
        disabled={loading}
      >
        {loading ? "Creating Game..." : "CREATE GAME"}
      </Button>
    </form>
  );
};

export default CreateGameForm;
