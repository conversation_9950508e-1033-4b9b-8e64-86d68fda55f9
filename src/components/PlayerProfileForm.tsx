
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

interface PlayerProfileFormProps {
  onSubmit: (profileData: PlayerProfileData) => void;
  loading?: boolean;
}

export interface PlayerProfileData {
  username: string;
  relationship: string;
}

const PlayerProfileForm: React.FC<PlayerProfileFormProps> = ({ onSubmit, loading = false }) => {
  const [username, setUsername] = useState('');
  const [relationship, setRelationship] = useState('friend');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username.trim()) {
      toast.error("Please enter a username");
      return;
    }
    
    onSubmit({
      username: username.trim(),
      relationship
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 w-full max-w-md">
      <div className="space-y-2">
        <Label htmlFor="username">Choose Your Username</Label>
        <Input
          id="username"
          placeholder="Enter your username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          className="bg-tnt-ivory text-tnt-charcoal font-montserrat border-2 border-tnt-crimson placeholder:text-gray-400"
          maxLength={20}
          disabled={loading}
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="relationship">Relationship to Host/Others</Label>
        <Select
          disabled={loading}
          value={relationship}
          onValueChange={setRelationship}
        >
          <SelectTrigger id="relationship" className="bg-tnt-ivory text-tnt-charcoal border-2 border-tnt-crimson">
            <SelectValue placeholder="Select relationship" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="friend">Friend</SelectItem>
            <SelectItem value="partner">Partner</SelectItem>
            <SelectItem value="lover">Lover</SelectItem>
            <SelectItem value="spouse">Spouse</SelectItem>
            <SelectItem value="crush">Crush</SelectItem>
            <SelectItem value="coworker">Coworker</SelectItem>
            <SelectItem value="acquaintance">Acquaintance</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <Button
        type="submit"
        className="w-full bg-tnt-crimson hover:bg-tnt-pink text-white font-poppins font-bold text-xl py-6"
        disabled={loading}
      >
        {loading ? "Submitting..." : "CONTINUE"}
      </Button>
    </form>
  );
};

export default PlayerProfileForm;
