
import React from 'react';
import { cn } from "@/lib/utils";

interface TntLogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
}

const TntLogo = ({ className, size = 'md', animated = true }: TntLogoProps) => {
  const sizeClasses = {
    sm: 'text-xl md:text-2xl',
    md: 'text-2xl md:text-4xl',
    lg: 'text-4xl md:text-6xl',
    xl: 'text-5xl md:text-7xl'
  };

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <div className="relative inline-flex items-center">
        <h1 className={cn("font-bold font-poppins tracking-tight", sizeClasses[size])}>
          <span className="text-tnt-crimson">T</span>
          <span className="relative">
            <span className="text-tnt-pink">n</span>
            <span className={cn("absolute -top-1.5 right-0 text-tnt-gold text-[0.5em]", animated && "animate-explosion")}>
              &#x2727;
            </span>
          </span>
          <span className="text-tnt-crimson">T</span>
          <span className="text-tnt-ivory text-[0.5em] align-super">™</span>
        </h1>
        <div className={cn("ml-2 font-poppins text-[0.4em] text-tnt-ivory", sizeClasses[size])}>
          <div className="leading-tight">Tempted</div>
          <div className="text-tnt-pink leading-tight">'n</div>
          <div className="leading-tight">Twisted</div>
        </div>
      </div>
    </div>
  );
};

export default TntLogo;
