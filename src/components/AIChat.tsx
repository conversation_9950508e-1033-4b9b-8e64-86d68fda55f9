
import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowRight, Volume } from "lucide-react";
import { Toggle } from "@/components/ui/toggle";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Badge } from "@/components/ui/badge";

interface AIChatProps {
  gameId: string;
  playerId: string;
  playerName: string;
  onSendMessage: (message: string) => Promise<void>;
  messages: Array<{id: string; sender: string; text: string; timestamp: Date}>;
}

const VOICE_OPTIONS = [
  { value: 'alloy', name: 'Alloy', icon: <Volume size={16} /> },
  { value: 'echo', name: 'Echo', icon: <Volume size={16} /> },
  { value: 'shimmer', name: 'Shimmer', icon: <Volume size={16} /> },
];

const AIChat: React.FC<AIChatProps> = ({ 
  gameId, 
  playerId, 
  playerName, 
  onSendMessage, 
  messages 
}) => {
  const [newMessage, setNewMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState('alloy');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Auto-scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    if (!newMessage.trim()) return;
    
    setIsSending(true);
    try {
      await onSendMessage(newMessage);
      setNewMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleVoiceChange = (voice: string) => {
    setSelectedVoice(voice);
    // Future integration: Update voice settings in the game
  };

  return (
    <div className="flex flex-col h-full border border-tnt-pink/30 rounded-lg overflow-hidden bg-gray-900/90 backdrop-blur-sm shadow-lg">
      <div className="bg-gradient-to-r from-tnt-pink to-tnt-crimson p-3 border-b border-tnt-pink/30">
        <div className="flex justify-between items-center">
          <h3 className="font-poppins font-bold text-white text-lg">Game Host AI Chat</h3>
          <div className="flex items-center gap-2">
            {VOICE_OPTIONS.map(voice => (
              <Toggle 
                key={voice.value}
                pressed={selectedVoice === voice.value}
                onPressedChange={() => handleVoiceChange(voice.value)}
                variant="voiceOption"
                size="xs"
                className="flex items-center gap-1"
              >
                {voice.icon}
                {voice.name}
              </Toggle>
            ))}
          </div>
        </div>
      </div>
      
      <ScrollArea className="flex-grow p-4 bg-gray-900/80">
        <div className="space-y-4">
          {messages.map((msg) => (
            <div 
              key={msg.id}
              className={`flex ${msg.sender === 'host' ? 'justify-start' : 'justify-end'}`}
            >
              <div 
                className={`max-w-[80%] rounded-lg p-3 ${
                  msg.sender === 'host'
                    ? 'bg-gray-800 text-white border border-tnt-pink/20'
                    : 'bg-tnt-pink text-white'
                }`}
              >
                {msg.sender !== 'host' && (
                  <p className="text-xs opacity-75 mb-1">{playerName}</p>
                )}
                <p>{msg.text}</p>
                <p className="text-xs opacity-50 mt-1 text-right">
                  {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      
      <form 
        onSubmit={handleSendMessage}
        className="p-3 border-t border-tnt-pink/30 flex gap-2 bg-gray-900"
      >
        <Input
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type your message to the AI host..."
          disabled={isSending}
          className="flex-grow bg-gray-800 border-tnt-pink/30 text-white focus-visible:ring-tnt-pink"
        />
        <Button 
          type="submit" 
          disabled={isSending || !newMessage.trim()} 
          variant="tntPrimary"
          aria-label="Send message"
        >
          <ArrowRight size={18} />
        </Button>
      </form>
    </div>
  );
};

export default AIChat;
