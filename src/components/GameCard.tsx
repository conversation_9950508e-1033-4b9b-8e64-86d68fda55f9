
import React from 'react';
import { cn } from "@/lib/utils";

interface GameCardProps {
  type: 'black' | 'white';
  content: string;
  className?: string;
  isFlipped?: boolean;
  onClick?: () => void;
}

const GameCard = ({ type, content, className, isFlipped = false, onClick }: GameCardProps) => {
  const cardClasses = {
    black: "bg-tnt-charcoal text-tnt-ivory border-2 border-tnt-gold",
    white: "bg-tnt-ivory text-tnt-charcoal border-2 border-tnt-crimson",
  };

  return (
    <div 
      className={cn(
        "relative rounded-lg p-6 shadow-lg cursor-pointer transform transition-transform duration-300 hover:scale-105 min-h-[200px] flex items-center justify-center",
        cardClasses[type],
        isFlipped && "animate-card-flip",
        className
      )}
      onClick={onClick}
    >
      <p className="text-center font-poppins font-medium text-lg">
        {content}
      </p>
      <div className="absolute bottom-4 right-4 font-bold text-xs">
        <span className="font-poppins">
          Tempted 'n Twisted™
        </span>
      </div>
    </div>
  );
};

export default GameCard;
