
import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

interface JoinGameFormProps {
  onJoin: (gameCode: string, playerName: string) => void;
  loading?: boolean;
  initialGameCode?: string;
  isPrivateGame?: boolean;
}

const JoinGameForm = ({ 
  onJoin, 
  loading = false, 
  initialGameCode = '',
  isPrivateGame = false
}: JoinGameFormProps) => {
  const [gameCode, setGameCode] = useState('');
  const [playerName, setPlayerName] = useState('');
  
  // Set the initial game code if provided
  useEffect(() => {
    if (initialGameCode) {
      setGameCode(initialGameCode);
    }
  }, [initialGameCode]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!gameCode.trim()) {
      toast.error("Please enter a game code");
      return;
    }
    
    if (!playerName.trim()) {
      toast.error("Please enter your player name");
      return;
    }
    
    onJoin(gameCode.toUpperCase(), playerName);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 w-full max-w-md">
      <div className="space-y-2">
        <Input
          placeholder={`Enter ${isPrivateGame ? 'Private' : 'Game'} Code`}
          value={gameCode}
          onChange={(e) => setGameCode(e.target.value)}
          className="bg-tnt-ivory text-tnt-charcoal text-center text-2xl font-poppins h-16 border-2 border-tnt-crimson placeholder:text-gray-400 uppercase"
          maxLength={6}
          disabled={loading}
        />
      </div>
      <div className="space-y-2">
        <Input
          placeholder="Enter Your Name"
          value={playerName}
          onChange={(e) => setPlayerName(e.target.value)}
          className="bg-tnt-ivory text-tnt-charcoal font-montserrat border-2 border-tnt-crimson placeholder:text-gray-400"
          maxLength={20}
          disabled={loading}
        />
      </div>
      <Button
        type="submit"
        className="w-full bg-tnt-crimson hover:bg-tnt-pink text-white font-poppins font-bold text-xl py-6"
        disabled={loading}
      >
        {loading ? "Joining..." : `JOIN ${isPrivateGame ? 'PRIVATE' : ''} GAME`}
      </Button>
    </form>
  );
};

export default JoinGameForm;
