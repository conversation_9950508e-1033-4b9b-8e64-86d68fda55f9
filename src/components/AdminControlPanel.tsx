
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Settings, Save, ArrowRight, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import * as adminService from '@/services/adminService';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import HostPersonaEditor from '@/components/host/HostPersonaEditor';

interface AdminControlPanelProps {
  gameId: string | null;
}

export interface AdminSettings {
  aiPersona: string;
  responseLength: "short" | "medium" | "long";
  turnOrder: "random" | "sequential" | "host";
  escalationSpeed: "normal" | "fast" | "slow";
  miniGameFrequency: number;
  promptDifficultyScaling: boolean;
  isPrivateGame: boolean;
}

const DEFAULT_SETTINGS: AdminSettings = {
  aiPersona: "You are the fun, flirty host of Tempted 'n Twisted, a spicy party game. Your tone is playful, mischievous, and a bit suggestive, but never crude or explicit. You guide players through the game with enthusiasm and humor.",
  responseLength: "medium",
  turnOrder: "sequential",
  escalationSpeed: "normal",
  miniGameFrequency: 3,
  promptDifficultyScaling: true,
  isPrivateGame: false
};

const AdminControlPanel: React.FC<AdminControlPanelProps> = ({ gameId }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [savedPresets, setSavedPresets] = useState<Array<{id: string, name: string}>>([]);
  const [newPresetName, setNewPresetName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isDevMode, setIsDevMode] = useState(false);
  const [activeTab, setActiveTab] = useState<"game" | "host">("game");
  
  const form = useForm<AdminSettings>({
    defaultValues: DEFAULT_SETTINGS
  });

  const { watch, reset, control } = form;
  const currentSettings = watch();
  
  useEffect(() => {
    // Check if we're in dev mode
    const devMode = process.env.NODE_ENV === 'development' || localStorage.getItem('devMode') === 'true';
    setIsDevMode(devMode);
    
    // Make the panel open by default in dev mode
    if (devMode) {
      setIsOpen(true);
    }
  }, []);
  
  // Load saved presets
  useEffect(() => {
    if (gameId) {
      loadPresets();
    }
  }, [gameId]);
  
  const loadPresets = async () => {
    try {
      const presets = await adminService.loadPresets();
      setSavedPresets(presets);
    } catch (error) {
      console.error("Failed to load presets:", error);
      if (isDevMode) {
        // In dev mode, show a toast with the error
        toast.error(`Failed to load presets: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  const saveCurrentSettings = async () => {
    if (!newPresetName.trim()) {
      toast.error("Please enter a name for this preset");
      return;
    }
    
    setIsLoading(true);
    try {
      await adminService.savePreset(newPresetName, currentSettings);
      toast.success(`Preset "${newPresetName}" saved successfully`);
      setNewPresetName('');
      loadPresets();
    } catch (error) {
      toast.error("Failed to save preset");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const loadPreset = async (presetId: string) => {
    setIsLoading(true);
    try {
      const settings = await adminService.loadPreset(presetId);
      reset(settings);
      toast.success("Preset loaded successfully");
    } catch (error) {
      toast.error("Failed to load preset");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const forceEscalation = async () => {
    if (!gameId) {
      toast.error("No active game to escalate");
      return;
    }
    
    try {
      await adminService.forceEscalation(gameId);
      toast.success("Forced escalation to next level");
    } catch (error) {
      toast.error("Failed to force escalation");
      console.error(error);
    }
  };
  
  const forceMiniGame = async () => {
    if (!gameId) {
      toast.error("No active game to trigger mini-game");
      return;
    }
    
    try {
      await adminService.forceMiniGame(gameId);
      toast.success("Mini-game triggered for next round");
    } catch (error) {
      toast.error("Failed to trigger mini-game");
      console.error(error);
    }
  };
  
  // Apply settings changes
  const applySettings = async (data: AdminSettings) => {
    if (!gameId) {
      toast.error("No active game to apply settings");
      return;
    }
    
    try {
      await adminService.updateGameSettings(gameId, data);
      toast.success("Settings applied to current game");
    } catch (error) {
      toast.error("Failed to apply settings");
      console.error(error);
    }
  };

  return (
    <div className="fixed right-4 top-20 z-30">
      <Collapsible 
        open={isOpen} 
        onOpenChange={setIsOpen}
        className="w-80 bg-gray-800/95 backdrop-blur-md rounded-lg border border-tnt-pink/30 shadow-xl overflow-hidden"
      >
        <CollapsibleTrigger asChild>
          <Button 
            variant="tntOutline" 
            className="flex items-center gap-2 absolute right-0 top-0 bg-tnt-charcoal/90 shadow-lg border-tnt-pink"
            aria-label="Toggle admin controls"
          >
            <Settings size={16} /> 
            {isOpen ? "Hide Controls" : "Game Controls"}
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="p-4">
          <h3 className="text-xl font-bold mb-4 text-tnt-pink">Host Control Panel</h3>
          
          {/* Show a warning if no gameId is provided */}
          {!gameId && (
            <div className="mb-4 p-2 bg-yellow-600/30 border border-yellow-500 rounded-md flex items-center gap-2 text-yellow-300 text-sm">
              <AlertCircle size={16} />
              <p>No active game detected. Some features may be disabled.</p>
            </div>
          )}
          
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "game" | "host")} className="w-full">
            <TabsList className="w-full mb-4 bg-gray-700">
              <TabsTrigger value="game" className="flex-1 data-[state=active]:bg-tnt-pink data-[state=active]:text-white">
                Game Controls
              </TabsTrigger>
              <TabsTrigger value="host" className="flex-1 data-[state=active]:bg-tnt-gold data-[state=active]:text-tnt-charcoal">
                Host Persona
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="game">
              <Form {...form}>
                <form 
                  className="space-y-4"
                  onSubmit={(e) => {
                    e.preventDefault();
                    applySettings(form.getValues());
                  }}
                >
                  <FormField
                    control={form.control}
                    name="turnOrder"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white font-medium">Turn Order</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                              <SelectValue placeholder="Select turn order" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-gray-800 border-gray-700 text-white">
                            <SelectItem value="random" className="focus:bg-tnt-pink/30 focus:text-white">Random</SelectItem>
                            <SelectItem value="sequential" className="focus:bg-tnt-pink/30 focus:text-white">Sequential</SelectItem>
                            <SelectItem value="host" className="focus:bg-tnt-pink/30 focus:text-white">Host Picks</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="isPrivateGame"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-600 bg-gray-700/50 p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Private Game Mode</FormLabel>
                          <FormDescription className="text-gray-300">Enable AI host and private game features</FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <div className="pt-2">
                    <Button 
                      type="submit" 
                      className="w-full bg-tnt-pink hover:bg-tnt-crimson text-white"
                      disabled={!gameId}
                    >
                      Apply Game Settings
                    </Button>
                  </div>
                </form>
              </Form>
              
              <div className="mt-6 border-t border-gray-600 pt-4">
                <h4 className="text-sm font-medium mb-2 text-white">Game Controls</h4>
                
                <div className="flex gap-2 mb-4">
                  <Button 
                    onClick={forceEscalation}
                    disabled={!gameId}
                    className="flex-1 bg-tnt-gold/30 hover:bg-tnt-gold/60 text-tnt-gold border border-tnt-gold/60"
                  >
                    Force Escalation
                  </Button>
                  <Button 
                    onClick={forceMiniGame}
                    disabled={!gameId}
                    className="flex-1 bg-tnt-crimson/30 hover:bg-tnt-crimson/60 text-white border border-tnt-crimson/60"
                  >
                    Trigger Mini-Game
                  </Button>
                </div>
              </div>
              
              <div className="mt-4 border-t border-gray-600 pt-4">
                <h4 className="text-sm font-medium mb-2 text-white">Save/Load Presets</h4>
                
                <div className="flex gap-2 mb-4">
                  <Input 
                    placeholder="Preset name" 
                    value={newPresetName}
                    onChange={(e) => setNewPresetName(e.target.value)}
                    className="flex-1 bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
                  />
                  <Button 
                    onClick={saveCurrentSettings}
                    disabled={isLoading || !newPresetName.trim()}
                    className="flex-shrink-0 bg-tnt-pink hover:bg-tnt-crimson text-white"
                  >
                    <Save size={16} className="mr-1" />
                    Save
                  </Button>
                </div>
                
                <div className="mt-4">
                  <Label htmlFor="loadPreset" className="text-sm mb-1 block text-white">Load Preset</Label>
                  <Select onValueChange={(value) => loadPreset(value)}>
                    <SelectTrigger className="w-full bg-gray-700 border-gray-600 text-white">
                      <SelectValue placeholder="Select a preset" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700 text-white">
                      {savedPresets.map((preset) => (
                        <SelectItem key={preset.id} value={preset.id} className="focus:bg-tnt-pink/30 focus:text-white">{preset.name}</SelectItem>
                      ))}
                      {savedPresets.length === 0 && (
                        <div className="px-2 py-4 text-gray-400 text-center">No saved presets</div>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="host">
              <HostPersonaEditor 
                gameId={gameId}
                settings={currentSettings}
                onSettingsChange={reset}
                onApplySettings={applySettings}
              />
            </TabsContent>
          </Tabs>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default AdminControlPanel;
