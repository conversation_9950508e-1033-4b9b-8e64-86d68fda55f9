
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { getCardPacks, CardPack } from "@/services/packService";
import { toast } from "sonner";
import { Package, CheckCircle2 } from "lucide-react";

interface CardPackSelectorProps {
  onPacksSelected: (packIds: string[]) => void;
  initialSelectedPacks?: string[];
}

const CardPackSelector: React.FC<CardPackSelectorProps> = ({ onPacksSelected, initialSelectedPacks = [] }) => {
  const [availablePacks, setAvailablePacks] = useState<CardPack[]>([]);
  const [selectedPacks, setSelectedPacks] = useState<string[]>(initialSelectedPacks);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  // Load available card packs
  useEffect(() => {
    const loadPacks = async () => {
      try {
        const packs = await getCardPacks();
        setAvailablePacks(packs);
        // If no packs are pre-selected, select the base pack by default
        if (initialSelectedPacks.length === 0) {
          setSelectedPacks(["original"]);
        }
      } catch (error) {
        console.error("Failed to load card packs:", error);
        toast.error("Failed to load card packs");
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPacks();
  }, []);
  
  const togglePack = (packId: string) => {
    setSelectedPacks((current) => {
      if (current.includes(packId)) {
        // Don't allow deselecting the last pack
        if (current.length <= 1) {
          toast.error("You must select at least one pack");
          return current;
        }
        return current.filter(id => id !== packId);
      } else {
        return [...current, packId];
      }
    });
  };
  
  const handleConfirm = () => {
    if (selectedPacks.length === 0) {
      toast.error("You must select at least one pack");
      return;
    }
    
    onPacksSelected(selectedPacks);
    toast.success(`${selectedPacks.length} card ${selectedPacks.length === 1 ? 'pack' : 'packs'} selected`);
  };
  
  const getCategoryColor = (category: string) => {
    switch(category) {
      case "base": return "bg-blue-600 text-white";
      case "adult": return "bg-tnt-crimson text-white";
      case "party": return "bg-tnt-gold text-tnt-charcoal";
      case "custom": return "bg-purple-600 text-white";
      case "original": return "bg-blue-600 text-white";
      case "dare": return "bg-tnt-pink text-white";
      default: return "bg-gray-600 text-white";
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="w-12 h-12 border-4 border-tnt-pink border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold text-white">Select Card Packs</h3>
        <div className="text-sm text-gray-300">
          {selectedPacks.length} pack{selectedPacks.length !== 1 ? 's' : ''} selected
        </div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {availablePacks.map((pack) => (
          <Card 
            key={pack.id} 
            className={`bg-gray-800 border ${selectedPacks.includes(pack.id) ? 'border-tnt-pink shadow-tnt-pink/20 shadow-lg' : 'border-gray-700'} cursor-pointer transition-all duration-200`}
            onClick={() => togglePack(pack.id)}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <div className={`inline-block px-2 py-1 text-xs rounded mb-2 ${getCategoryColor(pack.category)}`}>
                    {pack.isCustom ? 'Custom' : pack.category.charAt(0).toUpperCase() + pack.category.slice(1)}
                  </div>
                  <CardTitle className="text-white">{pack.packName}</CardTitle>
                </div>
                {selectedPacks.includes(pack.id) && (
                  <CheckCircle2 className="text-tnt-pink h-6 w-6" />
                )}
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-gray-300">{pack.description}</CardDescription>
              <div className="mt-2 text-sm text-gray-400">
                <div>{pack.blackCards.length} prompt cards</div>
                <div>{pack.whiteCards.length} answer cards</div>
                {pack.isCustom && pack.isPublic && (
                  <div className="text-green-400 text-xs mt-1">Community deck</div>
                )}
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Checkbox 
                checked={selectedPacks.includes(pack.id)}
                onCheckedChange={() => togglePack(pack.id)}
                id={`pack-${pack.id}`}
                className="data-[state=checked]:bg-tnt-pink data-[state=checked]:border-tnt-pink"
              />
              <label htmlFor={`pack-${pack.id}`} className="ml-2 text-sm text-gray-300 cursor-pointer">
                {selectedPacks.includes(pack.id) ? 'Selected' : 'Select this pack'}
              </label>
            </CardFooter>
          </Card>
        ))}
      </div>
      
      <div className="flex justify-end mt-6">
        <Button 
          variant="default"
          onClick={handleConfirm}
          className="bg-tnt-pink hover:bg-tnt-pink/80 px-6"
          disabled={selectedPacks.length === 0}
        >
          <Package className="mr-2 h-4 w-4" />
          Confirm Selection
        </Button>
      </div>
    </div>
  );
};

export default CardPackSelector;
