
import React, { useEffect, useState } from 'react';
import { getHostLine, GamePhase } from "@/services/hostService";
import { AdminSettings } from "@/components/AdminControlPanel";
import { cn } from "@/lib/utils";

interface HostLineDisplayProps {
  gamePhase: GamePhase;
  roundNumber: number;
  settings: AdminSettings;
  winner?: {player: string; card: string} | null;
  currentPrompt?: string;
  className?: string;
}

const HostLineDisplay: React.FC<HostLineDisplayProps> = ({
  gamePhase,
  roundNumber,
  settings,
  winner,
  currentPrompt,
  className
}) => {
  const [hostLine, setHostLine] = useState<string>("");
  const [isAnimating, setIsAnimating] = useState(false);
  
  useEffect(() => {
    // Generate a new host line when the phase changes
    const newLine = getHostLine(gamePhase, settings, roundNumber, winner, currentPrompt);
    setIsAnimating(true);
    setHostLine(newLine);
    
    // Reset animation after it completes
    const timer = setTimeout(() => {
      setIsAnimating(false);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [gamePhase, roundNumber, settings, winner?.player]);
  
  // Add random flavor lines occasionally
  useEffect(() => {
    // 10% chance of a random flavor line every 8-15 seconds
    const interval = setInterval(() => {
      if (Math.random() < 0.1) {
        const flavorLine = getHostLine("waiting", settings, roundNumber, winner, currentPrompt);
        setIsAnimating(true);
        setHostLine(flavorLine);
        
        setTimeout(() => {
          setIsAnimating(false);
        }, 500);
      }
    }, Math.random() * 7000 + 8000);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className={cn(
      "bg-gradient-to-r from-tnt-pink/20 to-tnt-gold/20 backdrop-blur-sm p-4 rounded-lg border border-tnt-pink/30 shadow-lg",
      isAnimating ? "animate-pulse" : "",
      className
    )}>
      <div className="flex items-center gap-3 mb-2">
        <div className="w-3 h-3 rounded-full bg-tnt-pink animate-pulse"></div>
        <h3 className="font-bold text-tnt-pink text-lg">Host</h3>
      </div>
      <p className="text-xl font-poppins text-white">{hostLine}</p>
    </div>
  );
};

export default HostLineDisplay;
