
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>itle, SheetTrigger } from "@/components/ui/sheet";
import { Settings } from "lucide-react";
import AdminControlPanel from '@/components/AdminControlPanel';

interface AdminControlButtonProps {
  gameId?: string;
}

const AdminControlButton: React.FC<AdminControlButtonProps> = ({ gameId }) => {
  return (
    <div className="fixed top-4 right-4 z-50">
      <Sheet>
        <SheetTrigger asChild>
          <Button
            variant="tntOutline"
            size="sm"
            className="bg-black/80 shadow-lg flex items-center gap-2 border-tnt-pink hover:bg-black/90"
          >
            <Settings size={16} />
            Host Controls
          </Button>
        </SheetTrigger>
        <SheetContent className="w-96 bg-gray-900/95 border-tnt-pink/40">
          <SheetHeader>
            <SheetTitle className="text-tnt-pink">Host Control Panel</SheetTitle>
          </SheetHeader>
          <div className="mt-6">
            <AdminControlPanel gameId={gameId || "sinful-secrets-dev"} />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default AdminControlButton;
