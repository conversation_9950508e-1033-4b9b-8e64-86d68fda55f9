
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { AdminSettings } from "@/components/AdminControlPanel";
import { getSamplePersona, loadHostPersonas, saveHostPersona } from "@/services/hostService";
import { toast } from "sonner";
import { Save, RefreshCcw, Check } from "lucide-react";

interface HostPersonaEditorProps {
  gameId: string | null;
  settings: AdminSettings;
  onSettingsChange: (settings: AdminSettings) => void;
  onApplySettings: (settings: AdminSettings) => void;
}

const HostPersonaEditor: React.FC<HostPersonaEditorProps> = ({ 
  gameId, 
  settings,
  onSettingsChange,
  onApplySettings
}) => {
  const [savedPersonas, setSavedPersonas] = useState<Array<{id: string, name: string}>>([]);
  const [newPersonaName, setNewPersonaName] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  const form = useForm<AdminSettings>({
    defaultValues: settings
  });
  
  // Load saved personas when component mounts
  useEffect(() => {
    loadSavedPersonas();
  }, []);
  
  const loadSavedPersonas = async () => {
    try {
      const personas = await loadHostPersonas();
      setSavedPersonas(personas.map(p => ({ id: p.id, name: p.name })));
    } catch (error) {
      console.error("Failed to load host personas:", error);
    }
  };
  
  const handleApplyTemplate = (type: string) => {
    const templateSettings = getSamplePersona(type);
    form.reset(templateSettings);
    onSettingsChange(templateSettings);
    toast.success(`Applied ${type} template`);
  };
  
  const handleSavePersona = async () => {
    if (!newPersonaName.trim()) {
      toast.error("Please enter a name for this persona");
      return;
    }
    
    setIsLoading(true);
    try {
      await saveHostPersona(newPersonaName, form.getValues());
      toast.success(`Persona "${newPersonaName}" saved successfully`);
      setNewPersonaName("");
      await loadSavedPersonas();
    } catch (error) {
      toast.error("Failed to save persona");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleLoadPersona = async (personaId: string) => {
    setIsLoading(true);
    try {
      const personaSettings = await getSamplePersona("flirty"); // In a real implementation, this would be loadHostPersona(personaId)
      form.reset(personaSettings);
      onSettingsChange(personaSettings);
      toast.success("Persona loaded successfully");
    } catch (error) {
      toast.error("Failed to load persona");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleApplySettings = () => {
    const values = form.getValues();
    onApplySettings(values);
    toast.success("Host persona applied to active game");
  };
  
  return (
    <div className="space-y-4">
      <div className="mb-2">
        <h4 className="font-medium text-white mb-2">Quick Templates</h4>
        <div className="flex gap-2">
          <Button 
            onClick={() => handleApplyTemplate("flirty")} 
            size="sm"
            variant="outline"
            className="bg-tnt-crimson/20 hover:bg-tnt-crimson/40 border-tnt-crimson/30 text-tnt-pink"
          >
            Flirty
          </Button>
          <Button 
            onClick={() => handleApplyTemplate("sassy")} 
            size="sm"
            variant="outline"
            className="bg-purple-700/20 hover:bg-purple-700/40 border-purple-600/30 text-purple-400"
          >
            Sassy
          </Button>
          <Button 
            onClick={() => handleApplyTemplate("party")} 
            size="sm"
            variant="outline"
            className="bg-tnt-gold/20 hover:bg-tnt-gold/40 border-tnt-gold/30 text-tnt-gold"
          >
            Party Animal
          </Button>
        </div>
      </div>
      
      <Form {...form}>
        <form className="space-y-4">
          <FormField
            control={form.control}
            name="aiPersona"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">Host Persona</FormLabel>
                <FormDescription className="text-gray-400">
                  Define the personality and tone of your AI host
                </FormDescription>
                <FormControl>
                  <Textarea
                    {...field}
                    className="min-h-32 bg-gray-800 border-gray-700 text-white"
                    placeholder="You are a sassy, flirty host for a wild party game..."
                  />
                </FormControl>
              </FormItem>
            )}
          />
          
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="responseLength"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Response Style</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                        <SelectValue placeholder="Select length" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-gray-800 border-gray-700 text-white">
                      <SelectItem value="short" className="focus:bg-tnt-pink/30 focus:text-white">Short & Punchy</SelectItem>
                      <SelectItem value="medium" className="focus:bg-tnt-pink/30 focus:text-white">Balanced</SelectItem>
                      <SelectItem value="long" className="focus:bg-tnt-pink/30 focus:text-white">Elaborate</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="escalationSpeed"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Escalation Speed</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                        <SelectValue placeholder="Select speed" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-gray-800 border-gray-700 text-white">
                      <SelectItem value="slow" className="focus:bg-tnt-pink/30 focus:text-white">Slow Build</SelectItem>
                      <SelectItem value="normal" className="focus:bg-tnt-pink/30 focus:text-white">Normal</SelectItem>
                      <SelectItem value="fast" className="focus:bg-tnt-pink/30 focus:text-white">Fast & Wild</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>
          
          <div className="pt-2">
            <Button
              type="button"
              onClick={handleApplySettings}
              className="w-full bg-tnt-gold hover:bg-tnt-gold/80 text-tnt-charcoal"
              disabled={!gameId || isLoading}
            >
              <Check size={16} className="mr-2" />
              Apply Host Persona
            </Button>
          </div>
        </form>
      </Form>
      
      <div className="border-t border-gray-700 pt-4 mt-4">
        <h4 className="font-medium text-white mb-2">Save/Load Personas</h4>
        
        <div className="flex gap-2 mb-4">
          <Input
            placeholder="Persona name" 
            value={newPersonaName}
            onChange={(e) => setNewPersonaName(e.target.value)}
            className="flex-1 bg-gray-800 border-gray-700 text-white"
          />
          <Button
            onClick={handleSavePersona}
            disabled={isLoading || !newPersonaName.trim()}
            className="flex-shrink-0 bg-tnt-pink hover:bg-tnt-crimson text-white"
          >
            <Save size={16} className="mr-1" />
            Save
          </Button>
        </div>
        
        <div className="mt-2">
          <Label htmlFor="loadPersona" className="text-sm mb-1 block text-white">Load Saved Persona</Label>
          <Select onValueChange={(value) => handleLoadPersona(value)}>
            <SelectTrigger className="w-full bg-gray-800 border-gray-700 text-white">
              <SelectValue placeholder="Select a persona" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700 text-white">
              {savedPersonas.map((persona) => (
                <SelectItem key={persona.id} value={persona.id} className="focus:bg-tnt-pink/30 focus:text-white">{persona.name}</SelectItem>
              ))}
              {savedPersonas.length === 0 && (
                <div className="px-2 py-4 text-gray-400 text-center">No saved personas</div>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default HostPersonaEditor;
