
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface ResultsDisplayProps {
  roundNumber: number;
  winner: {
    player: string;
    card: string;
  };
  onStartNextRound: () => void;
}

const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
  roundN<PERSON><PERSON>,
  winner,
  onStartNextRound
}) => {
  return (
    <div className="flex flex-col items-center">
      <h2 className="text-3xl font-poppins font-bold mb-2">Round {roundNumber} Results</h2>
      <p className="text-lg mb-4">And the winner is...</p>
      
      <div className="bg-tnt-gold/20 backdrop-blur-sm rounded-lg p-6 shadow-xl border-2 border-tnt-gold w-full max-w-lg mb-8 text-center">
        <h3 className="text-2xl font-poppins font-bold text-tnt-gold mb-2">{winner.player}</h3>
        <p className="text-lg italic mb-4">"{winner.card}"</p>
        <div className="flex justify-center">
          <div className="px-4 py-2 bg-tnt-gold text-tnt-charcoal font-bold rounded-full inline-block">
            +1 Point
          </div>
        </div>
      </div>
      
      <Button
        onClick={onStartNextRound}
        variant="tntPrimary"
        className="font-poppins text-xl px-8 py-6 rounded-lg shadow-lg flex items-center"
      >
        Next Round <ArrowRight className="ml-2" />
      </Button>
    </div>
  );
};

export default ResultsDisplay;
