
import React from 'react';
import TntLogo from "@/components/TntLogo";
import { Button } from "@/components/ui/button";
import { Clipboard } from "lucide-react";
import { toast } from "sonner";

interface GameHeaderProps {
  hostName: string;
  gameCode: string;
}

const GameHeader: React.FC<GameHeaderProps> = ({ hostName, gameCode }) => {
  const copyGameCode = () => {
    navigator.clipboard.writeText(gameCode);
    toast.success("Game code copied to clipboard!");
  };

  return (
    <header className="bg-gradient-to-r from-tnt-charcoal to-black py-2 border-b border-tnt-pink/30 relative">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <TntLogo size="sm" />
        <div className="flex items-center">
          <div className="text-right mr-4">
            <p className="text-sm text-tnt-ivory">Hosting as <span className="font-bold text-tnt-gold">{hostName}</span></p>
          </div>
          <div className="inline-flex items-center bg-black/50 rounded-full pl-2 pr-1 border border-tnt-pink/40">
            <p className="text-sm text-tnt-pink mr-1">Game Code: <span className="font-bold">{gameCode}</span></p>
            <Button 
              variant="tntCopy" 
              onClick={copyGameCode}
              className="rounded-full flex items-center gap-1"
            >
              <Clipboard size={12} />
              Copy
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default GameHeader;
