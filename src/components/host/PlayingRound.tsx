
import React from 'react';
import GameCard from "@/components/GameCard";

interface PlayingRoundProps {
  roundNumber: number;
  currentPrompt: string;
  players: string[];
}

const PlayingRound: React.FC<PlayingRoundProps> = ({
  roundNumber,
  currentPrompt,
  players
}) => {
  return (
    <div className="flex flex-col items-center">
      <h2 className="text-3xl font-poppins font-bold mb-2">Round {roundNumber}</h2>
      <p className="text-lg mb-8">Players are choosing their cards...</p>
      
      <div className="w-full max-w-3xl mb-8">
        <GameCard 
          type="black" 
          content={currentPrompt}
          className="w-full mx-auto max-w-lg"
        />
      </div>
      
      <div className="w-full max-w-lg">
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-gray-700 mb-6">
          <h3 className="text-xl font-poppins mb-4">Waiting for submissions:</h3>
          <div className="space-y-2">
            {players.map((player, index) => (
              <div key={index} className="flex justify-between items-center">
                <span>{player}</span>
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded-full border-2 border-gray-500 mr-2"></div>
                  <span className="text-gray-400">Choosing card...</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="w-16 h-16 border-t-4 border-tnt-pink rounded-full animate-spin mt-6"></div>
    </div>
  );
};

export default PlayingRound;
