
import React from 'react';
import GameCard from "@/components/GameCard";
import { Button } from "@/components/ui/button";

interface Submission {
  player: string;
  card: string;
}

interface JudgingPhaseProps {
  roundNumber: number;
  currentPrompt: string;
  submissions: Submission[];
  onSelectWinner: (submission: Submission) => void;
}

const JudgingPhase: React.FC<JudgingPhaseProps> = ({
  roundNumber,
  currentPrompt,
  submissions,
  onSelectWinner
}) => {
  return (
    <div className="flex flex-col items-center">
      <h2 className="text-3xl font-poppins font-bold mb-2">Round {roundNumber}</h2>
      <p className="text-lg mb-8">Select the winner for this round!</p>
      
      <div className="w-full max-w-3xl mb-8">
        <GameCard 
          type="black" 
          content={currentPrompt}
          className="w-full mx-auto max-w-lg"
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-4xl">
        {submissions.map((submission, index) => (
          <div key={index} className="flex flex-col items-center">
            <GameCard 
              type="white" 
              content={submission.card}
              onClick={() => onSelectWinner(submission)}
            />
            <Button 
              variant="tntOutline"
              onClick={() => onSelectWinner(submission)}
              className="mt-3"
            >
              Select as Winner
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default JudgingPhase;
