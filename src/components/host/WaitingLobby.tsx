
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import QRCodeJoin from "@/components/QRCodeJoin";
import { toast } from "sonner";

interface WaitingLobbyProps {
  gameCode: string;
  players: string[];
  isPrivateGame: boolean;
  onTogglePrivateGame: () => void;
  onStartGame: () => void;
}

const WaitingLobby: React.FC<WaitingLobbyProps> = ({
  gameCode,
  players,
  isPrivateGame,
  onTogglePrivateGame,
  onStartGame
}) => {
  // In dev mode, allow starting with just the host for testing
  const isDevMode = process.env.NODE_ENV === 'development' || localStorage.getItem('devMode') === 'true';
  const minPlayers = isDevMode ? 1 : 2;

  return (
    <div className="flex flex-col items-center mt-4">
      <h2 className="text-4xl font-poppins font-bold mb-6 text-white">Waiting for Players</h2>
      <p className="text-lg mb-4 text-tnt-ivory">Share the game code or QR code with your friends to join!</p>
      
      <div className="flex justify-center mb-6">
        <QRCodeJoin gameCode={gameCode} />
      </div>
      
      <div className="bg-gray-900/70 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-tnt-pink/30 w-full max-w-lg mb-8">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-poppins text-white">Players in lobby: {players.length}</h3>
          
          <Button
            variant="tntOutline"
            onClick={onTogglePrivateGame}
            className={`text-sm ${isPrivateGame ? "bg-tnt-pink text-white hover:bg-tnt-crimson" : ""}`}
          >
            {isPrivateGame ? "Private Mode: ON" : "Private Mode: OFF"}
          </Button>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          {players.map((player, index) => (
            <div key={index} className="bg-gray-800/80 p-3 rounded-lg flex items-center justify-between border border-gray-700/50">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-tnt-pink mr-3"></div>
                <span className="text-white">{player}</span>
              </div>
            </div>
          ))}
          {players.length === 0 && (
            <div className="col-span-2 text-center py-6 text-gray-400">
              No players have joined yet...
            </div>
          )}
        </div>
      </div>
      
      <Button
        onClick={onStartGame}
        disabled={players.length < minPlayers}
        variant="tntPrimary"
        className="font-poppins text-xl px-8 py-6 rounded-lg shadow-lg w-full max-w-md"
      >
        Start Game
      </Button>
      {players.length < minPlayers && (
        <p className="text-sm text-gray-400 mt-2">
          Need at least {minPlayers} player{minPlayers > 1 ? 's' : ''} to start
          {isDevMode && <span className="text-yellow-400 ml-2">(Dev Mode)</span>}
        </p>
      )}
    </div>
  );
};

export default WaitingLobby;
