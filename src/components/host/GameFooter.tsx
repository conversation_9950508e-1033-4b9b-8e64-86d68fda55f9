
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";

interface GameFooterProps {
  players: string[];
  onEndGame: () => void;
}

const GameFooter: React.FC<GameFooterProps> = ({ players, onEndGame }) => {
  return (
    <footer className="bg-gradient-to-r from-black to-tnt-charcoal py-4 border-t border-tnt-pink/30">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Button 
          variant="tntOutline"
          onClick={onEndGame}
          className="bg-black/50"
        >
          End Game
        </Button>
        
        <div>
          <p className="text-sm text-tnt-ivory/80">
            {players.length} {players.length === 1 ? 'player' : 'players'} connected
          </p>
        </div>
      </div>
    </footer>
  );
};

export default GameFooter;
