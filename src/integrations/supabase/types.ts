export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_logs: {
        Row: {
          action_details: Json | null
          action_type: string
          admin_id: string | null
          created_at: string
          id: string
        }
        Insert: {
          action_details?: Json | null
          action_type: string
          admin_id?: string | null
          created_at?: string
          id?: string
        }
        Update: {
          action_details?: Json | null
          action_type?: string
          admin_id?: string | null
          created_at?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_logs_admin_id_fkey"
            columns: ["admin_id"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_presets: {
        Row: {
          created_at: string
          host_profile_id: string | null
          id: string
          preset_name: string
          settings_json: Json
        }
        Insert: {
          created_at?: string
          host_profile_id?: string | null
          id?: string
          preset_name: string
          settings_json: Json
        }
        Update: {
          created_at?: string
          host_profile_id?: string | null
          id?: string
          preset_name?: string
          settings_json?: Json
        }
        Relationships: []
      }
      admin_roles: {
        Row: {
          description: string | null
          id: string
          role_name: string
        }
        Insert: {
          description?: string | null
          id?: string
          role_name: string
        }
        Update: {
          description?: string | null
          id?: string
          role_name?: string
        }
        Relationships: []
      }
      admin_users: {
        Row: {
          created_at: string
          id: string
          password_hash: string
          role_id: string | null
          updated_at: string
          username: string
        }
        Insert: {
          created_at?: string
          id?: string
          password_hash: string
          role_id?: string | null
          updated_at?: string
          username: string
        }
        Update: {
          created_at?: string
          id?: string
          password_hash?: string
          role_id?: string | null
          updated_at?: string
          username?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_users_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "admin_roles"
            referencedColumns: ["id"]
          },
        ]
      }
      black_cards: {
        Row: {
          card_text: string
          category_id: string | null
          created_at: string
          id: string
          updated_at: string
        }
        Insert: {
          card_text: string
          category_id?: string | null
          created_at?: string
          id?: string
          updated_at?: string
        }
        Update: {
          card_text?: string
          category_id?: string | null
          created_at?: string
          id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "black_cards_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "card_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      card_categories: {
        Row: {
          description: string | null
          id: string
          name: string
        }
        Insert: {
          description?: string | null
          id?: string
          name: string
        }
        Update: {
          description?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      custom_card_packs: {
        Row: {
          black_cards: Json
          created_at: string
          creator_id: string
          description: string | null
          id: string
          is_public: boolean
          name: string
          updated_at: string
          white_cards: Json
        }
        Insert: {
          black_cards?: Json
          created_at?: string
          creator_id: string
          description?: string | null
          id?: string
          is_public?: boolean
          name: string
          updated_at?: string
          white_cards?: Json
        }
        Update: {
          black_cards?: Json
          created_at?: string
          creator_id?: string
          description?: string | null
          id?: string
          is_public?: boolean
          name?: string
          updated_at?: string
          white_cards?: Json
        }
        Relationships: []
      }
      game_enabled_modes: {
        Row: {
          game_id: string | null
          game_mode_id: string | null
          id: string
        }
        Insert: {
          game_id?: string | null
          game_mode_id?: string | null
          id?: string
        }
        Update: {
          game_id?: string | null
          game_mode_id?: string | null
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "game_enabled_modes_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_enabled_modes_game_mode_id_fkey"
            columns: ["game_mode_id"]
            isOneToOne: false
            referencedRelation: "game_modes"
            referencedColumns: ["id"]
          },
        ]
      }
      game_modes: {
        Row: {
          description: string | null
          id: string
          is_active: boolean | null
          name: string
        }
        Insert: {
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
        }
        Update: {
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
        }
        Relationships: []
      }
      games: {
        Row: {
          ai_judging: boolean | null
          created_at: string
          current_round: number | null
          game_code: string
          host_id: string | null
          id: string
          judging_timer: number | null
          max_players: number | null
          max_rounds: number | null
          max_score: number | null
          settings: Json | null
          status: string | null
          submission_timer: number | null
          updated_at: string
        }
        Insert: {
          ai_judging?: boolean | null
          created_at?: string
          current_round?: number | null
          game_code: string
          host_id?: string | null
          id?: string
          judging_timer?: number | null
          max_players?: number | null
          max_rounds?: number | null
          max_score?: number | null
          settings?: Json | null
          status?: string | null
          submission_timer?: number | null
          updated_at?: string
        }
        Update: {
          ai_judging?: boolean | null
          created_at?: string
          current_round?: number | null
          game_code?: string
          host_id?: string | null
          id?: string
          judging_timer?: number | null
          max_players?: number | null
          max_rounds?: number | null
          max_score?: number | null
          settings?: Json | null
          status?: string | null
          submission_timer?: number | null
          updated_at?: string
        }
        Relationships: []
      }
      player_cards: {
        Row: {
          id: string
          player_id: string | null
          round_used: number | null
          white_card_id: string | null
        }
        Insert: {
          id?: string
          player_id?: string | null
          round_used?: number | null
          white_card_id?: string | null
        }
        Update: {
          id?: string
          player_id?: string | null
          round_used?: number | null
          white_card_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_cards_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_cards_white_card_id_fkey"
            columns: ["white_card_id"]
            isOneToOne: false
            referencedRelation: "white_cards"
            referencedColumns: ["id"]
          },
        ]
      }
      players: {
        Row: {
          created_at: string
          device_token: string | null
          game_id: string | null
          id: string
          is_connected: boolean | null
          is_host: boolean | null
          is_saboteur: boolean | null
          player_name: string
          score: number | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          device_token?: string | null
          game_id?: string | null
          id?: string
          is_connected?: boolean | null
          is_host?: boolean | null
          is_saboteur?: boolean | null
          player_name: string
          score?: number | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          device_token?: string | null
          game_id?: string | null
          id?: string
          is_connected?: boolean | null
          is_host?: boolean | null
          is_saboteur?: boolean | null
          player_name?: string
          score?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "players_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
        ]
      }
      round_submissions: {
        Row: {
          black_card_id: string | null
          created_at: string
          game_id: string | null
          id: string
          is_winner: boolean | null
          player_id: string | null
          round_number: number
          white_card_id: string | null
          wildcard_id: string | null
        }
        Insert: {
          black_card_id?: string | null
          created_at?: string
          game_id?: string | null
          id?: string
          is_winner?: boolean | null
          player_id?: string | null
          round_number: number
          white_card_id?: string | null
          wildcard_id?: string | null
        }
        Update: {
          black_card_id?: string | null
          created_at?: string
          game_id?: string | null
          id?: string
          is_winner?: boolean | null
          player_id?: string | null
          round_number?: number
          white_card_id?: string | null
          wildcard_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "round_submissions_black_card_id_fkey"
            columns: ["black_card_id"]
            isOneToOne: false
            referencedRelation: "black_cards"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "round_submissions_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "round_submissions_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "round_submissions_white_card_id_fkey"
            columns: ["white_card_id"]
            isOneToOne: false
            referencedRelation: "white_cards"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "round_submissions_wildcard_id_fkey"
            columns: ["wildcard_id"]
            isOneToOne: false
            referencedRelation: "wildcards"
            referencedColumns: ["id"]
          },
        ]
      }
      white_cards: {
        Row: {
          card_text: string
          category_id: string | null
          created_at: string
          id: string
          updated_at: string
        }
        Insert: {
          card_text: string
          category_id?: string | null
          created_at?: string
          id?: string
          updated_at?: string
        }
        Update: {
          card_text?: string
          category_id?: string | null
          created_at?: string
          id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "white_cards_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "card_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      wildcards: {
        Row: {
          card_text: string
          category_id: string | null
          created_at: string
          effect_type: string
          id: string
          updated_at: string
        }
        Insert: {
          card_text: string
          category_id?: string | null
          created_at?: string
          effect_type: string
          id?: string
          updated_at?: string
        }
        Update: {
          card_text?: string
          category_id?: string | null
          created_at?: string
          effect_type?: string
          id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "wildcards_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "card_categories"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
