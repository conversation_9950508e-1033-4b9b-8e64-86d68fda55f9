// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://uayivasmxhelynrkekgg.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVheWl2YXNteGhlbHlucmtla2dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4NzUyMTcsImV4cCI6MjA2MTQ1MTIxN30.hk78RkL55Y6ktbZwgylz1aNJCGQZ8coPJgzS7VesVsk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);