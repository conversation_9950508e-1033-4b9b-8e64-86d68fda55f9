
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Gamepad, LibrarySquare, Package } from 'lucide-react';
import { useGameMode } from '@/contexts/GameModeContext';
import TntLogo from '@/components/TntLogo';

// Game modes data - extensible for future modes
const gameModes = [
  {
    id: 'tnt',
    name: 'Tempted \'n Twisted',
    description: 'Classic party card game with player-selected packs and fun judging.',
    icon: LibrarySquare,
    path: '/modules/TnT',
    gradient: 'bg-tnt-gradient',
    color: 'text-tnt-crimson'
  },
  {
    id: 'sinfulSecrets',
    name: 'Sinful Secrets',
    description: 'Spicy escalating intimacy game with Truth, Dare, Would You Rather, and Wildcards.',
    icon: Gamepad,
    path: '/modules/SinfulSecrets',
    gradient: 'bg-secrets-gradient',
    color: 'text-tnt-hotPink'
  }
  // Add more game modes here in the future
];

const ModeSelector: React.FC = () => {
  const navigate = useNavigate();
  const { setGameMode } = useGameMode();

  const handleModeSelect = (modeId: 'tnt' | 'sinfulSecrets') => {
    setGameMode(modeId);
    const selectedMode = gameModes.find(mode => mode.id === modeId);
    if (selectedMode) {
      navigate(selectedMode.path);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-white relative">
      {/* Background effects */}
      <div className="absolute inset-0 opacity-20 z-0 bg-[radial-gradient(circle_at_center,rgba(236,72,153,0.15)_0%,transparent_70%)]"></div>
      
      <div className="container mx-auto px-4 py-12 flex flex-col items-center justify-center flex-grow z-10">
        <div className="text-center mb-12">
          <TntLogo size="lg" className="animate-float mb-6" />
          <h1 className="text-4xl md:text-5xl font-pacifico mb-4 text-tnt-whiteSmoke">
            Choose Your Adventure
          </h1>
          <p className="text-lg font-poppins text-tnt-whiteSmoke/80 max-w-lg mx-auto">
            Select how you want to play tonight. Each mode offers a unique experience!
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl w-full">
          {gameModes.map((mode) => (
            <motion.div
              key={mode.id}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
              className={`rounded-xl overflow-hidden cursor-pointer shadow-xl transition-all duration-300 group h-full`}
              onClick={() => handleModeSelect(mode.id as 'tnt' | 'sinfulSecrets')}
            >
              <div className={`${mode.gradient} p-8 h-full flex flex-col`}>
                <div className="flex items-center mb-4">
                  <div className="p-3 bg-white/10 backdrop-blur-sm rounded-full">
                    <mode.icon size={32} className="text-white" />
                  </div>
                  <h2 className="text-2xl md:text-3xl font-pacifico ml-4 text-white">
                    {mode.name}
                  </h2>
                </div>
                
                <p className="text-white/90 font-poppins mb-6 flex-grow">
                  {mode.description}
                </p>
                
                <div className="flex justify-between items-center mt-auto">
                  <div className="flex items-center text-lg font-semibold text-white bg-white/20 px-4 py-2 rounded-lg backdrop-blur-sm">
                    <Package className="mr-2 h-5 w-5" />
                    <span>Select Mode</span>
                  </div>
                  <div className="hidden group-hover:block animate-bounce-subtle">
                    <span className="mr-2">→</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <p className="text-sm text-tnt-whiteSmoke/60 font-poppins">
            All games are for adults 18+ only. Play responsibly.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ModeSelector;
