
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGameMode } from '@/contexts/GameModeContext';
import SinfulSecretsGameOrchestrator from './components/SinfulSecretsGameOrchestrator';


const SinfulSecretsModule: React.FC = () => {
  const navigate = useNavigate();
  const { gameMode } = useGameMode();
  
  useEffect(() => {
    // Check if there's an active Sinful Secrets session
    const hasActiveSession = sessionStorage.getItem('sinfulSecretsGameCode') ||
                             sessionStorage.getItem('sinfulSecretsHostName');
    const isTestMode = localStorage.getItem('sinfulSecretsTestMode') === 'true';

    // If there's an active session, set the game mode and don't redirect
    if (hasActiveSession && !gameMode) {
      // Don't redirect, let the orchestrator handle the session
      return;
    }

    // Only redirect if no active session and no game mode selected
    if (!gameMode && !isTestMode && !hasActiveSession) {
      navigate('/select-mode');
    } else if (gameMode && gameMode !== 'sinfulSecrets' && !hasActiveSession) {
      navigate('/select-mode');
    }
  }, [gameMode, navigate]);

  // Allow test mode or active session to bypass game mode requirement
  const isTestMode = localStorage.getItem('sinfulSecretsTestMode') === 'true';
  const hasActiveSession = sessionStorage.getItem('sinfulSecretsGameCode') ||
                           sessionStorage.getItem('sinfulSecretsHostName');

  if (!isTestMode && !hasActiveSession && gameMode !== 'sinfulSecrets') {
    return null;
  }

  return (
    <SinfulSecretsGameOrchestrator />
  );
};

export default SinfulSecretsModule;
