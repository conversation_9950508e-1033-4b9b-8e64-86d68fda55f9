
import { PromptCategory, IntensityLevel, GameSubset, Prompt } from '../types/gameTypes';

const createPrompt = (
  id: string,
  text: string,
  category: PromptCategory,
  intensity: IntensityLevel,
  subset: GameSubset
): Prompt => ({
  id,
  text,
  category,
  intensity,
  subset
});

// Truth prompts by intensity
const truthPrompts: Prompt[] = [
  // Playful (Subset A)
  createPrompt('truth-playful-1', "What's your funniest or most embarrassing date story?", 'Truth', 'Playful', 'A'),
  createPrompt('truth-playful-2', "What's your most ridiculous fear or phobia?", 'Truth', 'Playful', 'A'),
  createPrompt('truth-playful-3', "What's the weirdest thing you've ever eaten?", 'Truth', 'Playful', 'A'),
  createPrompt('truth-playful-4', "What's your go-to karaoke song?", 'Truth', 'Playful', 'A'),
  
  // Sensual (Subset B)
  createPrompt('truth-sensual-1', "What's the wildest place you've kissed someone?", 'Truth', 'Sensual', 'B'),
  createPrompt('truth-sensual-2', "Describe your ideal romantic getaway.", 'Truth', 'Sensual', 'B'),
  createPrompt('truth-sensual-3', "What's the most romantic thing someone has done for you?", 'Truth', 'Sensual', 'B'),
  createPrompt('truth-sensual-4', "What's your favorite thing about physical intimacy?", 'Truth', 'Sensual', 'B'),
  
  // Daring (Subset C)
  createPrompt('truth-daring-1', "What's the most risqué text message you've ever sent?", 'Truth', 'Daring', 'C'),
  createPrompt('truth-daring-2', "If you could try any kink or fantasy, what would it be?", 'Truth', 'Daring', 'C'),
  createPrompt('truth-daring-3', "What's your biggest turn-on that might surprise people?", 'Truth', 'Daring', 'C'),
  createPrompt('truth-daring-4', "What's the naughtiest thing you've done in public?", 'Truth', 'Daring', 'C'),
  
  // Bold (Subset D)
  createPrompt('truth-bold-1', "What's a fantasy you've never shared with anyone before?", 'Truth', 'Bold', 'D'),
  createPrompt('truth-bold-2', "If you had to choose one player for a romantic or daring adventure, who would it be and why?", 'Truth', 'Bold', 'D'),
  createPrompt('truth-bold-3', "What's the most intimate secret you're willing to share right now?", 'Truth', 'Bold', 'D'),
  createPrompt('truth-bold-4', "What's your wildest sexual fantasy?", 'Truth', 'Bold', 'D')
];

// Dare prompts by intensity
const darePrompts: Prompt[] = [
  // Playful (Subset A)
  createPrompt('dare-playful-1', "Show us your best dance move for 30 seconds!", 'Dare', 'Playful', 'A'),
  createPrompt('dare-playful-2', "Make the funniest face you can and hold it for 10 seconds.", 'Dare', 'Playful', 'A'),
  createPrompt('dare-playful-3', "Sing happy birthday in the most dramatic way possible.", 'Dare', 'Playful', 'A'),
  createPrompt('dare-playful-4', "Do your best impression of someone in the room.", 'Dare', 'Playful', 'A'),
  
  // Sensual (Subset B)
  createPrompt('dare-sensual-1', "Whisper something flirty in the ear of the person to your right.", 'Dare', 'Sensual', 'B'),
  createPrompt('dare-sensual-2', "Draw a small heart or star on someone's arm using your finger.", 'Dare', 'Sensual', 'B'),
  createPrompt('dare-sensual-3', "Give someone a 30-second shoulder massage.", 'Dare', 'Sensual', 'B'),
  createPrompt('dare-sensual-4', "Look into someone's eyes for 30 seconds without breaking contact.", 'Dare', 'Sensual', 'B'),
  
  // Daring (Subset C)
  createPrompt('dare-daring-1', "Let someone trace the outline of your lips with their finger.", 'Dare', 'Daring', 'C'),
  createPrompt('dare-daring-2', "Hold eye contact with the person to your left for 30 seconds without breaking.", 'Dare', 'Daring', 'C'),
  createPrompt('dare-daring-3', "Let someone feed you something sensually.", 'Dare', 'Daring', 'C'),
  createPrompt('dare-daring-4', "Give someone a kiss on the cheek in the most seductive way possible.", 'Dare', 'Daring', 'C'),
  
  // Bold (Subset D)
  createPrompt('dare-bold-1', "Allow someone to write a message or draw something on your body with a marker.", 'Dare', 'Bold', 'D'),
  createPrompt('dare-bold-2', "Kiss the person to your right on the cheek—or take two penalty drinks.", 'Dare', 'Bold', 'D'),
  createPrompt('dare-bold-3', "Let someone blindfold you and guess who's touching your hand.", 'Dare', 'Bold', 'D'),
  createPrompt('dare-bold-4', "Perform a sensual dance for 30 seconds.", 'Dare', 'Bold', 'D')
];

// Would You Rather prompts by intensity
const wouldYouRatherPrompts: Prompt[] = [
  // Playful (Subset A)
  createPrompt('wyr-playful-1', "Would you rather only eat pizza for the rest of your life or never eat it again?", 'WouldYouRather', 'Playful', 'A'),
  createPrompt('wyr-playful-2', "Would you rather have to sing every word you say or dance every step you take?", 'WouldYouRather', 'Playful', 'A'),
  createPrompt('wyr-playful-3', "Would you rather wear pajamas to work or a fancy suit to bed?", 'WouldYouRather', 'Playful', 'A'),
  
  // Sensual (Subset B)
  createPrompt('wyr-sensual-1', "Would you rather have your partner use ice or a feather during an intimate moment?", 'WouldYouRather', 'Sensual', 'B'),
  createPrompt('wyr-sensual-2', "Would you rather receive a romantic poem or a sensual massage?", 'WouldYouRather', 'Sensual', 'B'),
  createPrompt('wyr-sensual-3', "Would you rather be kissed on your neck or whispered to in your ear?", 'WouldYouRather', 'Sensual', 'B'),
  
  // Daring (Subset C)
  createPrompt('wyr-daring-1', "Would you rather be tied up or blindfolded during an intimate moment?", 'WouldYouRather', 'Daring', 'C'),
  createPrompt('wyr-daring-2', "Would you rather share a deep secret or act out a flirtatious scene?", 'WouldYouRather', 'Daring', 'C'),
  createPrompt('wyr-daring-3', "Would you rather be dominant or submissive for an entire night?", 'WouldYouRather', 'Daring', 'C'),
  
  // Bold (Subset D)
  createPrompt('wyr-bold-1', "Would you rather tease your partner all night or have them tease you?", 'WouldYouRather', 'Bold', 'D'),
  createPrompt('wyr-bold-2', "Would you rather have a daring secret revealed or perform a bold task for the group?", 'WouldYouRather', 'Bold', 'D'),
  createPrompt('wyr-bold-3', "Would you rather share your most intimate fantasy or act it out?", 'WouldYouRather', 'Bold', 'D')
];

// Wildcard prompts by intensity
const wildcardPrompts: Prompt[] = [
  // Playful (Subset A)
  createPrompt('wildcard-playful-1', "Everyone shares a fun or awkward childhood memory.", 'Wildcard', 'Playful', 'A'),
  createPrompt('wildcard-playful-2', "Trade an accessory (hat, watch, etc.) with the person to your left.", 'Wildcard', 'Playful', 'A'),
  createPrompt('wildcard-playful-3', "Each player shares their most embarrassing moment in 10 words or less.", 'Wildcard', 'Playful', 'A'),
  
  // Sensual (Subset B)
  createPrompt('wildcard-sensual-1', "Each player gives a sincere compliment to the person sitting across from them.", 'Wildcard', 'Sensual', 'B'),
  createPrompt('wildcard-sensual-2', "Share one quality you find most attractive in someone in the room.", 'Wildcard', 'Sensual', 'B'),
  createPrompt('wildcard-sensual-3', "Everyone shares their idea of the perfect date.", 'Wildcard', 'Sensual', 'B'),
  
  // Daring (Subset C)
  createPrompt('wildcard-daring-1', "Play a lightning round of 'Never Have I Ever.' First player to lose all fingers takes a penalty drink or performs a bold dare.", 'Wildcard', 'Daring', 'C'),
  createPrompt('wildcard-daring-2', "Pair up and act out a 10-second seductive skit.", 'Wildcard', 'Daring', 'C'),
  createPrompt('wildcard-daring-3', "Everyone gives their best seductive look to someone in the group.", 'Wildcard', 'Daring', 'C'),
  
  // Bold (Subset D)
  createPrompt('wildcard-bold-1', "Each player anonymously writes their most daring secret. The host reads them aloud, and the group guesses who wrote what.", 'Wildcard', 'Bold', 'D'),
  createPrompt('wildcard-bold-2', "Create and perform a daring group pose or photo (safe and consensual, of course!).", 'Wildcard', 'Bold', 'D'),
  createPrompt('wildcard-bold-3', "Let the group vote on the most daring person, who must then perform a bold task.", 'Wildcard', 'Bold', 'D')
];

// Export organized prompts
export const prompts: Record<PromptCategory, Prompt[]> = {
  Truth: truthPrompts,
  Dare: darePrompts,
  WouldYouRather: wouldYouRatherPrompts,
  Wildcard: wildcardPrompts
};
