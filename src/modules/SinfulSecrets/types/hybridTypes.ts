
import { GameState, Player, Prompt, PromptCategory } from './gameTypes';

export interface CardPrompt {
  id: string;
  text: string;
  isNSFW: boolean;
  category: 'romantic' | 'playful' | 'daring' | 'wild';
}

export interface HybridGameState extends GameState {
  cardModeActive: boolean;
  currentCardPrompt: CardPrompt | null;
  cardDeck: CardPrompt[];
  usedCards: string[];
}

export interface HybridPromptResponse {
  type: 'traditional' | 'card';
  prompt?: Prompt;
  card?: CardPrompt;
  selectedNumber?: number;
  category?: PromptCategory;
  requiresDrink: boolean;
}

export interface TestGameState {
  isTestMode: boolean;
  testGameId: string;
  quickRestart: boolean;
}
