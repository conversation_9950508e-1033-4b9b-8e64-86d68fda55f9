export type PromptCategory = 'Truth' | 'Dare' | 'WouldYouRather' | 'Wildcard';
export type IntensityLevel = 'Playful' | 'Sensual' | 'Daring' | 'Bold';
export type GameSubset = 'A' | 'B' | 'C' | 'D';

export interface Player {
  id: string;
  name: string;
  gender?: 'M' | 'F' | 'Other';
  role?: string;
  hasPlayed: boolean;
  penaltyDrinks: number;
  safeWordUsage: number;
  score?: number; // Added score property for card game
}

export interface Prompt {
  id: string;
  text: string;
  category: PromptCategory;
  intensity: IntensityLevel;
  subset: GameSubset;
  isUsed?: boolean;
  personalizedText?: string;
}

export interface GameState {
  round: number;
  subset: GameSubset;
  intensityLevel: IntensityLevel;
  currentPrompt: Prompt | null;
  activePlayer: Player | null;
  players: Player[];
  safeWord: string;
  isOverdriveMode: boolean;
  overdriveChance: number;
  usedPrompts: string[];
  usedNumbers: number[];
  numberMapping: Record<number, PromptCategory>;
  neverHaveIEverCounter: number;
  gameStarted: boolean;
}

export interface PromptResponse {
  prompt: Prompt;
  selectedNumber: number;
  category: PromptCategory;
  requiresDrink?: boolean;
}
