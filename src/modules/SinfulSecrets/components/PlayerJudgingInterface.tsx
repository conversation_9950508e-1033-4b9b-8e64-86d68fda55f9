import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import GameCard from '@/components/GameCard';

interface PlayerJudgingInterfaceProps {
  currentPrompt: string;
  submissions: Array<{ 
    id: string;
    player_id: string; 
    card_text: string; 
    is_deck_card?: boolean;
    player_name?: string;
  }>;
  onSelectWinner: (submissionId: string) => void;
  isJudge: boolean;
  judgeName?: string;
}

const PlayerJudgingInterface: React.FC<PlayerJudgingInterfaceProps> = ({
  currentPrompt,
  submissions,
  onSelectWinner,
  isJudge,
  judgeName
}) => {
  const [selectedCard, setSelectedCard] = useState<string | null>(null);

  const handleCardSelect = (submissionId: string) => {
    if (!isJudge) return;
    setSelectedCard(submissionId);
  };

  const handleConfirmSelection = () => {
    if (selectedCard && isJudge) {
      onSelectWinner(selectedCard);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-6">
      <div className="max-w-6xl mx-auto">
        
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-pacifico text-tnt-hotPink mb-4">Card Judging</h1>
          
          {/* Current Prompt */}
          <div className="bg-tnt-deepPurple/20 rounded-lg p-4 mb-6">
            <h2 className="text-xl font-bold text-tnt-hotPink mb-2">Black Card:</h2>
            <div className="max-w-md mx-auto">
              <GameCard 
                type="black" 
                content={currentPrompt}
                className="w-full"
              />
            </div>
          </div>

          {/* Judge Status */}
          {isJudge ? (
            <div className="bg-tnt-gold/20 rounded-lg p-4 mb-6">
              <h3 className="text-xl font-bold text-tnt-gold mb-2">👑 You are the Judge!</h3>
              <p className="text-tnt-whiteSmoke">Choose the best white card that answers the black card prompt</p>
            </div>
          ) : (
            <div className="bg-tnt-deepPurple/20 rounded-lg p-4 mb-6">
              <h3 className="text-xl font-bold text-tnt-hotPink mb-2">Waiting for Judge</h3>
              <p className="text-tnt-whiteSmoke">{judgeName} is choosing the winner...</p>
            </div>
          )}
        </div>

        {/* Submitted Cards */}
        {submissions.length > 0 && (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-center text-tnt-hotPink">
              Submitted Cards
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {submissions.map((submission) => (
                <div 
                  key={submission.id} 
                  className={`space-y-3 ${isJudge ? 'cursor-pointer' : ''}`}
                  onClick={() => handleCardSelect(submission.id)}
                >
                  <GameCard 
                    type="white" 
                    content={submission.card_text}
                    className={`w-full transition-all duration-200 ${
                      selectedCard === submission.id 
                        ? 'ring-4 ring-tnt-gold scale-105' 
                        : isJudge 
                          ? 'hover:scale-102 hover:ring-2 hover:ring-tnt-hotPink' 
                          : ''
                    }`}
                  />
                  
                  {/* Card Info */}
                  <div className="text-center">
                    {submission.is_deck_card ? (
                      <p className="text-sm text-tnt-whiteSmoke/60">Random Card</p>
                    ) : (
                      <p className="text-sm text-tnt-whiteSmoke/60">Player Card</p>
                    )}
                    
                    {isJudge && selectedCard === submission.id && (
                      <p className="text-sm text-tnt-gold font-bold">Selected!</p>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Judge Actions */}
            {isJudge && (
              <div className="text-center mt-8">
                {selectedCard ? (
                  <Button
                    onClick={handleConfirmSelection}
                    className="bg-tnt-gold hover:bg-tnt-gold/80 text-tnt-charcoal font-bold px-8 py-3 text-lg"
                  >
                    Confirm Winner! 👑
                  </Button>
                ) : (
                  <p className="text-tnt-whiteSmoke/60">Click on a card to select the winner</p>
                )}
              </div>
            )}
          </div>
        )}

        {submissions.length === 0 && (
          <div className="text-center">
            <p className="text-xl text-tnt-whiteSmoke/60">Waiting for card submissions...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlayerJudgingInterface;
