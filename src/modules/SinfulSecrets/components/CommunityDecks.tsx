
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { getPublicCustomCardPacks, CardPack } from "@/services/packService";
import { toast } from "sonner";
import { Users, CheckCircle2 } from "lucide-react";

interface CommunityDecksProps {
  selectedPacks: string[];
  onTogglePack: (packId: string) => void;
  onClose: () => void;
}

const CommunityDecks: React.FC<CommunityDecksProps> = ({
  selectedPacks,
  onTogglePack,
  onClose
}) => {
  const [communityDecks, setCommunityDecks] = useState<CardPack[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadCommunityDecks();
  }, []);

  const loadCommunityDecks = async () => {
    try {
      const decks = await getPublicCustomCardPacks();
      setCommunityDecks(decks);
    } catch (error) {
      console.error('Failed to load community decks:', error);
      toast.error('Failed to load community decks');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="w-12 h-12 border-4 border-tnt-pink border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white flex items-center gap-2">
          <Users className="h-6 w-6 text-tnt-hotPink" />
          Community Decks
        </h2>
        <Button onClick={onClose} variant="tntOutline">
          Close
        </Button>
      </div>

      {communityDecks.length === 0 ? (
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="py-12 text-center">
            <p className="text-gray-300 mb-4">No public community decks available yet.</p>
            <p className="text-sm text-gray-400">
              Be the first to create and share a custom deck with the community!
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {communityDecks.map((deck) => (
            <Card 
              key={deck.id} 
              className={`bg-gray-800 border cursor-pointer transition-all duration-200 ${
                selectedPacks.includes(deck.id) 
                  ? 'border-tnt-pink shadow-tnt-pink/20 shadow-lg' 
                  : 'border-gray-700 hover:border-gray-600'
              }`}
              onClick={() => onTogglePack(deck.id)}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <div className="inline-block px-2 py-1 text-xs rounded mb-2 bg-purple-600 text-white">
                      Community
                    </div>
                    <CardTitle className="text-white">{deck.packName}</CardTitle>
                    <CardDescription className="text-gray-300">
                      {deck.description}
                    </CardDescription>
                  </div>
                  {selectedPacks.includes(deck.id) && (
                    <CheckCircle2 className="text-tnt-pink h-6 w-6" />
                  )}
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="text-sm text-gray-400 space-y-1 mb-4">
                  <div>{deck.blackCards.length} prompt cards</div>
                  <div>{deck.whiteCards.length} response cards</div>
                </div>
                
                <div className="flex items-center justify-between">
                  <Checkbox 
                    checked={selectedPacks.includes(deck.id)}
                    onCheckedChange={() => onTogglePack(deck.id)}
                    id={`community-pack-${deck.id}`}
                    className="data-[state=checked]:bg-tnt-pink data-[state=checked]:border-tnt-pink"
                  />
                  <label htmlFor={`community-pack-${deck.id}`} className="text-sm text-gray-300 cursor-pointer">
                    {selectedPacks.includes(deck.id) ? 'Selected' : 'Select this deck'}
                  </label>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default CommunityDecks;
