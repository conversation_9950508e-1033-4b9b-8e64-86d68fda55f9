import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Play, Settings, QrCode, Copy, Crown, UserCheck } from 'lucide-react';
import QRCodeJoin from '@/components/QRCodeJoin';
import { useSinfulSecretsGame, SinfulSecretsPlayer } from '@/hooks/useSinfulSecretsGame';

interface SinfulSecretsStagingAreaProps {
  gameCode: string;
  hostName: string;
  onStartGame: () => void;
  onShowSettings: () => void;
}

const SinfulSecretsStagingArea: React.FC<SinfulSecretsStagingAreaProps> = ({
  gameCode,
  hostName,
  onStartGame,
  onShowSettings
}) => {
  const [showQRCode, setShowQRCode] = useState(false);
  const [readyPlayers, setReadyPlayers] = useState<Set<string>>(new Set());
  
  const {
    game,
    players,
    isLoading,
    error
  } = useSinfulSecretsGame(gameCode);

  // Debug logging
  useEffect(() => {
    console.log('Staging area - Players updated:', players);
    console.log('Staging area - Game:', game);
    console.log('Staging area - isLoading:', isLoading);
    console.log('Staging area - error:', error);
  }, [players, game, isLoading, error]);

  // Manual refresh function for debugging
  const handleManualRefresh = () => {
    console.log('Manual refresh triggered');
    window.location.reload();
  };

  // Since host is not a player, all players in the list are actual players
  const minPlayersNeeded = 3; // Minimum players needed to start
  const canStartGame = players.length >= minPlayersNeeded;

  const copyGameCode = () => {
    navigator.clipboard.writeText(gameCode);
    toast.success('Game code copied to clipboard!');
  };

  const copyJoinLink = () => {
    const joinUrl = `${window.location.origin}/sinful-secrets-player?code=${gameCode}`;
    navigator.clipboard.writeText(joinUrl);
    toast.success('Join link copied to clipboard!');
  };

  const handleStartGame = () => {
    if (!canStartGame) {
      toast.error(`Need at least ${minPlayersNeeded} players to start`);
      return;
    }
    onStartGame();
  };

  const togglePlayerReady = (playerId: string) => {
    setReadyPlayers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(playerId)) {
        newSet.delete(playerId);
      } else {
        newSet.add(playerId);
      }
      return newSet;
    });
  };

  const allPlayersReady = players.length > 0 && players.every(p => readyPlayers.has(p.id));

  return (
    <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-5xl font-pacifico text-tnt-hotPink mb-4">Sinful Secrets</h1>
          <p className="text-xl text-tnt-whiteSmoke/80">Waiting for players to join...</p>
        </div>

        {/* Game Code Display */}
        <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30 mb-8">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold text-tnt-hotPink mb-4">Game Code</h2>
            <div className="text-6xl font-mono font-bold text-tnt-whiteSmoke mb-6 tracking-wider">
              {gameCode}
            </div>
            <div className="flex justify-center gap-4">
              <Button
                onClick={copyGameCode}
                variant="outline"
                className="border-tnt-hotPink/30 hover:border-tnt-hotPink"
              >
                <Copy className="mr-2 h-4 w-4" />
                Copy Code
              </Button>
              <Button
                onClick={copyJoinLink}
                variant="outline"
                className="border-tnt-hotPink/30 hover:border-tnt-hotPink"
              >
                <Copy className="mr-2 h-4 w-4" />
                Copy Link
              </Button>
              <Button
                onClick={() => setShowQRCode(!showQRCode)}
                variant="outline"
                className="border-tnt-hotPink/30 hover:border-tnt-hotPink"
              >
                <QrCode className="mr-2 h-4 w-4" />
                QR Code
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* QR Code */}
        {showQRCode && (
          <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30 mb-8">
            <CardContent className="p-8 text-center">
              <h3 className="text-xl font-bold text-tnt-hotPink mb-4">Scan to Join</h3>
              <div className="flex justify-center">
                <QRCodeJoin gameCode={gameCode} gameType="sinful-secrets" />
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Players List */}
          <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
            <CardHeader>
              <CardTitle className="text-tnt-hotPink flex items-center">
                <Users className="mr-2 h-5 w-5" />
                Players ({players.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Host */}
                <div className="flex items-center justify-between p-3 bg-tnt-hotPink/20 rounded-lg border border-tnt-hotPink/30">
                  <div className="flex items-center">
                    <Crown className="mr-2 h-4 w-4 text-tnt-hotPink" />
                    <span className="font-bold">{hostName}</span>
                    <span className="ml-2 text-sm text-tnt-whiteSmoke/60">(Host)</span>
                  </div>
                  <div className="text-tnt-hotPink font-bold">READY</div>
                </div>

                {/* Players */}
                {players.map((player) => (
                  <div
                    key={player.id}
                    className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                      readyPlayers.has(player.id)
                        ? 'bg-green-500/20 border-green-500/30'
                        : 'bg-tnt-charcoal/30 border-tnt-whiteSmoke/20'
                    }`}
                  >
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        player.is_active ? 'bg-green-500' : 'bg-gray-500'
                      }`} />
                      <span className="font-medium">{player.player_name}</span>
                    </div>
                    <Button
                      onClick={() => togglePlayerReady(player.id)}
                      size="sm"
                      variant={readyPlayers.has(player.id) ? "default" : "outline"}
                      className={readyPlayers.has(player.id)
                        ? "bg-green-500 hover:bg-green-600"
                        : "border-tnt-whiteSmoke/30 hover:border-tnt-whiteSmoke"
                      }
                    >
                      {readyPlayers.has(player.id) ? (
                        <>
                          <UserCheck className="mr-1 h-3 w-3" />
                          Ready
                        </>
                      ) : (
                        'Mark Ready'
                      )}
                    </Button>
                  </div>
                ))}

                {/* Empty slots */}
                {Array.from({ length: Math.max(0, minPlayersNeeded - players.length) }).map((_, index) => (
                  <div
                    key={`empty-${index}`}
                    className="flex items-center p-3 bg-tnt-charcoal/20 rounded-lg border border-dashed border-tnt-whiteSmoke/30"
                  >
                    <div className="w-3 h-3 rounded-full mr-3 bg-gray-600" />
                    <span className="text-tnt-whiteSmoke/60 italic">Waiting for player...</span>
                  </div>
                ))}
              </div>

              {/* Player count info */}
              <div className="mt-4 p-3 bg-tnt-charcoal/30 rounded-lg">
                <p className="text-sm text-tnt-whiteSmoke/80">
                  <strong>Players:</strong> {players.length} |
                  <strong className="ml-2">Minimum:</strong> {minPlayersNeeded} |
                  <strong className="ml-2">Maximum:</strong> 8
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Game Info & Controls */}
          <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
            <CardHeader>
              <CardTitle className="text-tnt-hotPink">Game Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-tnt-charcoal/30 rounded-lg">
                <h4 className="font-bold text-tnt-hotPink mb-2">How to Play:</h4>
                <ul className="text-sm text-tnt-whiteSmoke/80 space-y-1">
                  <li>• Players submit cards in response to prompts</li>
                  <li>• AI host judges the best responses</li>
                  <li>• Switch to Sinful Secrets mode for intimate challenges</li>
                  <li>• Use the safe word if things get too spicy</li>
                </ul>
              </div>

              <div className="p-4 bg-tnt-charcoal/30 rounded-lg">
                <h4 className="font-bold text-tnt-hotPink mb-2">Game Status:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Players Ready:</span>
                    <span className={allPlayersReady && canStartGame ? 'text-green-400' : 'text-yellow-400'}>
                      {readyPlayers.size}/{players.length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Minimum Met:</span>
                    <span className={canStartGame ? 'text-green-400' : 'text-red-400'}>
                      {canStartGame ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={onShowSettings}
                  variant="outline"
                  className="w-full border-tnt-hotPink/30 hover:border-tnt-hotPink"
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Game Settings
                </Button>

                <Button
                  onClick={handleManualRefresh}
                  variant="outline"
                  className="w-full border-yellow-500/30 hover:border-yellow-500 text-yellow-400"
                >
                  🔄 Debug Refresh
                </Button>

                <Button
                  onClick={handleStartGame}
                  disabled={!canStartGame}
                  className="w-full bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-3 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Play className="mr-2 h-5 w-5" />
                  {canStartGame ? 'Start Game' : `Need ${minPlayersNeeded - players.length} More Players`}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {error && (
          <div className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
            <p className="text-red-400">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SinfulSecretsStagingArea;
