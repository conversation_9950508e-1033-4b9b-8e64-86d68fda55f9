import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Player, GameState, PromptResponse } from '../types/gameTypes';
import { HybridGameState, CardPrompt, HybridPromptResponse } from '../types/hybridTypes';
import { 
  initializeGame, 
  selectPrompt, 
  personalizePrompt, 
  getNextPlayer, 
  markPlayerAsPlayed, 
  advanceRound, 
  handleSafeWord,
  shouldTriggerNeverHaveIEver,
  resetNeverHaveIEverCounter
} from '../services/gameEngine';
import { 
  initializeHybridGame, 
  shouldUseCardMode, 
  drawCard, 
  getHybridPrompt 
} from '../services/hybridGameEngine';

// Mock players for development
const mockPlayers: Player[] = [
  { id: '1', name: '<PERSON>', gender: 'F', role: 'Host', hasPlayed: false, penaltyDrinks: 0, safeWordUsage: 0 },
  { id: '2', name: '<PERSON>', gender: '<PERSON>', hasPlayed: false, penaltyDrinks: 0, safeWordUsage: 0 },
  { id: '3', name: '<PERSON>', gender: 'F', hasPlayed: false, penaltyDrinks: 0, safeWordUsage: 0 },
  { id: '4', name: '<PERSON>', gender: 'Other', hasPlayed: false, penaltyDrinks: 0, safeWordUsage: 0 }
];

// Helper function to convert GameState to HybridGameState
const convertToHybridGameState = (gameState: GameState, hybridState: HybridGameState): HybridGameState => {
  return {
    ...gameState,
    cardModeActive: hybridState.cardModeActive,
    currentCardPrompt: hybridState.currentCardPrompt,
    cardDeck: hybridState.cardDeck,
    usedCards: hybridState.usedCards
  };
};

const SinfulSecretsRoundManager: React.FC = () => {
  const [gameState, setGameState] = useState<HybridGameState>(initializeHybridGame(mockPlayers));
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedNumber, setSelectedNumber] = useState<number | null>(null);
  const [showNumberSelector, setShowNumberSelector] = useState<boolean>(true);
  const [currentPromptResponse, setCurrentPromptResponse] = useState<HybridPromptResponse | null>(null);
  
  // Start the game
  const startGame = () => {
    setGameState(prev => ({ ...prev, gameStarted: true }));
    toast.success('🎉 Welcome to Sinful Secrets! Let the games begin!');
  };
  
  // Enhanced number selection to support hybrid mode
  const handleNumberSelection = async (number: number) => {
    if (!gameState.gameStarted) return;
    
    setSelectedNumber(number);
    setIsLoading(true);
    setShowNumberSelector(false);
    
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      let promptResponse: HybridPromptResponse;
      
      // Check if we should use card mode
      if (shouldUseCardMode(gameState)) {
        const { gameState: updatedState, card } = drawCard(gameState);
        setGameState(updatedState);
        
        if (card) {
          promptResponse = {
            type: 'card',
            card,
            requiresDrink: card.isNSFW
          };
          
          toast.info('🎴 Card Mode Activated! Drawing a special challenge...');
        } else {
          // Fallback to traditional if no cards available
          const traditionalResponse = selectPrompt(gameState, number);
          promptResponse = {
            type: 'traditional',
            prompt: traditionalResponse.prompt,
            selectedNumber: number,
            category: traditionalResponse.category,
            requiresDrink: traditionalResponse.requiresDrink || false
          };
        }
      } else {
        // Traditional prompt system
        const traditionalResponse = selectPrompt(gameState, number);
        
        const personalizedText = personalizePrompt(
          traditionalResponse.prompt, 
          gameState.activePlayer!, 
          gameState.players
        );
        
        const personalizedPrompt = {
          ...traditionalResponse.prompt,
          personalizedText
        };
        
        promptResponse = {
          type: 'traditional',
          prompt: personalizedPrompt,
          selectedNumber: number,
          category: traditionalResponse.category,
          requiresDrink: traditionalResponse.requiresDrink || false
        };
        
        // Mark prompt as used
        setGameState(prev => ({
          ...prev,
          currentPrompt: personalizedPrompt,
          usedPrompts: [...prev.usedPrompts, traditionalResponse.prompt.id],
          usedNumbers: [...prev.usedNumbers, number],
          cardModeActive: false
        }));
      }
      
      setCurrentPromptResponse(promptResponse);
      
      // Handle automatic drinking if required
      if (promptResponse.requiresDrink) {
        const drinkReason = promptResponse.type === 'card' ? 'NSFW card' : `Number ${number}`;
        toast.info(`🍹 ${drinkReason} requires a drink before continuing!`);
      }
      
    } catch (error) {
      console.error('Error selecting prompt:', error);
      toast.error('Failed to get prompt. Please try again.');
      setShowNumberSelector(true);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle completing a prompt
  const handleCompletePrompt = () => {
    if (!gameState.activePlayer) return;
    
    // Mark current player as played and convert to HybridGameState
    let baseUpdatedGameState = markPlayerAsPlayed(gameState, gameState.activePlayer.id);
    let updatedGameState = convertToHybridGameState(baseUpdatedGameState, gameState);
    
    // Check if all players have played this round
    const allPlayed = updatedGameState.players.every(p => p.hasPlayed);
    
    if (allPlayed) {
      // Check if Never Have I Ever should trigger
      if (shouldTriggerNeverHaveIEver(updatedGameState)) {
        toast.info('🔥 Time for a Never Have I Ever lightning round!');
        const baseResetGameState = resetNeverHaveIEverCounter(updatedGameState);
        updatedGameState = convertToHybridGameState(baseResetGameState, gameState);
      }
      
      // Advance to next round
      const baseAdvancedGameState = advanceRound(updatedGameState);
      updatedGameState = convertToHybridGameState(baseAdvancedGameState, gameState);
      
      // Check for overdrive activation
      if (updatedGameState.isOverdriveMode && !gameState.isOverdriveMode) {
        toast.warning('🔥 OVERDRIVE MODE ACTIVATED! 🔥');
        toast.warning('Things are about to get twice as intense!');
      }
      
      toast.success(`Round ${gameState.round} completed! Starting Round ${updatedGameState.round}`);
    } else {
      // Move to next player
      updatedGameState.activePlayer = getNextPlayer(updatedGameState);
    }
    
    setGameState(updatedGameState);
    setCurrentPromptResponse(null);
    setSelectedNumber(null);
    setShowNumberSelector(true);
  };
  
  // Handle safe word usage
  const handleSafeWordUsage = () => {
    if (!gameState.activePlayer) return;
    
    const baseSafeWordGameState = handleSafeWord(gameState, gameState.activePlayer.id);
    const updatedGameState = convertToHybridGameState(baseSafeWordGameState, gameState);
    const player = updatedGameState.players.find(p => p.id === gameState.activePlayer!.id);
    
    toast.info(`🔐 Safe word "${gameState.safeWord}" used!`);
    
    if (player?.safeWordUsage === 2) {
      toast.warning('Double safe word penalty! Extra drinks required.');
    }
    
    toast.info('Skipping to the next prompt...');
    
    setGameState(updatedGameState);
    handleCompletePrompt();
  };
  
  // Get category color
  const getCategoryColor = (category: string) => {
    switch(category) {
      case 'Truth': return 'bg-tnt-deepPurple';
      case 'Dare': return 'bg-tnt-crimson';
      case 'WouldYouRather': return 'bg-tnt-hotPink';
      case 'Wildcard': return 'bg-tnt-gold text-tnt-midnightBlack';
      default: return 'bg-gray-600';
    }
  };
  
  // Get intensity color
  const getIntensityColor = (intensity: string) => {
    switch(intensity) {
      case 'Playful': return 'text-green-400';
      case 'Sensual': return 'text-yellow-400';
      case 'Daring': return 'text-orange-400';
      case 'Bold': return 'text-red-400';
      default: return 'text-white';
    }
  };
  
  // Enhanced prompt display to handle both cards and traditional prompts
  const renderCurrentPrompt = () => {
    if (!currentPromptResponse) return null;
    
    const isCardMode = currentPromptResponse.type === 'card';
    const promptText = isCardMode 
      ? currentPromptResponse.card?.text 
      : (currentPromptResponse.prompt?.personalizedText || currentPromptResponse.prompt?.text);
    
    const categoryColor = isCardMode 
      ? 'bg-gradient-to-r from-tnt-hotPink to-tnt-crimson' 
      : getCategoryColor(currentPromptResponse.category || 'Truth');
    
    return (
      <div className="bg-gradient-to-r from-tnt-charcoal/50 to-tnt-midnightBlack/50 rounded-xl overflow-hidden shadow-xl backdrop-blur-sm">
        <div className={`${categoryColor} py-3 px-6`}>
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-bold text-white">
              {isCardMode ? (
                <div className="flex items-center gap-2">
                  🎴 Card Challenge
                  <span className="text-sm bg-white/20 px-2 py-1 rounded-full">
                    {currentPromptResponse.card?.category}
                  </span>
                </div>
              ) : (
                currentPromptResponse.category === 'WouldYouRather' ? 'Would You Rather' : currentPromptResponse.category
              )}
            </h3>
            <span className="text-sm text-white/80">
              {isCardMode ? 'Card Mode' : `Number ${selectedNumber}`}
            </span>
          </div>
        </div>
        
        <div className="p-6">
          <p className="text-2xl font-poppins mb-6 text-tnt-whiteSmoke">
            {promptText}
          </p>
          
          {currentPromptResponse.requiresDrink && (
            <div className="bg-yellow-900/30 text-yellow-200 p-3 rounded-md mb-4 text-sm">
              🍹 This {isCardMode ? 'card' : 'number'} requires a drink before completing the challenge!
            </div>
          )}
          
          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 mt-8">
            <button
              onClick={handleCompletePrompt}
              className="bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-3 px-6 rounded-lg transition-colors"
            >
              {gameState.round >= 15 ? 'Complete Game' : 'Complete Challenge'}
            </button>
            
            <button
              onClick={handleSafeWordUsage}
              className="bg-transparent border border-tnt-whiteSmoke/30 hover:border-tnt-whiteSmoke text-tnt-whiteSmoke font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Use Safe Word
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  if (!gameState.gameStarted) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-2xl">
          <h1 className="text-5xl font-pacifico text-tnt-hotPink mb-6">Sinful Secrets</h1>
          <p className="text-xl mb-4">A daring game of truth, dare, and wild revelations</p>
          <p className="text-tnt-whiteSmoke/80 mb-8">
            15 rounds of escalating intensity designed to bring you closer together through playful, sensual, and bold challenges.
          </p>
          
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-bold mb-4">Safe Word: <span className="text-tnt-hotPink">{gameState.safeWord}</span></h3>
            <p className="text-sm text-tnt-whiteSmoke/70">
              Use this at any time to skip a prompt. Remember, this is all about fun and comfort!
            </p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-4 px-8 rounded-lg text-xl transition-colors"
          >
            Start the Adventure
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
      <div className="container mx-auto py-8">
        {/* Game Header */}
        <div className="mb-6 flex flex-col md:flex-row justify-between items-center">
          <div>
            <h1 className="text-3xl font-pacifico mb-2">Sinful Secrets</h1>
            <div className="flex items-center space-x-2">
              <span className="text-sm px-2 py-1 rounded bg-tnt-hotPink/20 text-tnt-hotPink">
                Round {gameState.round}/15
              </span>
              <span className={`text-sm px-2 py-1 rounded bg-tnt-deepPurple/20 ${getIntensityColor(gameState.intensityLevel)}`}>
                {gameState.intensityLevel} Intensity
              </span>
              {gameState.isOverdriveMode && (
                <span className="text-sm px-2 py-1 rounded bg-red-600/20 text-red-400 animate-pulse">
                  OVERDRIVE
                </span>
              )}
            </div>
          </div>
          
          <div className="mt-4 md:mt-0">
            <p className="text-sm border border-tnt-whiteSmoke/20 bg-tnt-deepPurple/10 rounded-lg p-2">
              Safe word: <span className="font-bold text-tnt-hotPink">{gameState.safeWord}</span>
            </p>
          </div>
        </div>
        
        {/* Active Player */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 mb-4">
            <div className="h-10 w-10 rounded-full bg-tnt-hotPink flex items-center justify-center font-bold text-white">
              {gameState.activePlayer?.name.charAt(0)}
            </div>
            <div>
              <p className="text-lg font-medium">{gameState.activePlayer?.name}'s turn</p>
              {gameState.activePlayer?.role && (
                <p className="text-xs text-tnt-whiteSmoke/60">{gameState.activePlayer.role}</p>
              )}
            </div>
          </div>
        </div>
        
        {/* Main Game Content */}
        {showNumberSelector && !isLoading && !currentPromptResponse && (
          <div className="bg-gradient-to-r from-tnt-charcoal/50 to-tnt-midnightBlack/50 rounded-xl p-8 text-center backdrop-blur-sm">
            <h2 className="text-2xl font-bold mb-6">Choose Your Number</h2>
            <p className="text-tnt-whiteSmoke/80 mb-4">Pick a number between 1 and 20 to reveal your challenge!</p>
            
            {/* Show hint about card mode */}
            {shouldUseCardMode(gameState) && (
              <div className="bg-tnt-hotPink/20 text-tnt-hotPink p-3 rounded-md mb-6 text-sm">
                🎴 Card Mode Active! This round features special card challenges.
              </div>
            )}
            
            <div className="grid grid-cols-5 md:grid-cols-10 gap-3">
              {Array.from({length: 20}, (_, i) => i + 1).map(num => (
                <button
                  key={num}
                  onClick={() => handleNumberSelection(num)}
                  disabled={gameState.usedNumbers.includes(num)}
                  className={`
                    w-12 h-12 rounded-lg font-bold text-lg transition-all
                    ${gameState.usedNumbers.includes(num) 
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                      : 'bg-tnt-hotPink hover:bg-tnt-pink text-white hover:scale-105'
                    }
                  `}
                >
                  {num}
                </button>
              ))}
            </div>
          </div>
        )}
        
        {isLoading && (
          <div className="bg-gradient-to-r from-tnt-charcoal/50 to-tnt-midnightBlack/50 rounded-xl p-8 text-center backdrop-blur-sm">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-tnt-hotPink mx-auto mb-4"></div>
            <p className="font-medium">Selecting the perfect challenge...</p>
          </div>
        )}
        
        {currentPromptResponse && renderCurrentPrompt()}
        
        {/* Player Status */}
        <div className="mt-8 grid grid-cols-2 sm:grid-cols-4 gap-4">
          {gameState.players.map((player) => (
            <div 
              key={player.id} 
              className={`p-4 rounded-lg backdrop-blur-sm ${
                player.id === gameState.activePlayer?.id 
                  ? 'bg-tnt-hotPink/20 border border-tnt-hotPink' 
                  : 'bg-tnt-whiteSmoke/5'
              }`}
            >
              <p className="font-medium">{player.name}</p>
              <p className="text-xs text-tnt-whiteSmoke/60">
                {player.hasPlayed ? 'Has played this round' : 'Waiting to play'}
              </p>
              {player.penaltyDrinks > 0 && (
                <p className="text-xs text-orange-400">
                  Penalty drinks: {player.penaltyDrinks}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SinfulSecretsRoundManager;
