
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import GameCard from '@/components/GameCard';
import { usePlayerHand } from '@/hooks/usePlayerHand';

interface PlayerCardInterfaceProps {
  playerName: string;
  playerId: string;
  gameId: string;
  currentPrompt: string;
  onSubmitCard: (playerId: string, card: string) => void;
  hasSubmitted: boolean;
}

const PlayerCardInterface: React.FC<PlayerCardInterfaceProps> = ({
  playerName,
  playerId,
  gameId,
  currentPrompt,
  onSubmitCard,
  hasSubmitted
}) => {
  const [selectedCard, setSelectedCard] = useState<string>('');
  const { playerHand, isLoading, error, burnHand } = usePlayerHand(playerId, gameId);

  const handleSubmit = () => {
    if (selectedCard) {
      onSubmitCard(playerId, selectedCard);
    }
  };

  const handleBurnHand = async () => {
    if (confirm('Are you sure you want to burn your hand? This will replace all your cards with new ones.')) {
      await burnHand();
      setSelectedCard(''); // Clear selection
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Loading your cards...</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-tnt-hotPink mx-auto"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4 text-red-400">Error Loading Cards</h2>
          <p className="mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  if (hasSubmitted) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Card Submitted!</h1>
          <p className="text-tnt-whiteSmoke/80">
            Waiting for other players to submit their cards...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
      <div className="w-full max-w-4xl">
        <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-6 text-center">
          Hey {playerName}!
        </h1>
        
        <div className="mb-6">
          <h2 className="text-lg font-bold mb-4 text-center">Respond to this prompt:</h2>
          <div className="max-w-md mx-auto">
            <GameCard 
              type="black" 
              content={currentPrompt}
              className="w-full"
            />
          </div>
        </div>

        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold text-center flex-1">Choose your best response:</h3>
            <Button
              onClick={handleBurnHand}
              variant="outline"
              size="sm"
              className="border-tnt-gold/50 text-tnt-gold hover:bg-tnt-gold/10"
            >
              🔥 Burn Hand
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {playerHand.map((card, index) => (
              <GameCard
                key={`${card}-${index}`}
                type="white"
                content={card}
                onClick={() => setSelectedCard(card)}
                className={`cursor-pointer transition-all ${
                  selectedCard === card
                    ? 'ring-4 ring-tnt-hotPink scale-105'
                    : 'hover:scale-105'
                }`}
              />
            ))}
          </div>
        </div>

        {selectedCard && (
          <div className="text-center">
            <Button
              onClick={handleSubmit}
              className="bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-3 px-8 text-lg"
            >
              Submit Card
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlayerCardInterface;
