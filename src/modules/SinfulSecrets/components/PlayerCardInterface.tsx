
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import GameCard from '@/components/GameCard';
import { getWhiteCardOptions } from '../services/hybridGameFlow';

interface PlayerCardInterfaceProps {
  playerName: string;
  playerId: string;
  currentPrompt: string;
  onSubmitCard: (playerId: string, card: string) => void;
  hasSubmitted: boolean;
}

const PlayerCardInterface: React.FC<PlayerCardInterfaceProps> = ({
  playerName,
  playerId,
  currentPrompt,
  onSubmitCard,
  hasSubmitted
}) => {
  const [selectedCard, setSelectedCard] = useState<string>('');
  const [cardOptions] = useState(() => {
    // Give each player 5 random white cards
    const allCards = getWhiteCardOptions();
    return allCards.sort(() => Math.random() - 0.5).slice(0, 5);
  });

  const handleSubmit = () => {
    if (selectedCard) {
      onSubmitCard(playerId, selectedCard);
    }
  };

  if (hasSubmitted) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Card Submitted!</h1>
          <p className="text-tnt-whiteSmoke/80">
            Waiting for the AI host to judge all submissions...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
      <div className="w-full max-w-4xl">
        <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-6 text-center">
          Hey {playerName}!
        </h1>
        
        <div className="mb-6">
          <h2 className="text-lg font-bold mb-4 text-center">Respond to this prompt:</h2>
          <div className="max-w-md mx-auto">
            <GameCard 
              type="black" 
              content={currentPrompt}
              className="w-full"
            />
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-bold mb-4 text-center">Choose your best response:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {cardOptions.map((card, index) => (
              <GameCard
                key={index}
                type="white"
                content={card}
                onClick={() => setSelectedCard(card)}
                className={`cursor-pointer transition-all ${
                  selectedCard === card 
                    ? 'ring-4 ring-tnt-hotPink scale-105' 
                    : 'hover:scale-105'
                }`}
              />
            ))}
          </div>
        </div>

        {selectedCard && (
          <div className="text-center">
            <Button
              onClick={handleSubmit}
              className="bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-3 px-8 text-lg"
            >
              Submit Card
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlayerCardInterface;
