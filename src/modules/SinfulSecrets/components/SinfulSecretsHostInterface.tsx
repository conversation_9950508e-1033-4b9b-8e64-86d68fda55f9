import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useSinfulSecretsGame } from '@/hooks/useSinfulSecretsGame';
import GameHeader from '@/components/host/GameHeader';
import GameFooter from '@/components/host/GameFooter';
import HostLineDisplay from '@/components/host/HostLineDisplay';
import AIChat from '@/components/AIChat';
import AdminControlButton from '@/components/host/AdminControlButton';
import QRCodeJoin from '@/components/QRCodeJoin';
import CardGamePhase from './CardGamePhase';
import SinfulSecretsPhase from './SinfulSecretsPhase';
import CardDeckSelector from './CardDeckSelector';

const SinfulSecretsHostInterface: React.FC = () => {
  const [hostName, setHostName] = useState<string>("");
  const [gameCode, setGameCode] = useState<string>("");
  const [isGameCreated, setIsGameCreated] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<Array<{id: string; sender: string; text: string; timestamp: Date}>>([]);
  const [currentPrompt, setCurrentPrompt] = useState<string>("");
  const [pendingTruthDare, setPendingTruthDare] = useState<{playerId: string, number: number} | null>(null);
  const [currentCardPrompt, setCurrentCardPrompt] = useState<string>("");
  const [showCardDeckSelector, setShowCardDeckSelector] = useState(false);
  const [selectedCardPacks, setSelectedCardPacks] = useState<string[]>([]);
  const [customDeck, setCustomDeck] = useState<any>(null);
  
  // AI Safety controls
  const [aiCallCount, setAiCallCount] = useState(0);
  const [lastAiCall, setLastAiCall] = useState<Date | null>(null);
  const [aiCooldown, setAiCooldown] = useState(false);
  const [isAiLoading, setIsAiLoading] = useState(false);
  
  const AI_CALL_LIMIT = 50;
  const AI_COOLDOWN_MS = 2000;

  useEffect(() => {
    const session = sessionStorage.getItem('playerName');
    const code = sessionStorage.getItem('gameCode');
    
    if (session) setHostName(session);
    if (code) setGameCode(code);

    // Initialize with welcome message and first card prompt
    const welcomeMessage = {
      id: '1',
      sender: 'host',
      text: 'Welcome to Sinful Secrets! We\'ll be playing a hybrid game - card rounds judged by me, alternating with spicy personal challenges. Let\'s start with our first card prompt! 😈',
      timestamp: new Date()
    };
    setChatMessages([welcomeMessage]);
    setCurrentCardPrompt(getCurrentPrompt(gameState));
  }, []);

  const currentGamePhase: GamePhase = getGamePhase(gameState, turnState);

  const checkAiLimits = (): boolean => {
    const now = new Date();
    
    if (aiCallCount >= AI_CALL_LIMIT) {
      toast.error('AI call limit reached for this session. Please refresh to reset.');
      return false;
    }
    
    if (lastAiCall && (now.getTime() - lastAiCall.getTime()) < AI_COOLDOWN_MS) {
      if (!aiCooldown) {
        setAiCooldown(true);
        setTimeout(() => setAiCooldown(false), AI_COOLDOWN_MS);
      }
      return false;
    }
    
    return true;
  };

  const callAI = async (message: string, context?: string): Promise<string> => {
    if (!checkAiLimits()) {
      return "Please wait a moment before sending another message...";
    }

    setIsAiLoading(true);
    setAiCallCount(prev => prev + 1);
    setLastAiCall(new Date());

    try {
      console.log('Calling AI with message:', message);
      
      const { data, error } = await supabase.functions.invoke('ai-chat', {
        body: {
          message,
          gameContext: context || `Round ${gameState.round}, ${gameState.intensityLevel} intensity, ${currentGamePhase} phase`,
          playerName: hostName
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        throw new Error(error.message || 'Failed to get AI response');
      }

      console.log('AI response received:', data);
      return data.response || data.fallback || "I'm here for all the secrets! 😈";

    } catch (error) {
      console.error('AI call error:', error);
      toast.error('AI host is taking a break. Try again in a moment.');
      return "I'm having a deliciously wicked moment... but I'll be right back! 😏";
    } finally {
      setIsAiLoading(false);
    }
  };

  const handleCardSubmission = async (playerId: string, card: string) => {
    const newSubmissions = [...turnState.cardSubmissions, { playerId, card }];
    setTurnState(prev => ({ ...prev, cardSubmissions: newSubmissions }));
    
    const aiResponse = await callAI(
      `A player submitted a card: "${card}" for the prompt "${currentCardPrompt}"`,
      'Card submission phase'
    );
    
    const aiMessage = {
      id: Date.now().toString(),
      sender: 'host',
      text: aiResponse,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, aiMessage]);
  };

  const handleWinnerSelection = async (submission: { playerId: string; card: string }) => {
    const winner = gameState.players.find(p => p.id === submission.playerId);
    if (winner) {
      winner.score = (winner.score || 0) + 1;
    }

    const { gameState: newGameState, turnState: newTurnState } = advanceGameFlow(
      gameState, 
      turnState, 
      'winner-selected'
    );
    
    setGameState(newGameState);
    setTurnState(newTurnState);
    
    const aiResponse = await callAI(
      `${winner?.name} won the card round with: "${submission.card}". Now it's time for their sinful secrets turn!`,
      'Winner selected, transitioning to sinful secrets'
    );
    
    const aiMessage = {
      id: Date.now().toString(),
      sender: 'host',
      text: aiResponse,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, aiMessage]);
  };

  const handlePlayerNumberSelection = async (playerId: string, number: number, category: string) => {
    if (category === 'Truth' || category === 'Dare') {
      setPendingTruthDare({ playerId, number });
      
      const player = gameState.players.find(p => p.id === playerId);
      const aiResponse = await callAI(
        `${player?.name} selected number ${number} and needs to choose between Truth or Dare`,
        'Number selection phase'
      );
      
      const aiMessage = {
        id: Date.now().toString(),
        sender: 'host',
        text: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);
    } else {
      processPromptSelection(playerId, number, category);
    }
  };

  const handleTruthDareSelection = (playerId: string, choice: 'Truth' | 'Dare') => {
    if (!pendingTruthDare) return;
    
    processPromptSelection(playerId, pendingTruthDare.number, choice);
    setPendingTruthDare(null);
  };

  const processPromptSelection = async (playerId: string, number: number, category: string) => {
    const player = gameState.players.find(p => p.id === playerId);
    if (!player) return;

    // Generate contextual prompts based on intensity level
    const prompts = {
      Truth: [
        `${player.name}, tell us about your most embarrassing romantic moment.`,
        `${player.name}, what's the most daring thing you've ever done for love?`,
        `${player.name}, confess your biggest secret crush from this group.`,
        `${player.name}, what's your wildest fantasy that you've never told anyone?`
      ],
      Dare: [
        `${player.name}, give someone in the group a 10-second passionate stare.`,
        `${player.name}, perform a seductive dance for 30 seconds.`,
        `${player.name}, whisper something naughty in someone's ear.`,
        `${player.name}, let the group vote on who you should give a massage to.`
      ],
      WouldYouRather: [
        `${player.name}, would you rather kiss someone here or reveal your biggest secret?`,
        `${player.name}, would you rather be blindfolded for the next 3 rounds or answer everything completely honestly?`,
        `${player.name}, would you rather send a risky text to your ex or share your browser history?`
      ],
      Wildcard: [
        `${player.name}, surprise us! Share something wild about yourself that would shock everyone here.`,
        `${player.name}, create your own challenge for another player right now.`,
        `${player.name}, choose two people to do a dare together while you watch.`
      ]
    };

    const categoryPrompts = prompts[category as keyof typeof prompts] || prompts.Truth;
    const prompt = categoryPrompts[Math.floor(Math.random() * categoryPrompts.length)];
    setCurrentPrompt(prompt);

    const aiResponse = await callAI(
      `Present this ${category.toLowerCase()} challenge to ${player.name}: ${prompt}`,
      `Challenge presentation - ${category} for ${player.name}`
    );

    const aiMessage = {
      id: Date.now().toString(),
      sender: 'host',
      text: aiResponse,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, aiMessage]);
  };

  const handleCompleteSinfulSecrets = async () => {
    const { gameState: newGameState, turnState: newTurnState } = advanceGameFlow(
      gameState, 
      turnState, 
      'sinful-secrets-complete'
    );
    
    setGameState(newGameState);
    setTurnState(newTurnState);
    setCurrentPrompt("");
    
    // Generate new card prompt for next card phase
    setCurrentCardPrompt(getCurrentPrompt(newGameState));
    
    const aiResponse = await callAI(
      'Sinful secrets challenge completed! Let\'s get back to our card game.',
      'Sinful secrets completed, back to cards'
    );

    const aiMessage = {
      id: Date.now().toString(),
      sender: 'host',
      text: aiResponse,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, aiMessage]);
  };

  const sendChatMessage = async (message: string) => {
    const newMessage = {
      id: Date.now().toString(),
      sender: 'player',
      text: message,
      timestamp: new Date()
    };
    
    setChatMessages(prev => [...prev, newMessage]);
    
    const aiResponse = await callAI(message, 'Player sent a chat message during the game');
    
    const aiMessage = {
      id: (Date.now() + 1).toString(),
      sender: 'host',
      text: aiResponse,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, aiMessage]);
  };

  const handleEndGame = () => {
    if (confirm("Are you sure you want to end the game?")) {
      toast.success("Game ended successfully");
    }
  };

  const handleStartCardSelection = () => {
    setShowCardDeckSelector(true);
  };

  const handleDecksSelected = (packIds: string[], customDeck?: any) => {
    setSelectedCardPacks(packIds);
    if (customDeck) {
      setCustomDeck(customDeck);
    }
    setShowCardDeckSelector(false);
    setGameState(prev => ({ ...prev, gameStarted: true }));
  };

  const handleBackFromDeckSelector = () => {
    setShowCardDeckSelector(false);
  };

  if (showCardDeckSelector) {
    return (
      <CardDeckSelector 
        onDecksSelected={handleDecksSelected}
        onBack={handleBackFromDeckSelector}
      />
    );
  }

  if (!gameState.gameStarted) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-2xl">
          <h1 className="text-5xl font-pacifico text-tnt-hotPink mb-6">Sinful Secrets</h1>
          <p className="text-xl mb-4">A hybrid game of cards, truth, dare, and wild revelations</p>
          
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-bold mb-4">Safe Word: <span className="text-tnt-hotPink">{gameState.safeWord}</span></h3>
            <p className="text-sm text-tnt-whiteSmoke/70 mb-6">
              Players can use this at any time to skip a prompt. Comfort and consent first!
            </p>
            
            <div className="mb-6">
              <QRCodeJoin gameCode={gameCode} />
            </div>
            
            <p className="text-tnt-whiteSmoke/80 mb-8">
              Players can scan the QR code or use game code: <span className="font-bold text-tnt-hotPink">{gameCode}</span>
            </p>
          </div>
          
          <button
            onClick={handleStartCardSelection}
            className="bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-4 px-8 rounded-lg text-xl transition-colors"
          >
            Choose Card Decks & Start
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal to-black text-white relative">
      <GameHeader hostName={hostName} gameCode={gameCode} />
      
      <AdminControlButton gameId="sinful-secrets-dev" />
      
      <div className="container mx-auto px-4 py-4 flex-grow flex flex-col">
        <HostLineDisplay 
          gamePhase="playing"
          roundNumber={gameState.round}
          settings={{
            aiPersona: "You are the seductive, playful host of Sinful Secrets. Your tone is flirty, encouraging, and slightly mischievous. You guide players through intimate revelations with warmth and excitement.",
            responseLength: "medium",
            turnOrder: "sequential",
            escalationSpeed: "normal",
            miniGameFrequency: 3,
            promptDifficultyScaling: true,
            isPrivateGame: true
          }}
          winner={null}
          currentPrompt={currentGamePhase === 'card-judging' ? currentCardPrompt : currentPrompt}
          className="mb-4"
        />
        
        <div className="w-full mb-4">
          <div className="max-w-5xl mx-auto h-96">
            <AIChat 
              gameId="sinful-secrets-dev"
              playerId="host"
              playerName={hostName}
              onSendMessage={sendChatMessage}
              messages={chatMessages}
            />
          </div>
        </div>

        {/* AI Safety Status */}
        <div className="bg-tnt-deepPurple/20 rounded-lg p-3 mb-4">
          <div className="flex justify-between items-center text-sm">
            <span>AI Calls: {aiCallCount}/{AI_CALL_LIMIT}</span>
            <div className="flex items-center gap-2">
              {isAiLoading && <span className="text-blue-400">AI Thinking...</span>}
              {aiCooldown && <span className="text-yellow-400">Cooldown Active</span>}
              <button
                onClick={() => {
                  setAiCallCount(0);
                  setLastAiCall(null);
                  toast.success('AI limits reset');
                }}
                className="text-xs bg-tnt-hotPink/20 hover:bg-tnt-hotPink/40 px-2 py-1 rounded"
              >
                Reset
              </button>
            </div>
          </div>
        </div>

        {/* Game Status */}
        <div className="bg-gradient-to-r from-tnt-charcoal/50 to-tnt-midnightBlack/50 rounded-xl p-6 backdrop-blur-sm mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-pacifico text-tnt-hotPink">
              {currentGamePhase === 'card-submission' ? 'Card Submission' :
               currentGamePhase === 'card-judging' ? 'Card Judging' :
               currentGamePhase === 'sinful-secrets' ? 'Sinful Secrets' :
               'Round Complete'}
            </h2>
            <div className="flex items-center space-x-4">
              <span className="text-sm px-3 py-1 rounded bg-tnt-hotPink/20 text-tnt-hotPink">
                Round {gameState.round}
              </span>
              <span className="text-sm px-3 py-1 rounded bg-tnt-deepPurple/20 text-tnt-deepPurple">
                {gameState.intensityLevel} Intensity
              </span>
            </div>
          </div>

          {/* Render appropriate phase component */}
          {(currentGamePhase === 'card-submission' || currentGamePhase === 'card-judging') && (
            <CardGamePhase
              currentPrompt={currentCardPrompt}
              submissions={turnState.cardSubmissions}
              phase={currentGamePhase === 'card-submission' ? 'submission' : 'judging'}
              onSelectWinner={handleWinnerSelection}
              totalPlayers={gameState.players.length}
            />
          )}

          {currentGamePhase === 'sinful-secrets' && turnState.currentSinfulSecretsPlayer && (
            <SinfulSecretsPhase
              currentPlayer={turnState.currentSinfulSecretsPlayer}
              currentPrompt={currentPrompt}
              onCompleteChallenge={handleCompleteSinfulSecrets}
              onPlayerNumberSelection={handlePlayerNumberSelection}
              pendingTruthDare={!!pendingTruthDare}
              onTruthDareSelection={handleTruthDareSelection}
            />
          )}

          {pendingTruthDare && (
            <div className="bg-yellow-900/30 rounded-lg p-4 mb-4">
              <p className="text-yellow-200">
                Waiting for {gameState.players.find(p => p.id === pendingTruthDare.playerId)?.name} to choose Truth or Dare...
              </p>
            </div>
          )}
        </div>

        {/* Player Status */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          {gameState.players.map((player) => (
            <div 
              key={player.id} 
              className={`p-4 rounded-lg backdrop-blur-sm ${
                player.id === turnState.currentSinfulSecretsPlayer?.id 
                  ? 'bg-tnt-hotPink/20 border border-tnt-hotPink' 
                  : 'bg-tnt-whiteSmoke/5'
              }`}
            >
              <p className="font-medium">{player.name}</p>
              <p className="text-xs text-tnt-whiteSmoke/60">
                Score: {player.score || 0}
              </p>
              {player.safeWordUsage > 0 && (
                <p className="text-xs text-orange-400">
                  Safe words used: {player.safeWordUsage}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
      
      <GameFooter 
        players={gameState.players.map(p => p.name)} 
        onEndGame={handleEndGame} 
      />
    </div>
  );
};

export default SinfulSecretsHostInterface;
