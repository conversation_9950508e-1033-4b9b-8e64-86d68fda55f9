import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useSinfulSecretsGame } from '@/hooks/useSinfulSecretsGame';
import GameHeader from '@/components/host/GameHeader';
import GameFooter from '@/components/host/GameFooter';
import HostLineDisplay from '@/components/host/HostLineDisplay';
import AIChat from '@/components/AIChat';
import AdminControlButton from '@/components/host/AdminControlButton';
import QRCodeJoin from '@/components/QRCodeJoin';
import { Button } from '@/components/ui/button';
import CardGamePhase from './CardGamePhase';
import HostCardJudgingInterface from './HostCardJudgingInterface';
import HostWaitingForJudge from './HostWaitingForJudge';
import SinfulSecretsPhase from './SinfulSecretsPhase';

interface SinfulSecretsHostInterfaceProps {
  gameCode: string;
  hostName: string;
}

const SinfulSecretsHostInterface: React.FC<SinfulSecretsHostInterfaceProps> = ({
  gameCode,
  hostName
}) => {
  const [chatMessages, setChatMessages] = useState<Array<{id: string; sender: string; text: string; timestamp: Date}>>([]);
  const [isAiLoading, setIsAiLoading] = useState<boolean>(false);

  // Use the real database hook
  const {
    game,
    players,
    cardSubmissions,
    currentChallenge,
    currentPrompt,
    isLoading,
    error,
    submitCard,
    selectChallengeNumber
  } = useSinfulSecretsGame(gameCode);

  const currentGamePhase = game?.game_phase || 'card-submission';
  const currentPlayer = game && players.length > 0 ? players[game.current_player_index] : null;

  // Get current judge info
  const currentJudge = game?.current_judge_id ? players.find(p => p.id === game.current_judge_id) : null;

  // Initialize with welcome message and generate first card prompt
  useEffect(() => {
    if (game && game.status === 'active' && chatMessages.length === 0) {
      const welcomeMessage = {
        id: '1',
        sender: 'host',
        text: 'Welcome to Sinful Secrets! We\'ll be playing a hybrid game - card rounds judged by me, alternating with spicy personal challenges. Let\'s start with our first card prompt! 😈',
        timestamp: new Date()
      };
      setChatMessages([welcomeMessage]);
      
      // Generate first card prompt if none exists
      if (!game.current_prompt && currentGamePhase === 'card-submission') {
        generateCardPrompt();
      }
    }
  }, [game, chatMessages.length, currentGamePhase]);

  const generateCardPrompt = () => {
    const prompts = [
      "Complete this sentence: 'The most embarrassing thing that happened to me on a date was...'",
      "Fill in the blank: 'I would never tell my parents about the time I...'",
      "Finish this: 'The wildest place I've ever kissed someone was...'",
      "Complete: 'If I could have a superpower in the bedroom, it would be...'",
      "Fill in: 'The most romantic thing someone has ever done for me was...'",
      "Finish: 'My biggest turn-on that I'm embarrassed to admit is...'",
      "Complete: 'The craziest thing I've done to get someone's attention was...'",
      "Fill in: 'If I wrote a book about my love life, the title would be...'"
    ];
    
    const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)];

    // Update the database with the current prompt (this will trigger real-time updates)
    if (game) {
      supabase
        .from('sinful_secrets_games')
        .update({ current_prompt: randomPrompt })
        .eq('id', game.id)
        .then(({ error }) => {
          if (error) {
            console.error('Failed to update prompt:', error);
          } else {
            console.log('Prompt updated in database:', randomPrompt);
          }
        });
    }
  };

  const callAI = async (userMessage: string, context: string = '') => {
    if (isAiLoading) return "I'm still thinking about the last message...";
    
    setIsAiLoading(true);
    try {
      const response = await fetch('/api/ai-chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: userMessage,
          context: `You are the seductive, playful host of Sinful Secrets. ${context}`,
          gamePhase: currentGamePhase,
          playerCount: players.length
        })
      });

      if (!response.ok) throw new Error('AI service unavailable');
      
      const data = await response.json();
      return data.response || "I'm feeling a bit speechless right now... 😉";
    } catch (error) {
      console.error('AI call failed:', error);
      return "Oops, I got a bit distracted there! What were we talking about? 😘";
    } finally {
      setIsAiLoading(false);
    }
  };



  const handleWinnerSelection = async (submission: any) => {
    if (!game) return;

    console.log('Selecting winner:', submission);

    try {
      // Mark submission as winner
      const { error: updateError } = await supabase
        .from('sinful_secrets_card_submissions')
        .update({ is_winner: true })
        .eq('game_id', game.id)
        .eq('id', submission.id); // Use submission ID instead of player_id

      if (updateError) {
        console.error('Error updating winner:', updateError);
        throw updateError;
      }

      // Update player score only if it's not a deck card
      if (!submission.is_deck_card && submission.player_id) {
        const { error: scoreError } = await supabase
          .from('sinful_secrets_players')
          .update({ score: supabase.sql`score + 1` })
          .eq('id', submission.player_id);

        if (scoreError) {
          console.error('Error updating score:', scoreError);
          throw scoreError;
        }
      }

      // Replace submitted cards for all players who submitted
      await replaceSubmittedCards();

      // Advance to sinful secrets phase
      const { error: gameError } = await supabase
        .from('sinful_secrets_games')
        .update({
          game_phase: 'sinful-secrets',
          is_card_phase: false
        })
        .eq('id', game.id);

      if (gameError) throw gameError;

      // AI announces winner and transitions
      const aiResponse = await callAI(
        `${submission.player_name} won this round with: "${submission.card_text}"`,
        'Announce the winner and transition to sinful secrets phase'
      );

      const aiMessage = {
        id: Date.now().toString(),
        sender: 'host',
        text: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);

      toast.success(`${submission.player_name} wins this round! Cards replaced for next round.`);
    } catch (error) {
      toast.error("Failed to select winner");
      console.error('Winner selection error:', error);
    }
  };

  const replaceSubmittedCards = async () => {
    if (!game) return;

    try {
      // Get all player submissions for this round (excluding deck cards)
      const playerSubmissions = cardSubmissions.filter(sub => !sub.is_deck_card && sub.player_id);

      console.log('Replacing cards for submissions:', playerSubmissions);

      // For each player who submitted a card, replace it in their hand
      for (const submission of playerSubmissions) {
        if (!submission.player_id) continue;

        // Get a new random white card that's not already in the player's hand
        const { data: whiteCards, error: cardError } = await supabase
          .from('white_cards')
          .select('card_text')
          .limit(50); // Get a pool of cards to choose from

        if (cardError) {
          console.error('Failed to get white cards:', cardError);
          continue;
        }

        // Get player's current hand
        const { data: currentHand, error: handError } = await supabase
          .from('sinful_secrets_player_hands')
          .select('card_text')
          .eq('player_id', submission.player_id)
          .eq('game_id', game.id);

        if (handError) {
          console.error('Failed to get player hand:', handError);
          continue;
        }

        const currentCards = currentHand.map(h => h.card_text);
        const availableCards = whiteCards.filter(card => !currentCards.includes(card.card_text));

        if (availableCards.length === 0) {
          console.warn('No available cards for replacement');
          continue;
        }

        const newCard = availableCards[Math.floor(Math.random() * availableCards.length)];

        // Replace the submitted card in the player's hand
        const { error: replaceError } = await supabase
          .from('sinful_secrets_player_hands')
          .update({ card_text: newCard.card_text })
          .eq('player_id', submission.player_id)
          .eq('game_id', game.id)
          .eq('card_text', submission.card_text);

        if (replaceError) {
          console.error('Failed to replace card:', replaceError);
        } else {
          console.log(`Replaced "${submission.card_text}" with "${newCard.card_text}" for player ${submission.player_id}`);
        }
      }

    } catch (error) {
      console.error('Failed to replace submitted cards:', error);
    }
  };

  const handleCompleteSinfulSecrets = async () => {
    if (!game) return;

    try {
      const nextPlayerIndex = (game.current_player_index + 1) % players.length;
      
      if (nextPlayerIndex === 0) {
        // All players have gone, check if game should end
        const newRound = game.current_round + 1;

        // End game after 5 rounds or if host chooses
        if (newRound > 5) {
          await endGame();
          return;
        }

        // Advance to next round
        const { error: roundError } = await supabase
          .from('sinful_secrets_games')
          .update({
            current_round: newRound,
            current_player_index: 0,
            game_phase: 'card-submission',
            is_card_phase: true,
            // Update intensity level based on round
            intensity_level: newRound <= 2 ? 'Playful' :
                           newRound <= 3 ? 'Sensual' :
                           newRound <= 4 ? 'Daring' : 'Bold'
          })
          .eq('id', game.id);

        if (roundError) throw roundError;

        // Generate new card prompt for next round
        generateCardPrompt();

        // Announce new round
        const aiResponse = await callAI(
          `Starting round ${newRound}! The intensity is now ${newRound <= 2 ? 'Playful' : newRound <= 3 ? 'Sensual' : newRound <= 4 ? 'Daring' : 'Bold'}`,
          'Announce the new round and intensity level'
        );

        const aiMessage = {
          id: Date.now().toString(),
          sender: 'host',
          text: aiResponse,
          timestamp: new Date()
        };
        setChatMessages(prev => [...prev, aiMessage]);
      } else {
        // Move to next player
        const { error: playerError } = await supabase
          .from('sinful_secrets_games')
          .update({ 
            current_player_index: nextPlayerIndex,
            game_phase: 'card-submission',
            is_card_phase: true
          })
          .eq('id', game.id);

        if (playerError) throw playerError;
        
        // Generate new card prompt
        generateCardPrompt();
      }

      const aiResponse = await callAI(
        'Sinful secrets challenge completed! Let\'s get back to our card game.',
        'Transition back to card phase'
      );

      const aiMessage = {
        id: Date.now().toString(),
        sender: 'host',
        text: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      toast.error("Failed to complete challenge");
      console.error('Complete challenge error:', error);
    }
  };

  const endGame = async () => {
    if (!game) return;

    try {
      // Mark game as completed
      const { error: gameError } = await supabase
        .from('sinful_secrets_games')
        .update({
          status: 'completed',
          game_phase: 'sinful-secrets'
        })
        .eq('id', game.id);

      if (gameError) throw gameError;

      // Calculate final scores and announce winner
      const sortedPlayers = [...players].sort((a, b) => b.score - a.score);
      const winner = sortedPlayers[0];

      const aiResponse = await callAI(
        `Game completed! Final scores: ${sortedPlayers.map(p => `${p.player_name}: ${p.score}`).join(', ')}. ${winner.player_name} wins with ${winner.score} points!`,
        'Announce the game completion and final winner'
      );

      const aiMessage = {
        id: Date.now().toString(),
        sender: 'host',
        text: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);

      toast.success(`Game completed! ${winner.player_name} wins!`);
    } catch (error) {
      console.error('Failed to end game:', error);
      toast.error('Failed to end game');
    }
  };

  const handlePlayerNumberSelection = async (playerId: string, number: number) => {
    try {
      await selectChallengeNumber(playerId, number);
      
      const aiResponse = await callAI(
        `Player selected number ${number}. Generate an appropriate challenge.`,
        'Generate a sinful secrets challenge based on the selected number'
      );

      const aiMessage = {
        id: Date.now().toString(),
        sender: 'host',
        text: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Number selection error:', error);
    }
  };

  const handleSendMessage = async (message: string) => {
    const aiResponse = await callAI(message, 'Respond as the flirty game host');
    
    const aiMessage = {
      id: Date.now().toString(),
      sender: 'host',
      text: aiResponse,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, aiMessage]);
  };

  if (isLoading || !game) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-tnt-hotPink mb-4"></div>
          <p className="text-tnt-whiteSmoke">Loading game...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack">
        <div className="text-center text-tnt-whiteSmoke">
          <h2 className="text-2xl font-bold mb-4">Game Error</h2>
          <p className="mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Reload Game</Button>
        </div>
      </div>
    );
  }

  // Don't render if game is not active yet
  if (game.status !== 'active') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack">
        <div className="text-center text-tnt-whiteSmoke">
          <h2 className="text-2xl font-bold mb-4">Game Not Started</h2>
          <p className="mb-4">Waiting for host to start the game...</p>
          <Button onClick={() => window.location.reload()}>Refresh</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke flex flex-col">
      <GameHeader 
        title="Sinful Secrets Host"
        subtitle={`Game: ${gameCode} | Round: ${game?.current_round || 1}`}
      />
      
      <div className="container mx-auto px-4 py-4 flex-grow flex flex-col">


        <div className="flex-grow flex gap-6">
          {/* Center - AI Chat (Primary Focus) */}
          <div className="flex-1 max-w-2xl">
            <div className="bg-tnt-deepPurple/20 rounded-lg p-3 mb-4">
              <h2 className="text-lg font-bold text-tnt-hotPink">🤖 AI Host</h2>
            </div>
            <AIChat
              messages={chatMessages}
              onSendMessage={handleSendMessage}
              isLoading={isAiLoading}
              placeholder="Chat with your AI host..."
            />
          </div>

          {/* Right side - Game Phase Display (Compact) */}
          <div className="w-96">
            <div className="bg-tnt-charcoal/50 rounded-lg p-4">
              <h3 className="text-lg font-bold text-tnt-pink mb-3">Game Status</h3>

              {/* Current Black Card Display */}
              {game.current_prompt && (
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-tnt-whiteSmoke/80 mb-2">Current Prompt:</h4>
                  <div className="bg-tnt-charcoal text-tnt-ivory border-2 border-tnt-gold rounded-lg p-3 text-sm">
                    {game.current_prompt}
                  </div>
                </div>
              )}
            {/* Render appropriate phase component */}
            {currentGamePhase === 'card-submission' && (
              <CardGamePhase
                currentPrompt={game.current_prompt || ""}
                submissions={cardSubmissions.map(sub => ({
                  playerId: sub.player_id,
                  card: sub.card_text,
                  isDeckCard: sub.is_deck_card || false
                }))}
                phase="submission"
                onSelectWinner={() => {}}
                totalPlayers={players.length}
                currentJudge={currentJudge}
              />
            )}

            {currentGamePhase === 'card-judging' && (
              <>
                {/* Debug Info */}
                <div className="mb-4 p-4 bg-tnt-deepPurple/20 rounded-lg">
                  <p className="text-sm text-tnt-whiteSmoke/60">
                    Host Debug: Game Phase = {currentGamePhase}, Submissions = {cardSubmissions.length}
                  </p>
                  <p className="text-xs text-tnt-whiteSmoke/40">
                    Submissions: {JSON.stringify(cardSubmissions.map(s => ({ id: s.id, text: s.card_text.substring(0, 20) + '...', player: s.player_name })), null, 2)}
                  </p>
                </div>
                <HostCardJudgingInterface
                  currentPrompt={game.current_prompt || ""}
                  submissions={cardSubmissions}
                  onSelectWinner={handleWinnerSelection}
                  currentJudge={currentJudge}
                />
              </>
            )}

            {currentGamePhase === 'sinful-secrets' && currentPlayer && (
              <SinfulSecretsPhase
                currentPlayer={{
                  id: currentPlayer.id,
                  name: currentPlayer.player_name,
                  gender: currentPlayer.gender,
                  hasPlayed: currentPlayer.has_played,
                  penaltyDrinks: currentPlayer.penalty_drinks,
                  safeWordUsage: currentPlayer.safe_word_usage,
                  score: currentPlayer.score
                }}
                currentPrompt={currentPrompt || ""}
                onCompleteChallenge={handleCompleteSinfulSecrets}
                onPlayerNumberSelection={(playerId, number, category) => {
                  handlePlayerNumberSelection(playerId, number);
                }}
                pendingTruthDare={false}
                onTruthDareSelection={() => {}}
              />
            )}
            </div>
          </div>
        </div>

        {/* Bottom controls */}
        <div className="mt-4 flex justify-between items-center">
          <div className="flex gap-2">
            <QRCodeJoin gameCode={gameCode} />
            <AdminControlButton 
              onAction={() => console.log('Admin controls')}
              label="Settings"
            />
          </div>
          
          <div className="text-sm text-tnt-whiteSmoke/60">
            Players: {players.length} | Phase: {currentGamePhase}
          </div>
        </div>
      </div>

      <GameFooter
        players={players.map(p => p.player_name)}
        onEndGame={() => {
          if (confirm('Are you sure you want to end the game?')) {
            window.location.href = '/play';
          }
        }}
      />
    </div>
  );
};

export default SinfulSecretsHostInterface;
