import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import GameCard from '@/components/GameCard';

interface CardSubmission {
  id: string;
  player_id: string | null;
  card_text: string;
  is_deck_card: boolean;
  player_name: string;
}

interface HostCardJudgingInterfaceProps {
  currentPrompt: string;
  submissions: CardSubmission[];
  onSelectWinner: (submission: CardSubmission) => void;
  currentJudge?: { id: string; player_name: string } | null;
}

const HostCardJudgingInterface: React.FC<HostCardJudgingInterfaceProps> = ({
  currentPrompt,
  submissions,
  onSelectWinner,
  currentJudge
}) => {
  const [revealedCards, setRevealedCards] = useState<Set<string>>(new Set());
  const [selectedWinner, setSelectedWinner] = useState<CardSubmission | null>(null);
  const [showResults, setShowResults] = useState(false);

  const handleRevealCard = (submissionId: string) => {
    setRevealedCards(prev => new Set([...prev, submissionId]));
  };

  const handleRevealAll = () => {
    const allIds = submissions.map(sub => sub.id);
    setRevealedCards(new Set(allIds));
  };

  const handleSelectWinner = (submission: CardSubmission) => {
    setSelectedWinner(submission);
    setShowResults(true);
    onSelectWinner(submission);
  };

  const isCardRevealed = (submissionId: string) => revealedCards.has(submissionId);
  const allCardsRevealed = submissions.length > 0 && revealedCards.size === submissions.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-pacifico text-tnt-hotPink mb-4">Card Judging</h2>
        <div className="max-w-md mx-auto mb-4">
          <GameCard
            type="black"
            content={currentPrompt}
            className="w-full"
          />
        </div>
        {currentJudge && (
          <div className="bg-tnt-gold/20 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-center gap-2 mb-2">
              <span className="text-2xl">👑</span>
              <p className="text-lg text-tnt-whiteSmoke">
                <span className="text-tnt-gold font-bold">{currentJudge.player_name}</span> - Click cards to reveal and select the winner!
              </p>
              <span className="text-2xl">👑</span>
            </div>
            <p className="text-sm text-tnt-whiteSmoke/80">
              Judge: Use the host screen to reveal cards and choose your favorite response
            </p>
          </div>
        )}
      </div>

      {/* Control Buttons */}
      <div className="flex justify-center gap-4">
        <Button
          onClick={handleRevealAll}
          disabled={allCardsRevealed}
          className="bg-tnt-gold hover:bg-tnt-gold/80 text-tnt-charcoal font-bold"
        >
          🎭 Reveal All Cards
        </Button>
      </div>

      {/* Submitted Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {submissions.map((submission, index) => {
          const isRevealed = isCardRevealed(submission.id);
          const isWinner = selectedWinner?.id === submission.id;
          
          return (
            <div 
              key={submission.id} 
              className={`relative transition-all duration-500 ${
                isWinner ? 'scale-110 z-10' : ''
              }`}
            >
              {/* Card Container */}
              <div className="relative">
                {/* Face-down card */}
                {!isRevealed && (
                  <div 
                    className="cursor-pointer transform transition-all duration-300 hover:scale-105"
                    onClick={() => handleRevealCard(submission.id)}
                  >
                    <GameCard 
                      type="white" 
                      content="Click to reveal..."
                      className="bg-gradient-to-br from-tnt-deepPurple to-tnt-charcoal border-2 border-tnt-gold/50"
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-6xl opacity-20">🎭</div>
                    </div>
                  </div>
                )}

                {/* Face-up card with flip animation */}
                {isRevealed && (
                  <div className={`transform transition-all duration-500 ${
                    isRevealed ? 'rotateY-0' : 'rotateY-180'
                  }`}>
                    <GameCard 
                      type="white" 
                      content={submission.card_text}
                      className={`${
                        isWinner 
                          ? 'ring-4 ring-tnt-gold shadow-2xl shadow-tnt-gold/50' 
                          : 'hover:scale-105 cursor-pointer'
                      } transition-all duration-300`}
                      onClick={() => !showResults && handleSelectWinner(submission)}
                    />
                  </div>
                )}

                {/* Winner Badge */}
                {isWinner && (
                  <div className="absolute -top-3 -right-3 bg-tnt-gold text-tnt-charcoal rounded-full p-2 font-bold text-sm animate-bounce">
                    🏆 WINNER!
                  </div>
                )}
              </div>

              {/* Player Info (shown after reveal) */}
              {isRevealed && (
                <div className="mt-3 text-center">
                  <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${
                    submission.is_deck_card 
                      ? 'bg-tnt-deepPurple/50 text-tnt-whiteSmoke' 
                      : 'bg-tnt-hotPink/20 text-tnt-hotPink'
                  }`}>
                    {submission.is_deck_card ? '🎲 Random Card' : `👤 ${submission.player_name}`}
                  </div>
                  
                  {/* Select Winner Button */}
                  {!showResults && (
                    <Button
                      onClick={() => handleSelectWinner(submission)}
                      className="mt-2 w-full bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold"
                      size="sm"
                    >
                      🏆 Select as Winner
                    </Button>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Results Display */}
      {showResults && selectedWinner && (
        <div className="mt-8 text-center bg-tnt-deepPurple/20 rounded-lg p-6">
          <h3 className="text-2xl font-bold text-tnt-gold mb-4">🎉 Round Winner! 🎉</h3>
          <div className="max-w-md mx-auto mb-4">
            <GameCard 
              type="white" 
              content={selectedWinner.card_text}
              className="ring-4 ring-tnt-gold"
            />
          </div>
          <p className="text-xl text-tnt-whiteSmoke">
            {selectedWinner.is_deck_card ? (
              <span>The <span className="text-tnt-gold font-bold">Random Card</span> wins this round!</span>
            ) : (
              <span><span className="text-tnt-hotPink font-bold">{selectedWinner.player_name}</span> wins this round!</span>
            )}
          </p>
          {!selectedWinner.is_deck_card && (
            <p className="text-sm text-tnt-whiteSmoke/80 mt-2">
              +1 point awarded to {selectedWinner.player_name}
            </p>
          )}
        </div>
      )}

      {/* Instructions */}
      {!allCardsRevealed && submissions.length > 0 && (
        <div className="text-center text-sm text-tnt-whiteSmoke/60">
          <p>Click individual cards to reveal them, or use "Reveal All Cards" button</p>
          <p>Once revealed, click a card or "Select as Winner" to choose the winner</p>
        </div>
      )}
    </div>
  );
};

export default HostCardJudgingInterface;
