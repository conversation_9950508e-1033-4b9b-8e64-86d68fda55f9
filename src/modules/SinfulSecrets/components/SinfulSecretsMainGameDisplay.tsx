import React from 'react';
import SinfulSecretsHostInterface from './SinfulSecretsHostInterface';

interface SinfulSecretsMainGameDisplayProps {
  gameCode: string;
  hostName: string;
  onShowHostControls: () => void;
}

const SinfulSecretsMainGameDisplay: React.FC<SinfulSecretsMainGameDisplayProps> = ({
  gameCode,
  hostName,
  onShowHostControls
}) => {
  // Simply render the consolidated host interface
  return (
    <SinfulSecretsHostInterface
      gameCode={gameCode}
      hostName={hostName}
    />
  );
};

export default SinfulSecretsMainGameDisplay;
