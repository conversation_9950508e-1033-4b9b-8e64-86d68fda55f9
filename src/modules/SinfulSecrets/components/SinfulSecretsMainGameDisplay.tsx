import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Settings, Crown, Users, MessageCircle, Sparkles } from 'lucide-react';
import { useSinfulSecretsGame } from '@/hooks/useSinfulSecretsGame';
import { supabase } from '@/integrations/supabase/client';
import AIChat from '@/components/AIChat';
import BlackCard from '@/components/game/BlackCard';
import QRCodeJoin from '@/components/QRCodeJoin';
import CardJudgingInterface from './CardJudgingInterface';

interface SinfulSecretsMainGameDisplayProps {
  gameCode: string;
  hostName: string;
  onShowHostControls: () => void;
}

const SinfulSecretsMainGameDisplay: React.FC<SinfulSecretsMainGameDisplayProps> = ({
  gameCode,
  hostName,
  onShowHostControls
}) => {
  const [showQRCode, setShowQRCode] = useState(false);
  const [chatMessages, setChatMessages] = useState<Array<{
    id: string;
    sender: string;
    text: string;
    timestamp: Date;
  }>>([]);

  const {
    game,
    players,
    cardSubmissions,
    currentPrompt,
    isLoading,
    error
  } = useSinfulSecretsGame(gameCode);

  const currentGamePhase = game?.game_phase || 'card-submission';
  const nonHostPlayers = players.filter(p => !p.is_host);
  const submittedCount = cardSubmissions?.length || 0;
  const totalPlayers = nonHostPlayers.length;

  // Check if all cards are submitted and should move to judging
  useEffect(() => {
    if (currentGamePhase === 'card-submission' &&
        submittedCount > 0 &&
        submittedCount === totalPlayers &&
        totalPlayers > 0) {
      // All cards submitted, move to judging phase
      setTimeout(async () => {
        try {
          const { error } = await supabase
            .from('sinful_secrets_games')
            .update({ game_phase: 'judging' })
            .eq('id', game?.id);

          if (error) throw error;

          const newMessage = {
            id: 'judging-start',
            sender: 'AI Host',
            text: "All cards are in! Time for me to judge these delicious responses! 😈🎭",
            timestamp: new Date()
          };
          setChatMessages(prev => [...prev, newMessage]);
        } catch (error) {
          console.error('Failed to update game phase:', error);
        }
      }, 2000);
    }
  }, [currentGamePhase, submittedCount, totalPlayers, game?.id]);

  // Initialize AI host welcome message
  useEffect(() => {
    if (game && players.length > 0 && chatMessages.length === 0) {
      const initializeAIWelcome = async () => {
        try {
          const { data, error } = await supabase.functions.invoke('ai-chat', {
            body: {
              message: `Welcome the players to Sinful Secrets! We have ${players.length} players ready to play.`,
              gameContext: `Game start, ${game.intensity_level || 'Sensual'} intensity, ${players.length} players`,
              playerName: 'System'
            }
          });

          const welcomeMessage = {
            id: 'welcome',
            sender: 'AI Host',
            text: data?.response || `Welcome to Sinful Secrets! I'm your AI host for tonight's adventure. I see we have ${players.length} brave souls ready to share their secrets. Let's start with some card submissions to warm up! 😈`,
            timestamp: new Date()
          };
          setChatMessages([welcomeMessage]);
        } catch (error) {
          console.error('Failed to get AI welcome message:', error);
          const welcomeMessage = {
            id: 'welcome',
            sender: 'AI Host',
            text: `Welcome to Sinful Secrets! I'm your AI host for tonight's adventure. I see we have ${players.length} brave souls ready to share their secrets. Let's start with some card submissions to warm up! 😈`,
            timestamp: new Date()
          };
          setChatMessages([welcomeMessage]);
        }
      };

      initializeAIWelcome();
    }
  }, [game, players, chatMessages.length]);

  // Add AI responses based on game events
  useEffect(() => {
    if (currentGamePhase === 'card-submission' && submittedCount > 0 && submittedCount < totalPlayers) {
      const lastMessage = chatMessages[chatMessages.length - 1];
      if (!lastMessage || !lastMessage.text.includes(`${submittedCount} cards`)) {
        const getAIEncouragement = async () => {
          try {
            const { data, error } = await supabase.functions.invoke('ai-chat', {
              body: {
                message: `${submittedCount} out of ${totalPlayers} players have submitted their cards. Encourage the remaining players to submit.`,
                gameContext: `Card submission phase, ${submittedCount}/${totalPlayers} submitted`,
                playerName: 'System'
              }
            });

            const newMessage = {
              id: `submission-${submittedCount}`,
              sender: 'AI Host',
              text: data?.response || `Great start! I've received ${submittedCount} cards so far. Come on, don't be shy! 💋`,
              timestamp: new Date()
            };
            setChatMessages(prev => [...prev, newMessage]);
          } catch (error) {
            console.error('Failed to get AI encouragement:', error);
            const fallbackMessages = [
              `Great start! I've received ${submittedCount} cards so far. Come on, don't be shy! 💋`,
              `${submittedCount} down, ${totalPlayers - submittedCount} to go! I'm excited to see what you've all come up with! 🔥`,
              `The cards are coming in nicely! ${totalPlayers - submittedCount} more players need to submit their responses.`
            ];

            const newMessage = {
              id: `submission-${submittedCount}`,
              sender: 'AI Host',
              text: fallbackMessages[Math.floor(Math.random() * fallbackMessages.length)],
              timestamp: new Date()
            };
            setChatMessages(prev => [...prev, newMessage]);
          }
        };

        getAIEncouragement();
      }
    }
  }, [submittedCount, totalPlayers, currentGamePhase, chatMessages]);

  const sendChatMessage = async (message: string) => {
    const newMessage = {
      id: Date.now().toString(),
      sender: hostName,
      text: message,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, newMessage]);

    // Call actual AI API
    try {
      const { data, error } = await supabase.functions.invoke('ai-chat', {
        body: {
          message,
          gameContext: `Round ${game?.current_round || 1}, ${game?.intensity_level || 'Sensual'} intensity, ${currentGamePhase} phase. Players: ${players.length}`,
          playerName: hostName
        }
      });

      if (error) throw error;

      const aiResponse = {
        id: (Date.now() + 1).toString(),
        sender: 'AI Host',
        text: data.response || data.fallback || "I'm here for all the secrets! 😈",
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('AI call error:', error);
      // Fallback to simulated response if AI fails
      const fallbackResponses = [
        `I hear you, ${hostName}! Let's keep this energy going! 💫`,
        `Interesting point! The game is definitely heating up! 🔥`,
        `Thanks for keeping things lively, ${hostName}! Your players are lucky to have such an engaged host! 😈`,
        `I love the enthusiasm! This is exactly the kind of energy that makes Sinful Secrets so much fun! 💋`
      ];

      const aiResponse = {
        id: (Date.now() + 1).toString(),
        sender: 'AI Host',
        text: fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)],
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiResponse]);
      toast.error('AI host is taking a break, but still here for you! 😏');
    }
  };

  const handleJudgingComplete = async (winnerPlayerId: string) => {
    try {
      // Update game state
      const { error: gameError } = await supabase
        .from('sinful_secrets_games')
        .update({
          game_phase: 'round-end',
          current_round: (game?.current_round || 1) + 1
        })
        .eq('id', game?.id);

      if (gameError) throw gameError;

      // Update player score
      const { error: scoreError } = await supabase
        .from('sinful_secrets_players')
        .update({
          score: supabase.sql`score + 1`
        })
        .eq('id', winnerPlayerId);

      if (scoreError) throw scoreError;

      // Add celebration message
      const winner = players.find(p => p.id === winnerPlayerId);
      const celebrationMessage = {
        id: 'round-winner',
        sender: 'AI Host',
        text: `🎉 Congratulations ${winner?.player_name}! That was absolutely perfect! Ready for the next round? 🔥`,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, celebrationMessage]);

      // Move to next round after celebration
      setTimeout(async () => {
        const { error } = await supabase
          .from('sinful_secrets_games')
          .update({ game_phase: 'card-submission' })
          .eq('id', game?.id);

        if (!error) {
          const nextRoundMessage = {
            id: 'next-round',
            sender: 'AI Host',
            text: `Round ${(game?.current_round || 1) + 1} begins! Let's see what spicy responses you have this time! 😈`,
            timestamp: new Date()
          };
          setChatMessages(prev => [...prev, nextRoundMessage]);
        }
      }, 4000);

    } catch (error) {
      console.error('Failed to complete judging:', error);
      toast.error('Failed to complete judging');
    }
  };

  // Show judging interface when in judging phase
  if (currentGamePhase === 'judging' && cardSubmissions && cardSubmissions.length > 0) {
    return (
      <CardJudgingInterface
        submissions={cardSubmissions.map(sub => ({
          id: sub.id,
          player_id: sub.player_id,
          player_name: players.find(p => p.id === sub.player_id)?.player_name || 'Unknown',
          card_text: sub.card_text
        }))}
        currentPrompt={currentPrompt || 'No prompt available'}
        onJudgingComplete={handleJudgingComplete}
        aiPersona={game?.ai_persona || 'seductive'}
        isAIJudging={true}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke">
      {/* Header */}
      <div className="bg-tnt-deepPurple/20 border-b border-tnt-hotPink/30 p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-pacifico text-tnt-hotPink">Sinful Secrets</h1>
            <div className="text-sm text-tnt-whiteSmoke/60">
              Game Code: <span className="font-mono font-bold text-tnt-whiteSmoke">{gameCode}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center text-sm text-tnt-whiteSmoke/80">
              <Users className="mr-1 h-4 w-4" />
              {players.length} Players
            </div>
            <Button
              onClick={() => setShowQRCode(!showQRCode)}
              variant="outline"
              size="sm"
              className="border-tnt-hotPink/30 hover:border-tnt-hotPink"
            >
              Join QR
            </Button>
            <Button
              onClick={onShowHostControls}
              variant="outline"
              size="sm"
              className="border-tnt-hotPink/30 hover:border-tnt-hotPink"
            >
              <Settings className="mr-1 h-4 w-4" />
              Host Controls
            </Button>
          </div>
        </div>
      </div>

      {/* QR Code Dropdown */}
      {showQRCode && (
        <div className="bg-tnt-deepPurple/30 border-b border-tnt-hotPink/20 p-4">
          <div className="max-w-7xl mx-auto text-center">
            <QRCodeJoin gameCode={gameCode} gameType="sinful-secrets" />
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          
          {/* Left Column - AI Host Chat (Main Focus) */}
          <div className="lg:col-span-2">
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30 h-full flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-tnt-hotPink flex items-center">
                  <MessageCircle className="mr-2 h-5 w-5" />
                  AI Host Chat
                  <Sparkles className="ml-2 h-4 w-4 text-tnt-hotPink animate-pulse" />
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col p-0">
                <div className="flex-1 px-6">
                  <AIChat
                    gameId={game?.id || 'staging'}
                    playerId="host"
                    playerName={hostName}
                    onSendMessage={sendChatMessage}
                    messages={chatMessages}
                    className="h-full"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Game State & Black Card */}
          <div className="space-y-6">
            
            {/* Current Black Card/Prompt */}
            {currentPrompt && (
              <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
                <CardHeader>
                  <CardTitle className="text-tnt-hotPink text-center">Current Prompt</CardTitle>
                </CardHeader>
                <CardContent>
                  <BlackCard prompt={currentPrompt} />
                </CardContent>
              </Card>
            )}

            {/* Game Status */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">Game Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Phase:</span>
                  <span className="font-bold text-tnt-hotPink capitalize">
                    {currentGamePhase.replace('-', ' ')}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span>Round:</span>
                  <span className="font-bold">{game?.current_round || 1}</span>
                </div>

                {currentGamePhase === 'card-submission' && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span>Cards Submitted:</span>
                      <span className="font-bold">{submittedCount}/{totalPlayers}</span>
                    </div>
                    <div className="w-full bg-tnt-charcoal/50 rounded-full h-2">
                      <div 
                        className="bg-tnt-hotPink h-2 rounded-full transition-all duration-300"
                        style={{ width: `${totalPlayers > 0 ? (submittedCount / totalPlayers) * 100 : 0}%` }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Players List */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">Players</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {players.map((player) => (
                    <div
                      key={player.id}
                      className="flex items-center justify-between p-2 bg-tnt-charcoal/30 rounded"
                    >
                      <div className="flex items-center">
                        {player.is_host && <Crown className="mr-1 h-3 w-3 text-tnt-hotPink" />}
                        <span className="text-sm">{player.player_name}</span>
                      </div>
                      <div className={`w-2 h-2 rounded-full ${
                        player.is_active ? 'bg-green-500' : 'bg-gray-500'
                      }`} />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Submitted Cards Preview */}
            {currentGamePhase === 'card-submission' && cardSubmissions && cardSubmissions.length > 0 && (
              <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
                <CardHeader>
                  <CardTitle className="text-tnt-hotPink">Submitted Cards</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {cardSubmissions.map((submission, index) => (
                      <div
                        key={submission.id}
                        className="p-2 bg-tnt-charcoal/30 rounded text-sm"
                      >
                        <span className="text-tnt-whiteSmoke/60">Player {index + 1}:</span>
                        <span className="ml-2 text-tnt-whiteSmoke">{submission.card_text}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      {error && (
        <div className="fixed bottom-4 right-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-400">{error}</p>
        </div>
      )}
    </div>
  );
};

export default SinfulSecretsMainGameDisplay;
