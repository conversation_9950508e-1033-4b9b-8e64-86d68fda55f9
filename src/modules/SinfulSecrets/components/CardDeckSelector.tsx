
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { getCardPacks, CardPack, saveCustomCardPack } from "@/services/packService";
import { supabase } from '@/integrations/supabase/client';
import { toast } from "sonner";
import { Package, Sparkles, CheckCircle2, Loader2, Settings, Users, Plus, AlertCircle } from "lucide-react";
import CustomDeckManager from './CustomDeckManager';
import CommunityDecks from './CommunityDecks';

interface CardDeckSelectorProps {
  onDecksSelected: (packIds: string[], customDeck?: CardPack) => void;
  onBack: () => void;
}

const CardDeckSelector: React.FC<CardDeckSelectorProps> = ({ onDecksSelected, onBack }) => {
  const [availablePacks, setAvailablePacks] = useState<CardPack[]>([]);
  const [selectedPacks, setSelectedPacks] = useState<string[]>(['original']);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [customPrompt, setCustomPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showCustomManager, setShowCustomManager] = useState(false);
  const [showCommunityDecks, setShowCommunityDecks] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // Save deck state
  const [showSaveDeck, setShowSaveDeck] = useState(false);
  const [saveData, setSaveData] = useState({
    name: '',
    description: '',
    isPublic: false
  });
  const [lastGeneratedDeck, setLastGeneratedDeck] = useState<{blackCards: string[], whiteCards: string[]} | null>(null);
  
  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setIsAuthenticated(!!session?.user);
    };
    
    checkAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setIsAuthenticated(!!session?.user);
    });

    return () => subscription.unsubscribe();
  }, []);
  
  // Load available card packs
  useEffect(() => {
    loadPacks();
  }, []);
  
  const loadPacks = async () => {
    try {
      const packs = await getCardPacks();
      setAvailablePacks(packs);
    } catch (error) {
      console.error("Failed to load card packs:", error);
      toast.error("Failed to load card packs");
    } finally {
      setIsLoading(false);
    }
  };
  
  const togglePack = (packId: string) => {
    setSelectedPacks((current) => {
      if (current.includes(packId)) {
        if (current.length <= 1) {
          toast.error("You must select at least one pack");
          return current;
        }
        return current.filter(id => id !== packId);
      } else {
        return [...current, packId];
      }
    });
  };

  const generateCustomDeck = async () => {
    if (!customPrompt.trim()) {
      toast.error("Please enter a description for your custom deck");
      return;
    }

    setIsGenerating(true);
    try {
      const { data, error } = await supabase.functions.invoke('ai-deck-generator', {
        body: {
          prompt: customPrompt,
          deckType: 'sinful-secrets',
          cardCount: { black: 8, white: 15 }
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      setLastGeneratedDeck(data);
      setSaveData(prev => ({
        ...prev,
        name: `Custom: ${customPrompt.slice(0, 30)}${customPrompt.length > 30 ? '...' : ''}`,
        description: customPrompt
      }));
      
      if (isAuthenticated) {
        setShowSaveDeck(true);
        toast.success("Custom deck generated! You can save it or use it directly.");
      } else {
        toast.success("Custom deck generated! You can use it for this session. Sign up to save decks permanently.");
      }
    } catch (error) {
      console.error('Failed to generate custom deck:', error);
      toast.error('Failed to generate custom deck. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveGeneratedDeck = async () => {
    if (!lastGeneratedDeck || !saveData.name.trim()) {
      toast.error("Please enter a name for your deck");
      return;
    }

    if (!isAuthenticated) {
      toast.error("Please sign up or log in to save custom decks");
      return;
    }

    try {
      const savedDeck = await saveCustomCardPack(
        saveData.name,
        saveData.description,
        lastGeneratedDeck.blackCards,
        lastGeneratedDeck.whiteCards,
        saveData.isPublic
      );

      if (savedDeck) {
        toast.success("Custom deck saved successfully!");
        setShowSaveDeck(false);
        setLastGeneratedDeck(null);
        setCustomPrompt('');
        setSaveData({ name: '', description: '', isPublic: false });
        
        // Reload packs to include the new saved deck
        await loadPacks();
        
        // Auto-select the new deck
        setSelectedPacks(prev => [...prev, savedDeck.id]);
      }
    } catch (error) {
      console.error('Failed to save deck:', error);
      toast.error('Failed to save deck. Please try again.');
    }
  };

  const handleUseGeneratedDeckDirectly = () => {
    if (!lastGeneratedDeck) return;

    const customDeck: CardPack = {
      id: `temp-custom-${Date.now()}`,
      packName: "Temporary AI Generated Deck",
      description: customPrompt,
      category: "custom",
      blackCards: lastGeneratedDeck.blackCards,
      whiteCards: lastGeneratedDeck.whiteCards,
      isCustom: true
    };

    onDecksSelected(selectedPacks, customDeck);
  };
  
  const handleConfirm = () => {
    if (selectedPacks.length === 0) {
      toast.error("You must select at least one pack");
      return;
    }
    
    onDecksSelected(selectedPacks);
  };
  
  const getCategoryColor = (category: string) => {
    switch(category) {
      case "base": return "bg-blue-600 text-white";
      case "adult": return "bg-tnt-crimson text-white";
      case "party": return "bg-tnt-gold text-tnt-charcoal";
      case "custom": return "bg-purple-600 text-white";
      case "original": return "bg-blue-600 text-white";
      case "dare": return "bg-tnt-pink text-white";
      default: return "bg-gray-600 text-white";
    }
  };

  if (showCustomManager) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-white p-6">
        <div className="max-w-6xl mx-auto">
          <CustomDeckManager onClose={() => setShowCustomManager(false)} />
        </div>
      </div>
    );
  }

  if (showCommunityDecks) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-white p-6">
        <div className="max-w-6xl mx-auto">
          <CommunityDecks
            selectedPacks={selectedPacks}
            onTogglePack={togglePack}
            onClose={() => setShowCommunityDecks(false)}
          />
        </div>
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack">
        <div className="w-12 h-12 border-4 border-tnt-pink border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-white p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-pacifico text-tnt-hotPink mb-2">Choose Your Cards</h1>
            <p className="text-lg text-tnt-whiteSmoke/80">Select existing decks or create custom ones</p>
          </div>
          <div className="flex gap-4">
            {isAuthenticated ? (
              <>
                <Button onClick={() => setShowCustomManager(true)} variant="tntOutline" size="lg">
                  <Settings className="mr-2 h-5 w-5" />
                  My Decks
                </Button>
                <Button onClick={() => setShowCommunityDecks(true)} variant="tntOutline" size="lg">
                  <Users className="mr-2 h-5 w-5" />
                  Community
                </Button>
              </>
            ) : (
              <div className="bg-yellow-600/20 border border-yellow-600/30 rounded-lg p-3 flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-yellow-400" />
                <span className="text-sm text-yellow-200">
                  Sign up to save and manage custom decks
                </span>
              </div>
            )}
            <Button onClick={onBack} variant="tntOutline" size="lg">
              Back to Setup
            </Button>
          </div>
        </div>

        {/* Save Generated Deck Modal */}
        {showSaveDeck && lastGeneratedDeck && isAuthenticated && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <Card className="bg-gray-800 border-gray-700 max-w-md w-full">
              <CardHeader>
                <CardTitle className="text-white">Save Generated Deck</CardTitle>
                <CardDescription className="text-gray-300">
                  Save this AI-generated deck to use again later
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Deck Name
                  </label>
                  <Input
                    value={saveData.name}
                    onChange={(e) => setSaveData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter deck name"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description
                  </label>
                  <Textarea
                    value={saveData.description}
                    onChange={(e) => setSaveData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe your deck"
                    className="bg-gray-700 border-gray-600 text-white"
                    rows={3}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={saveData.isPublic}
                    onCheckedChange={(checked) => setSaveData(prev => ({ ...prev, isPublic: checked }))}
                  />
                  <span className="text-sm text-gray-300">Make this deck public for others to use</span>
                </div>
              </CardContent>
              <CardFooter className="flex gap-2">
                <Button onClick={handleSaveGeneratedDeck} className="flex-1 bg-tnt-hotPink hover:bg-tnt-pink">
                  <Plus className="mr-2 h-4 w-4" />
                  Save Deck
                </Button>
                <Button onClick={handleUseGeneratedDeckDirectly} variant="outline" className="flex-1">
                  Use Without Saving
                </Button>
                <Button onClick={() => setShowSaveDeck(false)} variant="ghost">
                  Cancel
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Existing Decks */}
          <div className="lg:col-span-2">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-white">Available Card Decks</h2>
              <div className="text-sm text-gray-300">
                {selectedPacks.length} deck{selectedPacks.length !== 1 ? 's' : ''} selected
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
              {availablePacks.map((pack) => (
                <Card 
                  key={pack.id} 
                  className={`bg-gray-800 border cursor-pointer transition-all duration-200 ${
                    selectedPacks.includes(pack.id) 
                      ? 'border-tnt-pink shadow-tnt-pink/20 shadow-lg' 
                      : 'border-gray-700 hover:border-gray-600'
                  }`}
                  onClick={() => togglePack(pack.id)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className={`inline-block px-2 py-1 text-xs rounded mb-2 ${getCategoryColor(pack.category)}`}>
                          {pack.isCustom ? 'Custom' : pack.category.charAt(0).toUpperCase() + pack.category.slice(1)}
                        </div>
                        <CardTitle className="text-white text-lg">{pack.packName}</CardTitle>
                      </div>
                      {selectedPacks.includes(pack.id) && (
                        <CheckCircle2 className="text-tnt-pink h-6 w-6" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-300 mb-3">{pack.description}</CardDescription>
                    <div className="text-sm text-gray-400">
                      <div>{pack.blackCards.length} prompt cards</div>
                      <div>{pack.whiteCards.length} response cards</div>
                      {pack.isCustom && pack.isPublic && (
                        <div className="text-green-400 text-xs mt-1">Community deck</div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <Checkbox 
                      checked={selectedPacks.includes(pack.id)}
                      onCheckedChange={() => togglePack(pack.id)}
                      id={`pack-${pack.id}`}
                      className="data-[state=checked]:bg-tnt-pink data-[state=checked]:border-tnt-pink"
                    />
                    <label htmlFor={`pack-${pack.id}`} className="ml-2 text-sm text-gray-300 cursor-pointer">
                      {selectedPacks.includes(pack.id) ? 'Selected' : 'Select this deck'}
                    </label>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>

          {/* AI Custom Generator */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-800 border-gray-700 h-fit">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-tnt-hotPink" />
                  AI Custom Deck Generator
                </CardTitle>
                <CardDescription className="text-gray-300">
                  Create a personalized deck with AI that matches Cards Against Humanity's irreverent style
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Describe your ideal deck:
                  </label>
                  <Textarea
                    placeholder="e.g., 'Create spicy cards about dating apps and modern romance with cheeky humor' or 'Make cards about college life with party themes and relationship drama'"
                    value={customPrompt}
                    onChange={(e) => setCustomPrompt(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 min-h-[120px]"
                    disabled={isGenerating}
                  />
                </div>
                
                <div className="bg-tnt-deepPurple/20 rounded p-3 text-xs text-gray-300">
                  <strong>AI will create:</strong> Cards with Cards Against Humanity's signature irreverent, cheeky style but tailored to your theme. Perfect for spicy adult party games!
                </div>

                {!isAuthenticated && (
                  <div className="bg-yellow-600/20 border border-yellow-600/30 rounded p-3 text-xs text-yellow-200">
                    <strong>Note:</strong> Generated decks can be used immediately but won't be saved permanently. Sign up to save custom decks!
                  </div>
                )}

                <Button
                  onClick={generateCustomDeck}
                  disabled={isGenerating || !customPrompt.trim()}
                  className="w-full bg-tnt-hotPink hover:bg-tnt-pink text-white"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Cards...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate Custom Deck
                    </>
                  )}
                </Button>

                {lastGeneratedDeck && !isAuthenticated && (
                  <Button
                    onClick={handleUseGeneratedDeckDirectly}
                    variant="outline"
                    className="w-full"
                  >
                    Use Generated Deck
                  </Button>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex justify-center mt-8">
          <Button 
            onClick={handleConfirm}
            className="bg-tnt-hotPink hover:bg-tnt-pink text-white px-8 py-3 text-lg"
            disabled={selectedPacks.length === 0}
            size="lg"
          >
            <Package className="mr-2 h-5 w-5" />
            Start Game with Selected Decks
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CardDeckSelector;
