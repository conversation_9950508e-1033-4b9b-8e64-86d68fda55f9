
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { useSinfulSecretsGame } from '@/hooks/useSinfulSecretsGame';
import { supabase } from '@/integrations/supabase/client';
import PlayerCardInterface from './PlayerCardInterface';
import PlayerJudgingInterface from './PlayerJudgingInterface';

interface SinfulSecretsPlayerInterfaceProps {
  playerName: string;
  playerId: string;
  gameCode: string;
}

const SinfulSecretsPlayerInterface: React.FC<SinfulSecretsPlayerInterfaceProps> = ({
  playerName,
  playerId,
  gameCode
}) => {
  const [selectedNumber, setSelectedNumber] = useState<number | null>(null);
  const [hasSubmittedCard, setHasSubmittedCard] = useState<boolean>(false);

  // Use the real database hook
  const {
    game,
    players,
    cardSubmissions,
    currentChallenge,
    currentPrompt,
    isLoading,
    error,
    submitCard,
    selectChallengeNumber
  } = useSinfulSecretsGame(gameCode);

  const currentPlayer = players.find(p => p.id === playerId);
  const isActivePlayer = game && currentPlayer ? players[game.current_player_index]?.id === playerId : false;
  const gamePhase = game?.game_phase || 'card-submission';
  const gameStatus = game?.status || 'waiting';
  const hasPlayerSubmitted = cardSubmissions.some(sub => sub.player_id === playerId);
  const isJudge = game?.current_judge_id === playerId;
  const currentJudge = game?.current_judge_id ? players.find(p => p.id === game.current_judge_id) : null;
  
  // Mock number to category mapping
  const getNumberCategory = (number: number): string => {
    if (number <= 8) return Math.random() < 0.5 ? 'Truth' : 'Dare';
    if (number <= 12) return Math.random() < 0.5 ? 'Truth' : 'Dare';
    if (number <= 16) return 'WouldYouRather';
    return 'Wildcard';
  };

  const handleNumberSelection = async (number: number) => {
    setSelectedNumber(number);
    const category = getNumberCategory(number);

    try {
      await selectChallengeNumber(playerId, number);
      toast.success(`Selected number ${number} (${category})!`);
    } catch (error) {
      toast.error('Failed to select number');
    }
  };

  const handleTruthDareChoice = async (choice: 'Truth' | 'Dare') => {
    if (!game) return;

    try {
      // Update the player's choice in the database
      const { error } = await supabase
        .from('sinful_secrets_challenges')
        .insert({
          game_id: game.id,
          player_id: playerId,
          round_number: game.current_round,
          challenge_type: choice.toLowerCase(),
          selected_number: selectedNumber || 0
        });

      if (error) throw error;
      toast.success(`You chose ${choice}!`);
    } catch (error) {
      toast.error('Failed to record choice');
    }
  };

  const handleSafeWord = async () => {
    if (!game) return;

    try {
      // Increment safe word usage for player
      const { error: playerError } = await supabase
        .from('sinful_secrets_players')
        .update({ safe_word_usage: supabase.sql`safe_word_usage + 1` })
        .eq('id', playerId);

      if (playerError) throw playerError;

      // Log safe word usage
      const { error: logError } = await supabase
        .from('sinful_secrets_challenges')
        .insert({
          game_id: game.id,
          player_id: playerId,
          round_number: game.current_round,
          challenge_type: 'safe_word',
          selected_number: 0
        });

      if (logError) throw logError;

      toast.info(`🔐 Safe word "${game.safe_word}" used! Skipping to next prompt...`);
    } catch (error) {
      toast.error('Failed to use safe word');
    }
  };

  const handleCardSubmission = async (playerId: string, card: string) => {
    try {
      await submitCard(playerId, card);
      setHasSubmittedCard(true);
      toast.success('Card submitted!');
    } catch (error) {
      toast.error('Failed to submit card');
    }
  };

  const replaceSubmittedCards = async () => {
    if (!game) return;

    try {
      // Get all player submissions for this round (excluding deck cards)
      const playerSubmissions = cardSubmissions.filter(sub => !sub.is_deck_card && sub.player_id);

      console.log('Replacing cards for submissions:', playerSubmissions);

      // For each player who submitted a card, replace it in their hand
      for (const submission of playerSubmissions) {
        if (!submission.player_id) continue;

        // Get a new random white card that's not already in the player's hand
        const { data: whiteCards, error: cardError } = await supabase
          .from('white_cards')
          .select('card_text')
          .limit(50); // Get a pool of cards to choose from

        if (cardError) {
          console.error('Failed to get white cards:', cardError);
          continue;
        }

        // Get player's current hand
        const { data: currentHand, error: handError } = await supabase
          .from('sinful_secrets_player_hands')
          .select('card_text')
          .eq('player_id', submission.player_id)
          .eq('game_id', game.id);

        if (handError) {
          console.error('Failed to get player hand:', handError);
          continue;
        }

        const currentCards = currentHand.map(h => h.card_text);
        const availableCards = whiteCards.filter(card => !currentCards.includes(card.card_text));

        if (availableCards.length === 0) {
          console.warn('No available cards for replacement');
          continue;
        }

        const newCard = availableCards[Math.floor(Math.random() * availableCards.length)];

        // Replace the submitted card in the player's hand
        const { error: replaceError } = await supabase
          .from('sinful_secrets_player_hands')
          .update({ card_text: newCard.card_text })
          .eq('player_id', submission.player_id)
          .eq('game_id', game.id)
          .eq('card_text', submission.card_text);

        if (replaceError) {
          console.error('Failed to replace card:', replaceError);
        } else {
          console.log(`Replaced "${submission.card_text}" with "${newCard.card_text}" for player ${submission.player_id}`);
        }
      }

    } catch (error) {
      console.error('Failed to replace submitted cards:', error);
    }
  };

  const handleWinnerSelection = async (submissionId: string) => {
    if (!game || !isJudge) return;

    try {
      const { error: updateError } = await supabase
        .from('sinful_secrets_card_submissions')
        .update({ is_winner: true })
        .eq('id', submissionId);

      if (updateError) throw updateError;

      // Update winner's score if it's not a deck card
      const winningSubmission = cardSubmissions.find(s => s.id === submissionId);
      if (winningSubmission && !winningSubmission.is_deck_card) {
        const { error: scoreError } = await supabase
          .from('sinful_secrets_players')
          .update({ score: supabase.sql`score + 1` })
          .eq('id', winningSubmission.player_id);

        if (scoreError) throw scoreError;
      }

      // Replace submitted cards for all players who submitted
      await replaceSubmittedCards();

      // Advance to sinful secrets phase
      const { error: gameError } = await supabase
        .from('sinful_secrets_games')
        .update({
          game_phase: 'sinful-secrets',
          is_card_phase: false
        })
        .eq('id', game.id);

      if (gameError) throw gameError;

      toast.success('Winner selected!');
    } catch (error) {
      console.error('Failed to select winner:', error);
      toast.error('Failed to select winner');
    }
  };

  // Show waiting screen if game hasn't started yet
  if (gameStatus === 'waiting') {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Welcome, {playerName}!</h1>
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">Waiting for Game to Start</h2>
            <p className="text-tnt-whiteSmoke/80 mb-4">
              You've successfully joined the game! The host will start the game once enough players have joined.
            </p>
            <div className="flex items-center justify-center space-x-2 text-tnt-hotPink">
              <div className="animate-bounce">●</div>
              <div className="animate-bounce" style={{animationDelay: '0.1s'}}>●</div>
              <div className="animate-bounce" style={{animationDelay: '0.2s'}}>●</div>
            </div>
          </div>
          <p className="text-sm text-tnt-whiteSmoke/60">
            Game Code: <span className="font-bold text-tnt-hotPink">{gameCode}</span>
          </p>
        </div>
      </div>
    );
  }

  // Card submission phase
  if (gamePhase === 'card-submission' && gameStatus === 'active') {
    return (
      <PlayerCardInterface
        playerName={playerName}
        playerId={playerId}
        gameId={game?.id || ''}
        currentPrompt={game?.current_prompt || "Submit your best card for this round!"}
        onSubmitCard={handleCardSubmission}
        hasSubmitted={hasPlayerSubmitted}
      />
    );
  }

  // Card judging phase
  if (gamePhase === 'card-judging' && gameStatus === 'active') {
    return (
      <PlayerJudgingInterface
        currentPrompt={game?.current_prompt || "Choose the best answer!"}
        submissions={cardSubmissions.map(sub => ({
          id: sub.id,
          player_id: sub.player_id,
          card_text: sub.card_text,
          is_deck_card: sub.is_deck_card || false,
          player_name: sub.player_name
        }))}
        onSelectWinner={handleWinnerSelection}
        isJudge={isJudge}
        judgeName={currentJudge?.player_name}
      />
    );
  }

  // Sinful secrets phase - not active player
  if (gamePhase === 'sinful-secrets' && !isActivePlayer && gameStatus === 'active') {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Sinful Secrets</h1>
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-bold mb-2">Hey {playerName}!</h2>
            <p className="text-tnt-whiteSmoke/80">
              It's not your turn yet. Sit back and enjoy the spicy secrets! 😉
            </p>
          </div>
          <p className="text-sm text-tnt-whiteSmoke/60">
            Game Code: <span className="font-bold text-tnt-hotPink">{gameCode}</span>
          </p>
        </div>
      </div>
    );
  }

  // Sinful secrets phase - Truth/Dare selection
  if (pendingTruthDare) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-6">Choose Your Path</h1>
          
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-8">
            <p className="text-lg mb-6">
              You picked a spicy number! Now decide...
            </p>
            
            <div className="space-y-4">
              <Button
                onClick={() => handleTruthDareChoice('Truth')}
                className="w-full bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-4 px-6 rounded-lg text-xl transition-colors"
              >
                💭 Truth
                <span className="block text-sm font-normal opacity-80">
                  Reveal a secret
                </span>
              </Button>
              
              <Button
                onClick={() => handleTruthDareChoice('Dare')}
                className="w-full bg-tnt-crimson hover:bg-red-600 text-white font-bold py-4 px-6 rounded-lg text-xl transition-colors"
              >
                🔥 Dare
                <span className="block text-sm font-normal opacity-80">
                  Take action
                </span>
              </Button>
            </div>
          </div>

          <Button
            onClick={handleSafeWord}
            variant="outline"
            className="border-tnt-whiteSmoke/30 text-tnt-whiteSmoke hover:bg-tnt-whiteSmoke/10"
          >
            Use Safe Word
          </Button>
        </div>
      </div>
    );
  }

  // Sinful secrets phase - Number selection
  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
      <div className="text-center max-w-md w-full">
        <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Your Sinful Secrets Turn!</h1>
        
        <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Choose Your Number, {playerName}</h2>
          <p className="text-tnt-whiteSmoke/80 mb-6">
            Pick a number between 1-20 to reveal your challenge
          </p>
          
          <div className="grid grid-cols-4 gap-3 mb-6">
            {Array.from({length: 20}, (_, i) => i + 1).map(num => (
              <button
                key={num}
                onClick={() => handleNumberSelection(num)}
                className="w-12 h-12 rounded-lg font-bold text-lg transition-all bg-tnt-hotPink hover:bg-tnt-pink text-white hover:scale-105 active:scale-95"
              >
                {num}
              </button>
            ))}
          </div>
          
          <Button
            onClick={handleSafeWord}
            variant="outline"
            className="border-tnt-whiteSmoke/30 text-tnt-whiteSmoke hover:bg-tnt-whiteSmoke/10"
          >
            Use Safe Word
          </Button>
        </div>
        
        <p className="text-sm text-tnt-whiteSmoke/60">
          Game Code: <span className="font-bold text-tnt-hotPink">{gameCode}</span>
        </p>
      </div>
    </div>
  );
};

export default SinfulSecretsPlayerInterface;
