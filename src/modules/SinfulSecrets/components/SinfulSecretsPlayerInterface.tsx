
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { useSinfulSecretsGame } from '@/hooks/useSinfulSecretsGame';
import PlayerCardInterface from './PlayerCardInterface';

interface SinfulSecretsPlayerInterfaceProps {
  playerName: string;
  playerId: string;
  gameCode: string;
}

const SinfulSecretsPlayerInterface: React.FC<SinfulSecretsPlayerInterfaceProps> = ({
  playerName,
  playerId,
  gameCode
}) => {
  const [selectedNumber, setSelectedNumber] = useState<number | null>(null);
  const [hasSubmittedCard, setHasSubmittedCard] = useState<boolean>(false);

  // Use the real database hook
  const {
    game,
    players,
    cardSubmissions,
    currentChallenge,
    currentPrompt,
    isLoading,
    error,
    submitCard,
    selectChallengeNumber
  } = useSinfulSecretsGame(gameCode);

  const currentPlayer = players.find(p => p.id === playerId);
  const isActivePlayer = game && currentPlayer ? players[game.current_player_index]?.id === playerId : false;
  const gamePhase = game?.game_phase || 'card-submission';
  const gameStatus = game?.status || 'waiting';
  const hasPlayerSubmitted = cardSubmissions.some(sub => sub.player_id === playerId);
  
  // Mock number to category mapping
  const getNumberCategory = (number: number): string => {
    if (number <= 8) return Math.random() < 0.5 ? 'Truth' : 'Dare';
    if (number <= 12) return Math.random() < 0.5 ? 'Truth' : 'Dare';
    if (number <= 16) return 'WouldYouRather';
    return 'Wildcard';
  };

  const handleNumberSelection = async (number: number) => {
    setSelectedNumber(number);
    const category = getNumberCategory(number);

    try {
      await selectChallengeNumber(playerId, number);
      toast.success(`Selected number ${number} (${category})!`);
    } catch (error) {
      toast.error('Failed to select number');
    }
  };

  const handleTruthDareChoice = (choice: 'Truth' | 'Dare') => {
    toast.success(`You chose ${choice}!`);
    // TODO: Implement truth/dare choice functionality
  };

  const handleSafeWord = () => {
    toast.info('🔐 Safe word used! Skipping to next prompt...');
    // TODO: Implement safe word functionality
  };

  const handleCardSubmission = async (card: string) => {
    try {
      await submitCard(playerId, card);
      setHasSubmittedCard(true);
      toast.success('Card submitted!');
    } catch (error) {
      toast.error('Failed to submit card');
    }
  };

  // Show waiting screen if game hasn't started yet
  if (gameStatus === 'waiting') {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Welcome, {playerName}!</h1>
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">Waiting for Game to Start</h2>
            <p className="text-tnt-whiteSmoke/80 mb-4">
              You've successfully joined the game! The host will start the game once enough players have joined.
            </p>
            <div className="flex items-center justify-center space-x-2 text-tnt-hotPink">
              <div className="animate-bounce">●</div>
              <div className="animate-bounce" style={{animationDelay: '0.1s'}}>●</div>
              <div className="animate-bounce" style={{animationDelay: '0.2s'}}>●</div>
            </div>
          </div>
          <p className="text-sm text-tnt-whiteSmoke/60">
            Game Code: <span className="font-bold text-tnt-hotPink">{gameCode}</span>
          </p>
        </div>
      </div>
    );
  }

  // Card submission phase
  if (gamePhase === 'card-submission' && gameStatus === 'active') {
    return (
      <PlayerCardInterface
        playerName={playerName}
        playerId={playerId}
        currentPrompt={currentPrompt || "Submit your best card for this round!"}
        onSubmitCard={handleCardSubmission}
        hasSubmitted={hasPlayerSubmitted}
      />
    );
  }

  // Card judging phase
  if (gamePhase === 'card-judging' && gameStatus === 'active') {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Judging Time!</h1>
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-6">
            <p className="text-tnt-whiteSmoke/80">
              The AI host is judging all submissions. Get ready for the results! 🎭
            </p>
          </div>
          <p className="text-sm text-tnt-whiteSmoke/60">
            Game Code: <span className="font-bold text-tnt-hotPink">{gameCode}</span>
          </p>
        </div>
      </div>
    );
  }

  // Sinful secrets phase - not active player
  if (gamePhase === 'sinful-secrets' && !isActivePlayer && gameStatus === 'active') {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Sinful Secrets</h1>
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-bold mb-2">Hey {playerName}!</h2>
            <p className="text-tnt-whiteSmoke/80">
              It's not your turn yet. Sit back and enjoy the spicy secrets! 😉
            </p>
          </div>
          <p className="text-sm text-tnt-whiteSmoke/60">
            Game Code: <span className="font-bold text-tnt-hotPink">{gameCode}</span>
          </p>
        </div>
      </div>
    );
  }

  // Sinful secrets phase - Truth/Dare selection
  if (pendingTruthDare) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md">
          <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-6">Choose Your Path</h1>
          
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-8">
            <p className="text-lg mb-6">
              You picked a spicy number! Now decide...
            </p>
            
            <div className="space-y-4">
              <Button
                onClick={() => handleTruthDareChoice('Truth')}
                className="w-full bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-4 px-6 rounded-lg text-xl transition-colors"
              >
                💭 Truth
                <span className="block text-sm font-normal opacity-80">
                  Reveal a secret
                </span>
              </Button>
              
              <Button
                onClick={() => handleTruthDareChoice('Dare')}
                className="w-full bg-tnt-crimson hover:bg-red-600 text-white font-bold py-4 px-6 rounded-lg text-xl transition-colors"
              >
                🔥 Dare
                <span className="block text-sm font-normal opacity-80">
                  Take action
                </span>
              </Button>
            </div>
          </div>

          <Button
            onClick={handleSafeWord}
            variant="outline"
            className="border-tnt-whiteSmoke/30 text-tnt-whiteSmoke hover:bg-tnt-whiteSmoke/10"
          >
            Use Safe Word
          </Button>
        </div>
      </div>
    );
  }

  // Sinful secrets phase - Number selection
  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
      <div className="text-center max-w-md w-full">
        <h1 className="text-3xl font-pacifico text-tnt-hotPink mb-4">Your Sinful Secrets Turn!</h1>
        
        <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Choose Your Number, {playerName}</h2>
          <p className="text-tnt-whiteSmoke/80 mb-6">
            Pick a number between 1-20 to reveal your challenge
          </p>
          
          <div className="grid grid-cols-4 gap-3 mb-6">
            {Array.from({length: 20}, (_, i) => i + 1).map(num => (
              <button
                key={num}
                onClick={() => handleNumberSelection(num)}
                className="w-12 h-12 rounded-lg font-bold text-lg transition-all bg-tnt-hotPink hover:bg-tnt-pink text-white hover:scale-105 active:scale-95"
              >
                {num}
              </button>
            ))}
          </div>
          
          <Button
            onClick={handleSafeWord}
            variant="outline"
            className="border-tnt-whiteSmoke/30 text-tnt-whiteSmoke hover:bg-tnt-whiteSmoke/10"
          >
            Use Safe Word
          </Button>
        </div>
        
        <p className="text-sm text-tnt-whiteSmoke/60">
          Game Code: <span className="font-bold text-tnt-hotPink">{gameCode}</span>
        </p>
      </div>
    </div>
  );
};

export default SinfulSecretsPlayerInterface;
