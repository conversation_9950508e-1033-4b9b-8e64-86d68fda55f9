
import React from 'react';
import { Button } from '@/components/ui/button';
import { Player } from '../types/gameTypes';

interface SinfulSecretsPhaseProps {
  currentPlayer: Player;
  currentPrompt?: string;
  onCompleteChallenge: () => void;
  onPlayerNumberSelection: (playerId: string, number: number, category: string) => void;
  pendingTruthDare?: boolean;
  onTruthDareSelection?: (playerId: string, choice: 'Truth' | 'Dare') => void;
}

const SinfulSecretsPhase: React.FC<SinfulSecretsPhaseProps> = ({
  currentPlayer,
  currentPrompt,
  onCompleteChallenge,
  onPlayerNumberSelection,
  pendingTruthDare,
  onTruthDareSelection
}) => {
  const getNumberCategory = (number: number): string => {
    if (number <= 8) return Math.random() < 0.5 ? 'Truth' : 'Dare';
    if (number <= 12) return Math.random() < 0.5 ? 'Truth' : 'Dare';
    if (number <= 16) return 'WouldYouRather';
    return 'Wildcard';
  };

  const handleNumberSelection = (number: number) => {
    const category = getNumberCategory(number);
    onPlayerNumberSelection(currentPlayer.id, number, category);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-pacifico text-tnt-hotPink mb-4">
          Sinful Secrets Turn
        </h2>
        <p className="text-xl text-tnt-whiteSmoke">
          <span className="text-tnt-hotPink font-bold">{currentPlayer.name}</span>'s Turn
        </p>
      </div>

      {!currentPrompt && !pendingTruthDare && (
        <div className="bg-tnt-deepPurple/20 rounded-lg p-6">
          <h3 className="text-lg font-bold mb-4 text-center">Choose Your Number (1-20)</h3>
          <div className="grid grid-cols-5 gap-3 max-w-md mx-auto">
            {Array.from({length: 20}, (_, i) => i + 1).map(num => (
              <button
                key={num}
                onClick={() => handleNumberSelection(num)}
                className="w-12 h-12 rounded-lg font-bold text-lg transition-all bg-tnt-hotPink hover:bg-tnt-pink text-white hover:scale-105 active:scale-95"
              >
                {num}
              </button>
            ))}
          </div>
        </div>
      )}

      {pendingTruthDare && onTruthDareSelection && (
        <div className="bg-tnt-deepPurple/20 rounded-lg p-6 text-center">
          <h3 className="text-lg font-bold mb-4">Choose Truth or Dare</h3>
          <div className="space-y-4">
            <Button
              onClick={() => onTruthDareSelection(currentPlayer.id, 'Truth')}
              className="w-full bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-4"
            >
              💭 Truth
            </Button>
            <Button
              onClick={() => onTruthDareSelection(currentPlayer.id, 'Dare')}
              className="w-full bg-tnt-crimson hover:bg-red-600 text-white font-bold py-4"
            >
              🔥 Dare
            </Button>
          </div>
        </div>
      )}

      {currentPrompt && (
        <div className="bg-tnt-hotPink/20 rounded-lg p-6">
          <h3 className="font-bold text-tnt-hotPink mb-4 text-center">Your Challenge:</h3>
          <p className="text-lg text-center mb-6">{currentPrompt}</p>
          <div className="text-center">
            <Button
              onClick={onCompleteChallenge}
              className="bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-3 px-6"
            >
              Challenge Complete
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SinfulSecretsPhase;
