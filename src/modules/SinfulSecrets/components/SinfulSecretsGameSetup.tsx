import React, { useState } from 'react';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>Left, Play, Settings, Sparkles } from 'lucide-react';
import CardDeckSelector from './CardDeckSelector';

interface SinfulSecretsGameSetupProps {
  onGameSetupComplete: (setupData: GameSetupData) => void;
  onBack: () => void;
}

export interface GameSetupData {
  hostName: string;
  selectedCardPacks: string[];
  customDeck?: any;
  gameSettings: {
    intensityLevel: 'Playful' | 'Sensual' | 'Daring' | 'Bold';
    safeWord: string;
    aiPersona: string;
  };
}

const SinfulSecretsGameSetup: React.FC<SinfulSecretsGameSetupProps> = ({
  onGameSetupComplete,
  onBack
}) => {
  // Skip host info entirely - host is not a player
  const [currentStep, setCurrentStep] = useState<'deck-selection' | 'game-settings'>('deck-selection');
  const [hostName] = useState('Game Host'); // Default host name
  const [selectedCardPacks, setSelectedCardPacks] = useState<string[]>([]);
  const [customDeck, setCustomDeck] = useState<any>(null);
  const [gameSettings, setGameSettings] = useState({
    intensityLevel: 'Sensual' as const,
    safeWord: generateSafeWord(),
    aiPersona: 'seductive'
  });

  function generateSafeWord(): string {
    const words = ['Pineapple', 'Butterfly', 'Rainbow', 'Moonlight', 'Whisper', 'Velvet', 'Crystal', 'Harmony'];
    return words[Math.floor(Math.random() * words.length)];
  }

  // Host info step removed - host is not a player

  const handleDeckSelectionComplete = (packs: string[], customDeckData?: any) => {
    setSelectedCardPacks(packs);
    if (customDeckData) {
      setCustomDeck(customDeckData);
    }
    setCurrentStep('game-settings');
  };

  const handleGameSettingsComplete = () => {
    const setupData: GameSetupData = {
      hostName,
      selectedCardPacks,
      customDeck,
      gameSettings: {
        intensityLevel: gameSettings.intensityLevel,
        safeWord: gameSettings.safeWord,
        aiPersona: getAIPersonaText(gameSettings.aiPersona)
      }
    };
    onGameSetupComplete(setupData);
  };

  const getAIPersonaText = (persona: string): string => {
    const personas = {
      seductive: "You are the seductive, playful host of Sinful Secrets. Your tone is flirty, encouraging, and slightly mischievous. You guide players through intimate revelations with warmth and excitement.",
      playful: "You are the fun, energetic host of Sinful Secrets. Your tone is upbeat, encouraging, and playfully teasing. You keep the energy high and make everyone feel comfortable sharing.",
      mysterious: "You are the mysterious, alluring host of Sinful Secrets. Your tone is intriguing, sophisticated, and subtly provocative. You create an atmosphere of elegant intimacy.",
      bold: "You are the bold, confident host of Sinful Secrets. Your tone is direct, encouraging, and fearlessly honest. You push boundaries while keeping everyone comfortable."
    };
    return personas[persona as keyof typeof personas] || personas.seductive;
  };

  // Step 1: Deck Selection (Host info step removed)
  if (currentStep === 'deck-selection') {
    return (
      <CardDeckSelector
        onDecksSelected={(packs, customDeck) => {
          if (customDeck) {
            handleDeckSelectionComplete([], customDeck);
          } else {
            handleDeckSelectionComplete(packs);
          }
        }}
        onBack={onBack} // Go back to mode selector
      />
    );
  }

  // Step 3: Game Settings
  if (currentStep === 'game-settings') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-6">
        <div className="max-w-2xl mx-auto">
          <Button
            onClick={() => setCurrentStep('deck-selection')}
            variant="ghost"
            className="mb-6 text-tnt-whiteSmoke hover:text-tnt-hotPink"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Deck Selection
          </Button>

          <h1 className="text-4xl font-pacifico text-tnt-hotPink mb-8 text-center">Game Settings</h1>

          <div className="space-y-6">
            {/* Intensity Level */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">Intensity Level</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {(['Playful', 'Sensual', 'Daring', 'Bold'] as const).map((level) => (
                    <Button
                      key={level}
                      onClick={() => setGameSettings(prev => ({ ...prev, intensityLevel: level }))}
                      variant={gameSettings.intensityLevel === level ? "default" : "outline"}
                      className={gameSettings.intensityLevel === level 
                        ? "bg-tnt-hotPink hover:bg-tnt-pink" 
                        : "border-tnt-hotPink/30 hover:border-tnt-hotPink"
                      }
                    >
                      {level}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* AI Persona */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">AI Host Personality</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { id: 'seductive', label: 'Seductive', icon: '💋' },
                    { id: 'playful', label: 'Playful', icon: '😈' },
                    { id: 'mysterious', label: 'Mysterious', icon: '🔮' },
                    { id: 'bold', label: 'Bold', icon: '🔥' }
                  ].map((persona) => (
                    <Button
                      key={persona.id}
                      onClick={() => setGameSettings(prev => ({ ...prev, aiPersona: persona.id }))}
                      variant={gameSettings.aiPersona === persona.id ? "default" : "outline"}
                      className={gameSettings.aiPersona === persona.id 
                        ? "bg-tnt-hotPink hover:bg-tnt-pink" 
                        : "border-tnt-hotPink/30 hover:border-tnt-hotPink"
                      }
                    >
                      <span className="mr-2">{persona.icon}</span>
                      {persona.label}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Safe Word */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">Safe Word</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-3">
                  <Input
                    value={gameSettings.safeWord}
                    onChange={(e) => setGameSettings(prev => ({ ...prev, safeWord: e.target.value }))}
                    className="bg-tnt-charcoal border-tnt-hotPink/30 text-white"
                    placeholder="Enter safe word"
                  />
                  <Button
                    onClick={() => setGameSettings(prev => ({ ...prev, safeWord: generateSafeWord() }))}
                    variant="outline"
                    className="border-tnt-hotPink/30 hover:border-tnt-hotPink"
                  >
                    <Sparkles className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Complete Setup */}
            <Button
              onClick={handleGameSettingsComplete}
              className="w-full bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-4 px-6 rounded-lg transition-colors text-lg"
            >
              <Play className="mr-2 h-5 w-5" />
              Create Game & Enter Staging Area
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default SinfulSecretsGameSetup;
