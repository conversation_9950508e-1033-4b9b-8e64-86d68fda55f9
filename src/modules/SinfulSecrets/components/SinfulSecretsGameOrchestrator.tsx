import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useSinfulSecretsGame } from '@/hooks/useSinfulSecretsGame';
import SinfulSecretsGameSetup, { GameSetupData } from './SinfulSecretsGameSetup';
import SinfulSecretsStagingArea from './SinfulSecretsStagingArea';
import SinfulSecretsMainGameDisplay from './SinfulSecretsMainGameDisplay';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Settings, ArrowLeft } from 'lucide-react';

type GameFlowState = 'setup' | 'staging' | 'playing' | 'host-controls';

const SinfulSecretsGameOrchestrator: React.FC = () => {
  const navigate = useNavigate();
  const [flowState, setFlowState] = useState<GameFlowState>('setup');
  const [gameCode, setGameCode] = useState<string>('');
  const [hostName, setHostName] = useState<string>('');
  const [gameSetupData, setGameSetupData] = useState<GameSetupData | null>(null);

  const {
    game,
    players,
    createGame,
    isLoading,
    error
  } = useSinfulSecretsGame(gameCode);

  // Load saved session data
  useEffect(() => {
    const savedHostName = sessionStorage.getItem('sinfulSecretsHostName');
    const savedGameCode = sessionStorage.getItem('sinfulSecretsGameCode');
    const savedFlowState = sessionStorage.getItem('sinfulSecretsFlowState') as GameFlowState;
    
    if (savedHostName) setHostName(savedHostName);
    if (savedGameCode) {
      setGameCode(savedGameCode);
      if (savedFlowState && ['staging', 'playing'].includes(savedFlowState)) {
        setFlowState(savedFlowState);
      } else {
        setFlowState('staging');
      }
    }
  }, []);

  const handleGameSetupComplete = async (setupData: GameSetupData) => {
    try {
      setGameSetupData(setupData);
      setHostName(setupData.hostName);
      
      // Create the game
      const newGameCode = await createGame(setupData.hostName);
      setGameCode(newGameCode);
      
      // Update game with setup data (only columns that exist in the schema)
      const { error: updateError } = await supabase
        .from('sinful_secrets_games')
        .update({
          intensity_level: setupData.gameSettings.intensityLevel,
          safe_word: setupData.gameSettings.safeWord,
          ai_persona: setupData.gameSettings.aiPersona
        })
        .eq('game_code', newGameCode);

      if (updateError) throw updateError;

      // Store card pack selection and custom deck data in events table for now
      if (setupData.selectedCardPacks.length > 0 || setupData.customDeck) {
        const { error: eventError } = await supabase
          .from('sinful_secrets_events')
          .insert({
            game_id: (await supabase
              .from('sinful_secrets_games')
              .select('id')
              .eq('game_code', newGameCode)
              .single()).data?.id,
            event_type: 'game_setup',
            event_data: {
              selected_card_packs: setupData.selectedCardPacks,
              custom_deck: setupData.customDeck
            }
          });

        if (eventError) {
          console.warn('Failed to store card pack data:', eventError);
          // Don't throw error as this is not critical for game functionality
        }
      }

      // Save to session
      sessionStorage.setItem('sinfulSecretsHostName', setupData.hostName);
      sessionStorage.setItem('sinfulSecretsGameCode', newGameCode);
      sessionStorage.setItem('sinfulSecretsFlowState', 'staging');
      
      setFlowState('staging');
      toast.success(`Game created! Share code: ${newGameCode}`);
    } catch (error) {
      console.error('Failed to create game:', error);
      toast.error('Failed to create game');
    }
  };

  const handleStartGame = async () => {
    if (!game) return;

    try {
      const { error } = await supabase
        .from('sinful_secrets_games')
        .update({
          status: 'active'
        })
        .eq('id', game.id);

      if (error) throw error;
      
      sessionStorage.setItem('sinfulSecretsFlowState', 'playing');
      setFlowState('playing');
      toast.success("Game started! Let the sinful secrets begin! 😈");
    } catch (error) {
      toast.error("Failed to start game");
      console.error('Start game error:', error);
    }
  };

  const handleBackToModeSelector = () => {
    // Clear session data
    sessionStorage.removeItem('sinfulSecretsHostName');
    sessionStorage.removeItem('sinfulSecretsGameCode');
    sessionStorage.removeItem('sinfulSecretsFlowState');
    navigate('/play');
  };

  const handleShowHostControls = () => {
    setFlowState('host-controls');
  };

  const handleBackToGame = () => {
    setFlowState('playing');
  };

  const handleShowSettings = () => {
    setFlowState('host-controls');
  };

  // Setup Phase
  if (flowState === 'setup') {
    return (
      <SinfulSecretsGameSetup
        onGameSetupComplete={handleGameSetupComplete}
        onBack={handleBackToModeSelector}
      />
    );
  }

  // Staging Area Phase
  if (flowState === 'staging') {
    return (
      <SinfulSecretsStagingArea
        gameCode={gameCode}
        hostName={hostName}
        onStartGame={handleStartGame}
        onShowSettings={handleShowSettings}
      />
    );
  }

  // Host Controls Modal/Page
  if (flowState === 'host-controls') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-6">
        <div className="max-w-4xl mx-auto">
          <Button
            onClick={handleBackToGame}
            variant="ghost"
            className="mb-6 text-tnt-whiteSmoke hover:text-tnt-hotPink"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Game
          </Button>

          <h1 className="text-4xl font-pacifico text-tnt-hotPink mb-8 text-center">Host Controls</h1>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Game Settings */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">Game Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Intensity Level:</span>
                  <span className="font-bold">{game?.intensity_level || 'Sensual'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Safe Word:</span>
                  <span className="font-bold">{game?.safe_word || 'Pineapple'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Current Round:</span>
                  <span className="font-bold">{game?.current_round || 1}</span>
                </div>
                <div className="flex justify-between">
                  <span>Game Phase:</span>
                  <span className="font-bold capitalize">{game?.game_phase?.replace('-', ' ') || 'Card Submission'}</span>
                </div>
              </CardContent>
            </Card>

            {/* Player Management */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">Player Management</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Players:</span>
                    <span className="font-bold">{players.length}</span>
                  </div>
                  <div className="space-y-1 max-h-40 overflow-y-auto">
                    {players.map((player) => (
                      <div
                        key={player.id}
                        className="flex items-center justify-between p-2 bg-tnt-charcoal/30 rounded text-sm"
                      >
                        <span>{player.player_name} {player.is_host && '(Host)'}</span>
                        <div className={`w-2 h-2 rounded-full ${
                          player.is_active ? 'bg-green-500' : 'bg-gray-500'
                        }`} />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Game Actions */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">Game Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full border-tnt-hotPink/30 hover:border-tnt-hotPink"
                  disabled
                >
                  Skip Current Round
                </Button>
                <Button
                  variant="outline"
                  className="w-full border-tnt-hotPink/30 hover:border-tnt-hotPink"
                  disabled
                >
                  Force Next Phase
                </Button>
                <Button
                  variant="outline"
                  className="w-full border-yellow-500/30 hover:border-yellow-500 text-yellow-400"
                  onClick={() => {
                    if (confirm('Are you sure you want to end the game?')) {
                      handleBackToModeSelector();
                    }
                  }}
                >
                  End Game
                </Button>
              </CardContent>
            </Card>

            {/* Debug Info */}
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30">
              <CardHeader>
                <CardTitle className="text-tnt-hotPink">Debug Info</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div>Game Code: <span className="font-mono">{gameCode}</span></div>
                  <div>Game ID: <span className="font-mono">{game?.id}</span></div>
                  <div>Status: <span className="font-bold">{game?.status}</span></div>
                  <div>Loading: <span className="font-bold">{isLoading ? 'Yes' : 'No'}</span></div>
                  {error && <div className="text-red-400">Error: {error}</div>}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // Main Game Display Phase
  if (flowState === 'playing') {
    return (
      <SinfulSecretsMainGameDisplay
        gameCode={gameCode}
        hostName={hostName}
        onShowHostControls={handleShowHostControls}
      />
    );
  }

  return null;
};

export default SinfulSecretsGameOrchestrator;
