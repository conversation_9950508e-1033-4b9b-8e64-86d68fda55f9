import React from 'react';
import GameCard from '@/components/GameCard';
import { Crown, Clock, Eye } from 'lucide-react';

interface CardSubmission {
  id: string;
  player_id: string | null;
  card_text: string;
  is_deck_card: boolean;
  player_name: string;
}

interface HostWaitingForJudgeProps {
  currentPrompt: string;
  submissions: CardSubmission[];
  currentJudge?: { id: string; player_name: string } | null;
}

const HostWaitingForJudge: React.FC<HostWaitingForJudgeProps> = ({
  currentPrompt,
  submissions,
  currentJudge
}) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-pacifico text-tnt-hotPink mb-4">Player Judging in Progress</h2>
        <div className="max-w-md mx-auto mb-4">
          <GameCard 
            type="black" 
            content={currentPrompt}
            className="w-full"
          />
        </div>
        {currentJudge && (
          <div className="bg-tnt-gold/20 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Crown className="h-6 w-6 text-tnt-gold" />
              <p className="text-lg text-tnt-whiteSmoke">
                <span className="text-tnt-gold font-bold">{currentJudge.player_name}</span> is judging this round
              </p>
              <Crown className="h-6 w-6 text-tnt-gold" />
            </div>
            <div className="flex items-center justify-center gap-2 text-tnt-whiteSmoke/80">
              <Clock className="h-4 w-4 animate-pulse" />
              <span className="text-sm">Waiting for judge to select the winner...</span>
            </div>
          </div>
        )}
      </div>

      {/* Submissions Preview (Face Down) */}
      <div className="space-y-4">
        <div className="flex items-center justify-center gap-2 text-tnt-whiteSmoke">
          <Eye className="h-5 w-5" />
          <h3 className="text-lg font-bold">Submitted Cards ({submissions.length})</h3>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {submissions.map((submission, index) => (
            <div key={submission.id} className="relative">
              {/* Face-down card preview */}
              <GameCard 
                type="white" 
                content="Hidden from host"
                className="bg-gradient-to-br from-tnt-deepPurple to-tnt-charcoal border-2 border-tnt-gold/30 opacity-75"
              />
              
              {/* Card number */}
              <div className="absolute top-2 left-2 bg-tnt-gold text-tnt-charcoal rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                {index + 1}
              </div>
              
              {/* Player indicator */}
              <div className="mt-2 text-center">
                <div className={`inline-block px-2 py-1 rounded-full text-xs font-semibold ${
                  submission.is_deck_card 
                    ? 'bg-tnt-deepPurple/50 text-tnt-whiteSmoke' 
                    : 'bg-tnt-hotPink/20 text-tnt-hotPink'
                }`}>
                  {submission.is_deck_card ? '🎲 Random' : `👤 ${submission.player_name}`}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="text-center bg-tnt-deepPurple/10 rounded-lg p-4">
        <h4 className="text-lg font-bold text-tnt-hotPink mb-2">Host View - Judging Phase</h4>
        <div className="text-sm text-tnt-whiteSmoke/80 space-y-1">
          <p>• The judge can see all submitted cards and will select the winner</p>
          <p>• Cards are hidden from you to ensure fair judging</p>
          <p>• The game will automatically continue once the judge makes their selection</p>
          <p>• You can continue chatting with players while they judge</p>
        </div>
      </div>

      {/* Loading Animation */}
      <div className="flex justify-center">
        <div className="flex items-center gap-2 text-tnt-gold">
          <div className="w-2 h-2 bg-tnt-gold rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-tnt-gold rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-tnt-gold rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          <span className="ml-2 text-sm">Waiting for judge decision</span>
        </div>
      </div>
    </div>
  );
};

export default HostWaitingForJudge;
