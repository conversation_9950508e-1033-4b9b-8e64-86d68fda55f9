
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { 
  getUserCustomCardPacks, 
  deleteCustomCardPack, 
  updateCustomCardPack,
  CardPack 
} from "@/services/packService";
import { toast } from "sonner";
import { Trash2, Edit2, Save, X, Eye, EyeOff } from "lucide-react";

interface CustomDeckManagerProps {
  onClose: () => void;
}

const CustomDeckManager: React.FC<CustomDeckManagerProps> = ({ onClose }) => {
  const [customDecks, setCustomDecks] = useState<CardPack[]>([]);
  const [editingDeck, setEditingDeck] = useState<string | null>(null);
  const [editData, setEditData] = useState<{
    name: string;
    description: string;
    isPublic: boolean;
  }>({ name: '', description: '', isPublic: false });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadCustomDecks();
  }, []);

  const loadCustomDecks = async () => {
    try {
      const decks = await getUserCustomCardPacks();
      setCustomDecks(decks);
    } catch (error) {
      console.error('Failed to load custom decks:', error);
      toast.error('Failed to load your custom decks');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (deckId: string) => {
    if (!confirm('Are you sure you want to delete this deck? This action cannot be undone.')) {
      return;
    }

    try {
      const success = await deleteCustomCardPack(deckId);
      if (success) {
        setCustomDecks(prev => prev.filter(deck => deck.id !== deckId));
        toast.success('Deck deleted successfully');
      } else {
        toast.error('Failed to delete deck');
      }
    } catch (error) {
      console.error('Error deleting deck:', error);
      toast.error('Failed to delete deck');
    }
  };

  const startEditing = (deck: CardPack) => {
    setEditingDeck(deck.id);
    setEditData({
      name: deck.packName,
      description: deck.description,
      isPublic: deck.isPublic || false
    });
  };

  const cancelEditing = () => {
    setEditingDeck(null);
    setEditData({ name: '', description: '', isPublic: false });
  };

  const saveEditing = async () => {
    if (!editingDeck) return;

    try {
      const updatedDeck = await updateCustomCardPack(editingDeck, {
        name: editData.name,
        description: editData.description,
        is_public: editData.isPublic
      });

      if (updatedDeck) {
        setCustomDecks(prev => prev.map(deck => 
          deck.id === editingDeck ? updatedDeck : deck
        ));
        toast.success('Deck updated successfully');
        setEditingDeck(null);
      }
    } catch (error) {
      console.error('Error updating deck:', error);
      toast.error('Failed to update deck');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="w-12 h-12 border-4 border-tnt-pink border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">My Custom Decks</h2>
        <Button onClick={onClose} variant="tntOutline">
          Close
        </Button>
      </div>

      {customDecks.length === 0 ? (
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="py-12 text-center">
            <p className="text-gray-300 mb-4">You haven't created any custom decks yet.</p>
            <p className="text-sm text-gray-400">
              Use the AI generator to create your first custom deck!
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {customDecks.map((deck) => (
            <Card key={deck.id} className="bg-gray-800 border-gray-700">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    {editingDeck === deck.id ? (
                      <div className="space-y-2">
                        <Input
                          value={editData.name}
                          onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                          className="bg-gray-700 border-gray-600 text-white"
                          placeholder="Deck name"
                        />
                        <Textarea
                          value={editData.description}
                          onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                          className="bg-gray-700 border-gray-600 text-white"
                          placeholder="Deck description"
                          rows={2}
                        />
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={editData.isPublic}
                            onCheckedChange={(checked) => setEditData(prev => ({ ...prev, isPublic: checked }))}
                          />
                          <span className="text-sm text-gray-300">Make public</span>
                        </div>
                      </div>
                    ) : (
                      <>
                        <CardTitle className="text-white flex items-center gap-2">
                          {deck.packName}
                          {deck.isPublic ? (
                            <Eye className="h-4 w-4 text-green-400" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          )}
                        </CardTitle>
                        <CardDescription className="text-gray-300">
                          {deck.description}
                        </CardDescription>
                      </>
                    )}
                  </div>
                  
                  <div className="flex space-x-2 ml-4">
                    {editingDeck === deck.id ? (
                      <>
                        <Button
                          onClick={saveEditing}
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Save className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={cancelEditing}
                          size="sm"
                          variant="outline"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          onClick={() => startEditing(deck)}
                          size="sm"
                          variant="outline"
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleDelete(deck.id)}
                          size="sm"
                          variant="destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="text-sm text-gray-400 space-y-1">
                  <div>{deck.blackCards.length} prompt cards</div>
                  <div>{deck.whiteCards.length} response cards</div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs rounded ${
                      deck.isPublic ? 'bg-green-600' : 'bg-gray-600'
                    }`}>
                      {deck.isPublic ? 'Public' : 'Private'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomDeckManager;
