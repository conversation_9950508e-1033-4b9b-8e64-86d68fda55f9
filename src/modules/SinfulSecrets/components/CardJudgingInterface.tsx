import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Crown, Sparkles, Trophy, Timer } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface CardSubmission {
  id: string;
  player_id: string;
  player_name: string;
  card_text: string;
  is_winner?: boolean;
}

interface CardJudgingInterfaceProps {
  submissions: CardSubmission[];
  currentPrompt: string;
  onJudgingComplete: (winnerId: string) => void;
  aiPersona: string;
  isAIJudging?: boolean;
}

const CardJudgingInterface: React.FC<CardJudgingInterfaceProps> = ({
  submissions,
  currentPrompt,
  onJudgingComplete,
  aiPersona,
  isAIJudging = true
}) => {
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [revealedCards, setRevealedCards] = useState<Set<number>>(new Set());
  const [judgingPhase, setJudgingPhase] = useState<'revealing' | 'judging' | 'winner'>('revealing');
  const [selectedWinner, setSelectedWinner] = useState<string | null>(null);
  const [aiJudgingText, setAiJudgingText] = useState('');
  const [countdown, setCountdown] = useState(3);

  // Auto-reveal cards one by one
  useEffect(() => {
    if (judgingPhase === 'revealing' && currentCardIndex < submissions.length) {
      const timer = setTimeout(() => {
        setRevealedCards(prev => new Set([...prev, currentCardIndex]));
        setCurrentCardIndex(prev => prev + 1);
      }, 2000); // 2 seconds between reveals

      return () => clearTimeout(timer);
    } else if (judgingPhase === 'revealing' && currentCardIndex >= submissions.length) {
      // All cards revealed, start judging phase
      setTimeout(() => {
        setJudgingPhase('judging');
        if (isAIJudging) {
          startAIJudging();
        }
      }, 1000);
    }
  }, [currentCardIndex, submissions.length, judgingPhase, isAIJudging]);

  const startAIJudging = async () => {
    setAiJudgingText("Hmm, let me think about these responses... 🤔");
    
    // Simulate AI thinking time
    setTimeout(() => {
      setAiJudgingText("Oh my, some of these are quite spicy! 😈");
    }, 2000);

    setTimeout(() => {
      setAiJudgingText("I'm weighing creativity, humor, and boldness... 💭");
    }, 4000);

    setTimeout(() => {
      setAiJudgingText("And the winner is... 🥁");
      setCountdown(3);
      startCountdown();
    }, 6000);
  };

  const startCountdown = () => {
    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          selectRandomWinner();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const selectRandomWinner = () => {
    // For now, select a random winner. In a real implementation, this would use AI
    const randomIndex = Math.floor(Math.random() * submissions.length);
    const winner = submissions[randomIndex];
    setSelectedWinner(winner.id);
    setJudgingPhase('winner');
    
    const winnerMessages = [
      `Congratulations! That response was absolutely perfect! 🎉`,
      `What a brilliant answer! You've won this round! 👑`,
      `That was exactly what I was looking for! Well done! ✨`,
      `Perfect blend of wit and spice! You're the winner! 🔥`
    ];
    
    setAiJudgingText(winnerMessages[Math.floor(Math.random() * winnerMessages.length)]);
    
    // Complete judging after showing winner
    setTimeout(() => {
      onJudgingComplete(winner.player_id);
    }, 4000);
  };

  const handleManualWinnerSelection = (submissionId: string, playerId: string) => {
    setSelectedWinner(submissionId);
    setJudgingPhase('winner');
    setAiJudgingText("Great choice! The host has spoken! 👑");
    
    setTimeout(() => {
      onJudgingComplete(playerId);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-6">
      <div className="max-w-6xl mx-auto">
        
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-pacifico text-tnt-hotPink mb-4">Card Judging</h1>
          <div className="bg-tnt-deepPurple/20 rounded-lg p-4 mb-6">
            <h2 className="text-xl font-bold text-tnt-hotPink mb-2">Current Prompt:</h2>
            <p className="text-lg">{currentPrompt}</p>
          </div>
        </div>

        {/* AI Judging Status */}
        {isAIJudging && judgingPhase === 'judging' && (
          <div className="text-center mb-8">
            <Card className="bg-tnt-deepPurple/20 border-tnt-hotPink/30 max-w-md mx-auto">
              <CardContent className="p-6">
                <Sparkles className="h-8 w-8 text-tnt-hotPink mx-auto mb-4 animate-spin" />
                <p className="text-lg">{aiJudgingText}</p>
                {countdown > 0 && (
                  <div className="mt-4">
                    <div className="text-3xl font-bold text-tnt-hotPink">{countdown}</div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Cards Display */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {submissions.map((submission, index) => (
              <motion.div
                key={submission.id}
                initial={{ opacity: 0, y: 50, rotateY: 180 }}
                animate={{ 
                  opacity: revealedCards.has(index) ? 1 : 0.3,
                  y: revealedCards.has(index) ? 0 : 50,
                  rotateY: revealedCards.has(index) ? 0 : 180,
                  scale: selectedWinner === submission.id ? 1.05 : 1
                }}
                transition={{ 
                  duration: 0.8,
                  delay: judgingPhase === 'revealing' ? 0 : index * 0.1
                }}
                className="relative"
              >
                <Card className={`
                  transition-all duration-300 cursor-pointer
                  ${selectedWinner === submission.id 
                    ? 'bg-gradient-to-br from-yellow-500/20 to-tnt-hotPink/20 border-yellow-500 shadow-lg shadow-yellow-500/20' 
                    : 'bg-tnt-deepPurple/20 border-tnt-hotPink/30 hover:border-tnt-hotPink'
                  }
                  ${!isAIJudging && judgingPhase === 'judging' ? 'hover:scale-105' : ''}
                `}>
                  
                  {/* Winner Crown */}
                  {selectedWinner === submission.id && (
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      className="absolute -top-4 -right-4 z-10"
                    >
                      <div className="bg-yellow-500 rounded-full p-2">
                        <Crown className="h-6 w-6 text-white" />
                      </div>
                    </motion.div>
                  )}

                  <CardHeader>
                    <CardTitle className="text-tnt-hotPink flex items-center justify-between">
                      <span>Player {index + 1}</span>
                      {selectedWinner === submission.id && (
                        <Trophy className="h-5 w-5 text-yellow-500" />
                      )}
                    </CardTitle>
                  </CardHeader>
                  
                  <CardContent 
                    className="p-6"
                    onClick={() => {
                      if (!isAIJudging && judgingPhase === 'judging') {
                        handleManualWinnerSelection(submission.id, submission.player_id);
                      }
                    }}
                  >
                    {revealedCards.has(index) ? (
                      <div>
                        <p className="text-lg mb-4 min-h-[3rem]">{submission.card_text}</p>
                        {!isAIJudging && judgingPhase === 'judging' && (
                          <Button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleManualWinnerSelection(submission.id, submission.player_id);
                            }}
                            className="w-full bg-tnt-hotPink hover:bg-tnt-pink"
                          >
                            Select Winner
                          </Button>
                        )}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-20">
                        <div className="text-6xl">🎴</div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Judging Progress */}
        {judgingPhase === 'revealing' && (
          <div className="mt-8 text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Timer className="h-5 w-5 text-tnt-hotPink" />
              <span>Revealing cards... ({revealedCards.size}/{submissions.length})</span>
            </div>
            <div className="w-full max-w-md mx-auto bg-tnt-charcoal/50 rounded-full h-2">
              <div 
                className="bg-tnt-hotPink h-2 rounded-full transition-all duration-300"
                style={{ width: `${(revealedCards.size / submissions.length) * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* Winner Announcement */}
        {judgingPhase === 'winner' && selectedWinner && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mt-8 text-center"
          >
            <Card className="bg-gradient-to-br from-yellow-500/20 to-tnt-hotPink/20 border-yellow-500 max-w-md mx-auto">
              <CardContent className="p-8">
                <Trophy className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-yellow-500 mb-2">Winner!</h3>
                <p className="text-lg">{aiJudgingText}</p>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default CardJudgingInterface;
