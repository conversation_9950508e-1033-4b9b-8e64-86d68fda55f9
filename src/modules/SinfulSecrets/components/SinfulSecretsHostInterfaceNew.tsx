import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useSinfulSecretsGame } from '@/hooks/useSinfulSecretsGame';
import GameHeader from '@/components/host/GameHeader';
import GameFooter from '@/components/host/GameFooter';
import HostLineDisplay from '@/components/host/HostLineDisplay';
import AIChat from '@/components/AIChat';
import AdminControlButton from '@/components/host/AdminControlButton';
import QRCodeJoin from '@/components/QRCodeJoin';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import CardDeckSelector from './CardDeckSelector';

const SinfulSecretsHostInterface: React.FC = () => {
  const [hostName, setHostName] = useState<string>("");
  const [gameCode, setGameCode] = useState<string>("");
  const [isGameCreated, setIsGameCreated] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<Array<{id: string; sender: string; text: string; timestamp: Date}>>([]);
  const [isAiLoading, setIsAiLoading] = useState<boolean>(false);
  const [showCardDeckSelector, setShowCardDeckSelector] = useState<boolean>(false);
  const [selectedCardPacks, setSelectedCardPacks] = useState<string[]>([]);
  const [customDeck, setCustomDeck] = useState<any>(null);

  // Use the real database hook
  const {
    game,
    players,
    cardSubmissions,
    currentChallenge,
    currentPrompt,
    isLoading,
    error,
    createGame,
    joinGame,
    submitCard,
    selectChallengeNumber
  } = useSinfulSecretsGame(gameCode);

  const currentGamePhase = game?.game_phase || 'card-submission';
  const currentPlayer = game && players.length > 0 ? players[game.current_player_index] : null;

  // Load saved session data
  useEffect(() => {
    const savedHostName = sessionStorage.getItem('sinfulSecretsHostName');
    const savedGameCode = sessionStorage.getItem('sinfulSecretsGameCode');
    
    if (savedHostName) setHostName(savedHostName);
    if (savedGameCode) {
      setGameCode(savedGameCode);
      setIsGameCreated(true);
    }
  }, []);

  // Create game when host name is provided
  const handleCreateGame = async () => {
    if (!hostName.trim()) {
      toast.error("Please enter your name first!");
      return;
    }

    try {
      const newGameCode = await createGame(hostName);
      setGameCode(newGameCode);
      setIsGameCreated(true);
      
      // Save to session
      sessionStorage.setItem('sinfulSecretsHostName', hostName);
      sessionStorage.setItem('sinfulSecretsGameCode', newGameCode);
      
      toast.success(`Game created! Share code: ${newGameCode}`);
    } catch (error) {
      console.error('Failed to create game:', error);
    }
  };

  const handleStartGame = async () => {
    // First show card deck selector if no packs selected
    if (selectedCardPacks.length === 0 && !customDeck) {
      setShowCardDeckSelector(true);
      return;
    }

    if (!game) return;

    try {
      const { error } = await supabase
        .from('sinful_secrets_games')
        .update({
          status: 'active',
          selected_card_packs: selectedCardPacks,
          custom_deck: customDeck
        })
        .eq('id', game.id);

      if (error) throw error;
      toast.success("Game started! Let the sinful secrets begin! 😈");
    } catch (error) {
      toast.error("Failed to start game");
      console.error('Start game error:', error);
    }
  };

  const callAI = async (message: string, context?: string): Promise<string> => {
    setIsAiLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('ai-chat', {
        body: {
          message,
          gameContext: context || `Round ${game?.current_round || 1}, ${game?.intensity_level || 'Playful'} intensity, ${currentGamePhase} phase`,
          playerName: hostName
        }
      });

      if (error) throw error;
      return data.response || data.fallback || "I'm here for all the secrets! 😈";
    } catch (error) {
      console.error('AI call error:', error);
      toast.error('AI host is taking a break. Try again in a moment.');
      return "I'm having a deliciously wicked moment... but I'll be right back! 😏";
    } finally {
      setIsAiLoading(false);
    }
  };

  const handleCardSubmission = async (playerId: string, card: string) => {
    await submitCard(playerId, card);
    
    const aiResponse = await callAI(
      `A player submitted a card: "${card}" for the current prompt`,
      'Card submission phase'
    );
    
    const aiMessage = {
      id: Date.now().toString(),
      sender: 'host',
      text: aiResponse,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, aiMessage]);
  };

  const handleWinnerSelection = async (submission: { playerId: string; card: string }) => {
    if (!game) return;

    try {
      // Mark submission as winner
      const { error: updateError } = await supabase
        .from('sinful_secrets_card_submissions')
        .update({ is_winner: true })
        .eq('game_id', game.id)
        .eq('player_id', submission.playerId)
        .eq('round_number', game.current_round);

      if (updateError) throw updateError;

      // Update player score
      const { error: scoreError } = await supabase
        .from('sinful_secrets_players')
        .update({ score: supabase.sql`score + 1` })
        .eq('id', submission.playerId);

      if (scoreError) throw scoreError;

      // Advance to sinful secrets phase
      const { error: gameError } = await supabase
        .from('sinful_secrets_games')
        .update({ 
          game_phase: 'sinful-secrets',
          is_card_phase: false 
        })
        .eq('id', game.id);

      if (gameError) throw gameError;

      const winner = players.find(p => p.id === submission.playerId);
      const aiResponse = await callAI(
        `${winner?.player_name} won the card round with: "${submission.card}". Now it's time for their sinful secrets turn!`,
        'Winner selected, transitioning to sinful secrets'
      );
      
      const aiMessage = {
        id: Date.now().toString(),
        sender: 'host',
        text: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      toast.error("Failed to select winner");
      console.error('Winner selection error:', error);
    }
  };

  const handlePlayerNumberSelection = async (playerId: string, number: number, category: string) => {
    await selectChallengeNumber(playerId, number);
    
    const aiResponse = await callAI(
      `Player selected number ${number} (${category}). Generate an appropriate ${category} challenge.`,
      'Number selection phase'
    );
    
    const aiMessage = {
      id: Date.now().toString(),
      sender: 'host',
      text: aiResponse,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, aiMessage]);
  };

  const handleCompleteSinfulSecrets = async () => {
    if (!game) return;

    try {
      // Mark challenge as completed
      if (currentChallenge) {
        const { error: challengeError } = await supabase
          .from('sinful_secrets_challenges')
          .update({ 
            is_completed: true,
            completed_at: new Date().toISOString()
          })
          .eq('id', currentChallenge.id);

        if (challengeError) throw challengeError;
      }

      // Check if all players have completed their turns
      const nextPlayerIndex = (game.current_player_index + 1) % players.length;
      const isRoundComplete = nextPlayerIndex === 0;

      if (isRoundComplete) {
        // Advance to next round
        const newRound = game.current_round + 1;
        let newIntensity = game.intensity_level;
        
        if (newRound <= 3) newIntensity = 'Playful';
        else if (newRound <= 6) newIntensity = 'Sensual';
        else if (newRound <= 9) newIntensity = 'Daring';
        else newIntensity = 'Bold';

        const { error: roundError } = await supabase
          .from('sinful_secrets_games')
          .update({ 
            current_round: newRound,
            current_player_index: 0,
            intensity_level: newIntensity,
            game_phase: 'card-submission',
            is_card_phase: true
          })
          .eq('id', game.id);

        if (roundError) throw roundError;
      } else {
        // Move to next player
        const { error: playerError } = await supabase
          .from('sinful_secrets_games')
          .update({ 
            current_player_index: nextPlayerIndex,
            game_phase: 'card-submission',
            is_card_phase: true
          })
          .eq('id', game.id);

        if (playerError) throw playerError;
      }

      const aiResponse = await callAI(
        'Sinful secrets challenge completed! Let\'s get back to our card game.',
        'Sinful secrets completed, back to cards'
      );

      const aiMessage = {
        id: Date.now().toString(),
        sender: 'host',
        text: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      toast.error("Failed to complete challenge");
      console.error('Complete challenge error:', error);
    }
  };

  // Show game creation form if no game exists
  if (!isGameCreated) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md w-full">
          <h1 className="text-4xl font-pacifico text-tnt-hotPink mb-6">Host Sinful Secrets</h1>
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-6">
            <Input
              type="text"
              placeholder="Enter your host name"
              value={hostName}
              onChange={(e) => setHostName(e.target.value)}
              className="w-full p-3 rounded bg-tnt-charcoal border border-tnt-hotPink/30 text-white placeholder-gray-400 mb-4"
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleCreateGame();
              }}
            />
            <Button
              onClick={handleCreateGame}
              disabled={isLoading || !hostName.trim()}
              className="w-full bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-3 px-6 rounded-lg transition-colors"
            >
              {isLoading ? 'Creating...' : 'Create Game'}
            </Button>
          </div>
          {error && (
            <p className="text-red-400 text-sm">{error}</p>
          )}
        </div>
      </div>
    );
  }

  // Show card deck selector modal
  if (showCardDeckSelector) {
    return (
      <CardDeckSelector
        onPacksSelected={(packs) => {
          setSelectedCardPacks(packs);
          setShowCardDeckSelector(false);
          // Auto-start game after deck selection
          setTimeout(() => handleStartGame(), 100);
        }}
        onCustomDeckSelected={(deck) => {
          setCustomDeck(deck);
          setShowCardDeckSelector(false);
          // Auto-start game after deck selection
          setTimeout(() => handleStartGame(), 100);
        }}
        onCancel={() => setShowCardDeckSelector(false)}
        selectedPacks={selectedCardPacks}
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke">
      <GameHeader
        hostName={hostName}
        gameCode={gameCode}
        playerCount={players?.length || 0}
        onStartGame={handleStartGame}
        gameStarted={game?.status === 'active'}
      />
      
      <div className="container mx-auto px-4 py-4 flex-grow flex flex-col">
        <HostLineDisplay 
          gamePhase="playing"
          roundNumber={game?.current_round || 1}
          settings={{
            aiPersona: "You are the seductive, playful host of Sinful Secrets. Your tone is flirty, encouraging, and slightly mischievous. You guide players through intimate revelations with warmth and excitement.",
            responseLength: "medium",
            turnOrder: "sequential",
            escalationSpeed: "normal",
            miniGameFrequency: 3,
            promptDifficultyScaling: true,
            isPrivateGame: true
          }}
          winner={null}
          currentPrompt={currentGamePhase === 'card-judging' ? "Judge the submitted cards" : "Current challenge in progress"}
          className="mb-4"
        />

        <div className="flex-grow flex gap-6">
          {/* Left Column - Game Area */}
          <div className="flex-1 space-y-6">
            {/* Game Status */}
            <div className="bg-tnt-deepPurple/20 rounded-lg p-4">
              <h3 className="text-lg font-bold text-tnt-hotPink mb-2">Game Status</h3>
              <div className="mb-4 p-3 bg-tnt-hotPink/20 rounded-lg border border-tnt-hotPink/30">
                <p className="text-center">
                  <span className="text-tnt-whiteSmoke/60">Game Code:</span>
                  <span className="ml-2 text-2xl font-bold text-tnt-hotPink">{gameCode}</span>
                </p>
                <p className="text-center text-sm text-tnt-whiteSmoke/60 mt-1">
                  Share this code with players to join
                </p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-tnt-whiteSmoke/60">Players:</span>
                  <span className="ml-2 font-bold">{players.length}</span>
                </div>
                <div>
                  <span className="text-tnt-whiteSmoke/60">Round:</span>
                  <span className="ml-2 font-bold">{game?.current_round || 1}</span>
                </div>
                <div>
                  <span className="text-tnt-whiteSmoke/60">Phase:</span>
                  <span className="ml-2 font-bold capitalize">{currentGamePhase.replace('-', ' ')}</span>
                </div>
                <div>
                  <span className="text-tnt-whiteSmoke/60">Intensity:</span>
                  <span className="ml-2 font-bold">{game?.intensity_level || 'Playful'}</span>
                </div>
              </div>
            </div>

            {/* Players List */}
            <div className="bg-tnt-deepPurple/20 rounded-lg p-4">
              <h3 className="text-lg font-bold text-tnt-hotPink mb-2">Players ({players?.length || 0})</h3>
              <div className="space-y-2">
                {players && players.length > 0 ? (
                  players.map((player, index) => (
                    <div key={player.id} className="flex justify-between items-center p-2 bg-tnt-charcoal/30 rounded">
                      <div className="flex items-center">
                        <span className={`font-bold ${player.is_host ? 'text-tnt-hotPink' : 'text-tnt-whiteSmoke'}`}>
                          {player.player_name}
                          {player.is_host && ' (Host)'}
                          {index === game?.current_player_index && ' 👑'}
                        </span>
                      </div>
                      <div className="text-sm text-tnt-whiteSmoke/60">
                        Score: {player.score || 0}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-tnt-whiteSmoke/60 py-4">
                    Waiting for players to join...
                  </div>
                )}
              </div>
            </div>

            {/* Game Phase Content */}
            {currentGamePhase === 'card-submission' && (
              <div className="bg-tnt-deepPurple/20 rounded-lg p-4">
                <h3 className="text-lg font-bold text-tnt-hotPink mb-2">Card Submission Phase</h3>

                {/* Current Prompt */}
                {currentPrompt && (
                  <div className="mb-4 p-4 bg-tnt-hotPink/20 rounded-lg border border-tnt-hotPink/30">
                    <h4 className="text-md font-bold text-tnt-hotPink mb-2">Current Prompt:</h4>
                    <p className="text-tnt-whiteSmoke text-lg italic">"{currentPrompt}"</p>
                  </div>
                )}

                <p className="text-tnt-whiteSmoke/80 mb-4">
                  Waiting for players to submit their cards... ({cardSubmissions?.length || 0}/{Math.max((players?.length || 1) - 1, 0)})
                </p>
                <div className="space-y-2">
                  {cardSubmissions?.map((submission, index) => (
                    <div key={submission.id} className="p-2 bg-tnt-charcoal/30 rounded">
                      <span className="text-tnt-whiteSmoke/60">Player {index + 1}:</span>
                      <span className="ml-2">{submission.card_text}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {currentGamePhase === 'card-judging' && (
              <div className="bg-tnt-deepPurple/20 rounded-lg p-4">
                <h3 className="text-lg font-bold text-tnt-hotPink mb-2">Judge the Cards</h3>
                <p className="text-tnt-whiteSmoke/80 mb-4">Select the winning card:</p>
                <div className="space-y-2">
                  {cardSubmissions?.map((submission, index) => (
                    <button
                      key={submission.id}
                      onClick={() => handleWinnerSelection(submission)}
                      className="w-full p-3 bg-tnt-charcoal/30 hover:bg-tnt-hotPink/20 rounded text-left transition-colors"
                    >
                      <span className="text-tnt-whiteSmoke/60">Option {index + 1}:</span>
                      <span className="ml-2 block">{submission.card_text}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {currentGamePhase === 'sinful-secrets' && currentPlayer && (
              <div className="bg-tnt-deepPurple/20 rounded-lg p-4">
                <h3 className="text-lg font-bold text-tnt-hotPink mb-2">Sinful Secrets Phase</h3>
                <p className="text-tnt-whiteSmoke/80 mb-4">
                  <span className="font-bold text-tnt-hotPink">{currentPlayer.player_name}</span> is selecting their challenge...
                </p>
                {currentChallenge && (
                  <div className="mt-4">
                    <p className="text-tnt-whiteSmoke/80 mb-2">Challenge Type: <span className="font-bold">{currentChallenge.challenge_type}</span></p>
                    <p className="text-tnt-whiteSmoke/80 mb-4">Selected Number: <span className="font-bold">{currentChallenge.selected_number}</span></p>
                    <Button
                      onClick={handleCompleteSinfulSecrets}
                      className="bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-2 px-4"
                    >
                      Challenge Complete
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right Column - Chat & Controls */}
          <div className="w-80 space-y-4">
            <QRCodeJoin gameCode={gameCode} />
            
            <AIChat
              messages={chatMessages}
              onSendMessage={async (message) => {
                const response = await callAI(message, 'Host chat');
                const aiMessage = {
                  id: Date.now().toString(),
                  sender: 'host',
                  text: response,
                  timestamp: new Date()
                };
                setChatMessages(prev => [...prev, aiMessage]);
              }}
              isLoading={isAiLoading}
              placeholder="Chat with your AI host..."
            />

            <AdminControlButton 
              onAction={(action) => {
                console.log('Admin action:', action);
                toast.info(`Admin action: ${action}`);
              }}
            />
          </div>
        </div>
      </div>

      <GameFooter
        players={players?.map(p => p.player_name) || []}
        onEndGame={() => {
          if (confirm('Are you sure you want to end the game?')) {
            sessionStorage.removeItem('sinfulSecretsHostName');
            sessionStorage.removeItem('sinfulSecretsGameCode');
            window.location.href = '/select-mode';
          }
        }}
      />
    </div>
  );
};

export default SinfulSecretsHostInterface;
