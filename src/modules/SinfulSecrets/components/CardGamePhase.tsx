
import React from 'react';
import { Button } from '@/components/ui/button';
import GameCard from '@/components/GameCard';

interface CardGamePhaseProps {
  currentPrompt: string;
  submissions: Array<{ playerId: string; card: string }>;
  phase: 'submission' | 'judging';
  onSelectWinner: (submission: { playerId: string; card: string }) => void;
  totalPlayers: number;
}

const CardGamePhase: React.FC<CardGamePhaseProps> = ({
  currentPrompt,
  submissions,
  phase,
  onSelectWinner,
  totalPlayers
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-pacifico text-tnt-hotPink mb-4">Card Round</h2>
        <div className="max-w-md mx-auto">
          <GameCard 
            type="black" 
            content={currentPrompt}
            className="w-full"
          />
        </div>
      </div>

      {phase === 'submission' && (
        <div className="text-center">
          <p className="text-lg text-tnt-whiteSmoke">
            Waiting for players to submit their cards...
          </p>
          <p className="text-sm text-tnt-whiteSmoke/60">
            {submissions.length} / {totalPlayers - 1} submitted
          </p>
        </div>
      )}

      {phase === 'judging' && submissions.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-bold text-center text-tnt-hotPink">
            AI Host: Choose the Best Answer!
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
            {submissions.map((submission, index) => (
              <div key={index} className="space-y-2">
                <GameCard 
                  type="white" 
                  content={submission.card}
                  onClick={() => onSelectWinner(submission)}
                  className="cursor-pointer hover:scale-105 transition-transform"
                />
                <Button
                  onClick={() => onSelectWinner(submission)}
                  className="w-full bg-tnt-hotPink hover:bg-tnt-pink"
                >
                  Select Winner
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CardGamePhase;
