
import React from 'react';
import { Button } from '@/components/ui/button';
import GameCard from '@/components/GameCard';

interface CardGamePhaseProps {
  currentPrompt: string;
  submissions: Array<{ playerId: string; card: string; isDeckCard?: boolean }>;
  phase: 'submission' | 'judging';
  onSelectWinner: (submission: { playerId: string; card: string }) => void;
  totalPlayers: number;
  currentJudge?: { id: string; player_name: string } | null;
}

const CardGamePhase: React.FC<CardGamePhaseProps> = ({
  currentPrompt,
  submissions,
  phase,
  onSelectWinner,
  totalPlayers,
  currentJudge
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-pacifico text-tnt-hotPink mb-4">Card Round</h2>
        <div className="max-w-md mx-auto">
          <GameCard 
            type="black" 
            content={currentPrompt}
            className="w-full"
          />
        </div>
      </div>

      {phase === 'submission' && (
        <div className="text-center">
          <p className="text-lg text-tnt-whiteSmoke">
            Waiting for players to submit their cards...
          </p>
          <p className="text-sm text-tnt-whiteSmoke/60">
            {submissions.length} / {totalPlayers} submitted
          </p>
        </div>
      )}

      {phase === 'judging' && submissions.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-bold text-center text-tnt-hotPink">
            {currentJudge ? `${currentJudge.player_name} is judging!` : 'Waiting for judge...'}
          </h3>
          {currentJudge && (
            <p className="text-center text-tnt-whiteSmoke/80">
              {currentJudge.player_name}, choose the best answer from the cards below!
            </p>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
            {submissions.map((submission, index) => (
              <div key={index} className="space-y-2">
                <GameCard 
                  type="white" 
                  content={submission.card}
                  onClick={() => onSelectWinner(submission)}
                  className="cursor-pointer hover:scale-105 transition-transform"
                />
                <Button
                  onClick={() => onSelectWinner(submission)}
                  className="w-full bg-tnt-hotPink hover:bg-tnt-pink"
                >
                  Select Winner
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CardGamePhase;
