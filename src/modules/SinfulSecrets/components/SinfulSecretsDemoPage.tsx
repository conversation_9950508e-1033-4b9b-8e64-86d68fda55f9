
import React, { useState } from 'react';
import SinfulSecretsHostInterface from './SinfulSecretsHostInterface';
import SinfulSecretsPlayerInterface from './SinfulSecretsPlayerInterface';

const SinfulSecretsDemoPage: React.FC = () => {
  const [pendingTruthDare, setPendingTruthDare] = useState(false);
  const [isActivePlayer, setIsActivePlayer] = useState(true);

  const handleNumberSelect = (playerId: string, number: number, category: string) => {
    console.log(`Player ${playerId} selected number ${number} (${category})`);
    if (category === 'Truth' || category === 'Dare') {
      setPendingTruthDare(true);
    }
  };

  const handleTruthDareSelect = (playerId: string, choice: 'Truth' | 'Dare') => {
    console.log(`Player ${playerId} chose ${choice}`);
    setPendingTruthDare(false);
  };

  const handleCardSubmit = (playerId: string, card: string) => {
    console.log(`Player ${playerId} submitted card: ${card}`);
  };

  return (
    <div className="flex h-screen">
      {/* Host Interface - Left Side */}
      <div className="flex-1 border-r border-gray-300">
        <div className="h-full overflow-auto">
          <SinfulSecretsHostInterface />
        </div>
      </div>
      
      {/* Player Interface - Right Side */}
      <div className="w-96 bg-gray-100">
        <div className="h-full overflow-auto">
          <SinfulSecretsPlayerInterface
            playerName="Alex"
            playerId="1"
            gameCode="SEXY123"
            onNumberSelect={handleNumberSelect}
            onTruthDareSelect={handleTruthDareSelect}
            onCardSubmit={handleCardSubmit}
            pendingTruthDare={pendingTruthDare}
            isActivePlayer={isActivePlayer}
          />
        </div>
      </div>
      
      {/* Debug Controls */}
      <div className="absolute top-4 right-4 bg-white p-4 rounded shadow-lg z-50">
        <h3 className="font-bold mb-2">Debug Controls</h3>
        <button
          onClick={() => setIsActivePlayer(!isActivePlayer)}
          className="block w-full mb-2 px-3 py-1 bg-blue-500 text-white rounded text-sm"
        >
          Toggle Active Player: {isActivePlayer ? 'Active' : 'Waiting'}
        </button>
        <button
          onClick={() => setPendingTruthDare(!pendingTruthDare)}
          className="block w-full px-3 py-1 bg-green-500 text-white rounded text-sm"
        >
          Toggle Truth/Dare: {pendingTruthDare ? 'Pending' : 'Not Pending'}
        </button>
      </div>
    </div>
  );
};

export default SinfulSecretsDemoPage;
