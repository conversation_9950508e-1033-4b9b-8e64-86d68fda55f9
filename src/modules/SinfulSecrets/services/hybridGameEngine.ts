
import { GameState, Player, Prompt, PromptResponse, PromptCategory } from '../types/gameTypes';
import { selectPrompt, personalizePrompt } from './gameEngine';

// Card-based mechanics integration
export interface CardPrompt {
  id: string;
  text: string;
  isNSFW: boolean;
  category: 'romantic' | 'playful' | 'daring' | 'wild';
}

export interface HybridGameState extends GameState {
  cardModeActive: boolean;
  currentCardPrompt: CardPrompt | null;
  cardDeck: CardPrompt[];
  usedCards: string[];
}

// Mock NSFW card deck for testing
const mockCardDeck: CardPrompt[] = [
  { id: 'card-1', text: 'Share a secret fantasy you\'ve never told anyone.', isNSFW: true, category: 'daring' },
  { id: 'card-2', text: 'Kiss the person to your right passionately.', isNSFW: true, category: 'wild' },
  { id: 'card-3', text: 'Describe your ideal romantic evening in detail.', isNSFW: false, category: 'romantic' },
  { id: 'card-4', text: 'Let someone blindfold you and guess who\'s touching your hand.', isNSFW: true, category: 'daring' },
  { id: 'card-5', text: 'Whisper your biggest turn-on to the person across from you.', isNSFW: true, category: 'wild' },
  { id: 'card-6', text: 'Give someone a sensual 30-second massage.', isNSFW: true, category: 'romantic' },
  { id: 'card-7', text: 'Act out your most embarrassing sexual moment.', isNSFW: true, category: 'playful' },
  { id: 'card-8', text: 'Let the group vote on who you should kiss.', isNSFW: true, category: 'wild' }
];

export const initializeHybridGame = (players: Player[]): HybridGameState => {
  const baseGameState = {
    round: 1,
    subset: 'A' as const,
    intensityLevel: 'Playful' as const,
    currentPrompt: null,
    activePlayer: players[0] || null,
    players: players.map(p => ({ ...p, hasPlayed: false })),
    safeWord: generateSafeWord(),
    isOverdriveMode: false,
    overdriveChance: 15,
    usedPrompts: [],
    usedNumbers: [],
    numberMapping: generateNumberMapping(),
    neverHaveIEverCounter: 0,
    gameStarted: false
  };

  return {
    ...baseGameState,
    cardModeActive: false,
    currentCardPrompt: null,
    cardDeck: [...mockCardDeck],
    usedCards: []
  };
};

const generateSafeWord = (): string => {
  const safeWords = [
    'Waffle Tango', 'Fuzzy Pickles', 'Spicy Unicorn', 'Moist Cupcake', 
    'Bacon Wiggle', 'Disco Banana', 'Silly Goose', 'Magic Taco'
  ];
  return safeWords[Math.floor(Math.random() * safeWords.length)];
};

const generateNumberMapping = (): Record<number, PromptCategory> => {
  const mapping: Record<number, PromptCategory> = {};
  const categories: PromptCategory[] = ['Truth', 'Dare', 'WouldYouRather', 'Wildcard'];
  
  // Assign categories to numbers (can be customized)
  for (let i = 1; i <= 20; i++) {
    if (i <= 8) mapping[i] = 'Truth';
    else if (i <= 12) mapping[i] = 'Dare';
    else if (i <= 16) mapping[i] = 'WouldYouRather';
    else mapping[i] = 'Wildcard';
  }
  
  return mapping;
};

// Determine if this turn should be card-based or traditional
export const shouldUseCardMode = (gameState: HybridGameState): boolean => {
  // Every 3rd round, use card mode for variety
  return gameState.round % 3 === 0 && gameState.cardDeck.length > 0;
};

// Draw a random card from the deck
export const drawCard = (gameState: HybridGameState): { gameState: HybridGameState; card: CardPrompt | null } => {
  const availableCards = gameState.cardDeck.filter(card => !gameState.usedCards.includes(card.id));
  
  if (availableCards.length === 0) {
    return { gameState, card: null };
  }
  
  const randomCard = availableCards[Math.floor(Math.random() * availableCards.length)];
  
  const updatedGameState = {
    ...gameState,
    currentCardPrompt: randomCard,
    usedCards: [...gameState.usedCards, randomCard.id],
    cardModeActive: true
  };
  
  return { gameState: updatedGameState, card: randomCard };
};

// Get hybrid prompt (either card or traditional)
export const getHybridPrompt = (gameState: HybridGameState, selectedNumber?: number): PromptResponse | { card: CardPrompt; requiresDrink: boolean } => {
  if (shouldUseCardMode(gameState)) {
    const { card } = drawCard(gameState);
    if (card) {
      return {
        card,
        requiresDrink: card.isNSFW // NSFW cards require a drink
      };
    }
  }
  
  // Fall back to traditional prompt system
  if (selectedNumber) {
    return selectPrompt(gameState, selectedNumber);
  }
  
  // Default fallback
  const randomNumber = Math.floor(Math.random() * 20) + 1;
  return selectPrompt(gameState, randomNumber);
};
