
import { HybridGameState, CardPrompt } from '../types/hybridTypes';
import { Player } from '../types/gameTypes';

export type GamePhase = 'card-submission' | 'card-judging' | 'sinful-secrets' | 'round-complete';

export interface TurnState {
  currentPlayerIndex: number;
  hasAllPlayersCompletedSinfulSecrets: boolean;
  isCardPhase: boolean;
  cardSubmissions: Array<{ playerId: string; card: string }>;
  currentSinfulSecretsPlayer: Player | null;
}

export const initializeTurnState = (players: Player[]): TurnState => ({
  currentPlayerIndex: 0,
  hasAllPlayersCompletedSinfulSecrets: false,
  isCardPhase: true,
  cardSubmissions: [],
  currentSinfulSecretsPlayer: null
});

export const getGamePhase = (gameState: HybridGameState, turnState: TurnState): GamePhase => {
  if (turnState.isCardPhase) {
    if (turnState.cardSubmissions.length < gameState.players.length - 1) {
      return 'card-submission';
    } else {
      return 'card-judging';
    }
  } else {
    return 'sinful-secrets';
  }
};

export const advanceGameFlow = (
  gameState: HybridGameState, 
  turnState: TurnState,
  action: 'card-submitted' | 'winner-selected' | 'sinful-secrets-complete'
): { gameState: HybridGameState; turnState: TurnState } => {
  const newTurnState = { ...turnState };
  const newGameState = { ...gameState };

  switch (action) {
    case 'card-submitted':
      // Card submission doesn't change turn state, just waits for more submissions
      break;

    case 'winner-selected':
      // After AI selects winner, move to sinful secrets phase
      newTurnState.isCardPhase = false;
      newTurnState.currentSinfulSecretsPlayer = gameState.players[newTurnState.currentPlayerIndex];
      newTurnState.cardSubmissions = [];
      break;

    case 'sinful-secrets-complete':
      // After sinful secrets, check if all players have gone
      newTurnState.currentPlayerIndex++;
      
      if (newTurnState.currentPlayerIndex >= gameState.players.length) {
        // Round complete - reset for next round
        newTurnState.currentPlayerIndex = 0;
        newTurnState.hasAllPlayersCompletedSinfulSecrets = true;
        newGameState.round++;
        
        // Update intensity level based on round using correct IntensityLevel values
        if (newGameState.round <= 3) {
          newGameState.intensityLevel = 'Playful';
        } else if (newGameState.round <= 6) {
          newGameState.intensityLevel = 'Sensual';
        } else if (newGameState.round <= 9) {
          newGameState.intensityLevel = 'Daring';
        } else {
          newGameState.intensityLevel = 'Bold';
        }
      }
      
      // Always go back to card phase after sinful secrets
      newTurnState.isCardPhase = true;
      newTurnState.currentSinfulSecretsPlayer = null;
      break;
  }

  return { gameState: newGameState, turnState: newTurnState };
};

export const getCurrentPrompt = (gameState: HybridGameState): string => {
  const cardPrompts = [
    "What's the most embarrassing thing that happened to you on a date?",
    "Complete this sentence: 'I've never told anyone, but I...'",
    "What's your biggest turn-on that would surprise people?",
    "Describe your most awkward intimate moment.",
    "What's the wildest place you've ever been intimate?",
    "What's your secret fantasy you've never shared?",
    "What's the most scandalous thing in your browser history?",
    "Complete: 'I get instantly attracted when someone...'",
    "What's your guilty pleasure that you're embarrassed about?",
    "What's the boldest thing you've ever done to get someone's attention?"
  ];
  
  const intensityMultiplier = gameState.round <= 3 ? 0 : 
                             gameState.round <= 6 ? 0.3 : 
                             gameState.round <= 9 ? 0.6 : 1;
  
  const promptIndex = Math.floor((cardPrompts.length - 1) * intensityMultiplier + Math.random() * 3) % cardPrompts.length;
  return cardPrompts[promptIndex];
};

export const getWhiteCardOptions = (): string[] => [
  "A passionate kiss in the rain",
  "Sending a risky text to the wrong person",
  "Getting caught in an awkward position",
  "A midnight confession of feelings",
  "Dancing seductively when nobody's watching",
  "Accidentally revealing too much information",
  "A steamy dream about someone unexpected",
  "Flirting with disaster (and enjoying it)",
  "A secret crush that everyone knows about",
  "Being way too honest after a few drinks",
  "Getting lost in someone's eyes",
  "A wardrobe malfunction at the worst time",
  "Sliding into DMs with confidence",
  "A romantic gesture gone wrong",
  "Being irresistibly charming without trying"
];
