
import { PromptCategory, IntensityLevel, GameSubset, Player, Prompt, GameState, PromptResponse } from '../types/gameTypes';
import { prompts } from '../data/prompts';

// Generate safe word combinations
const generateSafeWord = (): string => {
  const adjectives = ['Fluffy', 'Dancing', 'Sparkly', 'Wobbly', 'Jumpy', 'Squeaky', 'Fuzzy', 'Moist', 'Bacon', 'Waffle', 'Spicy'];
  const nouns = ['Unicorn', 'Pancake', 'Marshmallow', 'Banana', 'Flamingo', 'Pineapple', 'Pickles', 'Cupcake', 'Wiggle', 'Tango'];
  
  const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
  
  return `${randomAdjective} ${randomNoun}`;
};

// Determine subset based on round number
export const getSubsetFromRound = (round: number): GameSubset => {
  if (round <= 2) return 'A';
  if (round <= 5) return 'B';
  if (round <= 9) return 'C';
  return 'D';
};

// Map subset to intensity level
export const getIntensityFromSubset = (subset: GameSubset): IntensityLevel => {
  const mapping: Record<GameSubset, IntensityLevel> = {
    'A': 'Playful',
    'B': 'Sensual', 
    'C': 'Daring',
    'D': 'Bold'
  };
  return mapping[subset];
};

// Generate number to category mapping for each round
export const generateNumberMapping = (): Record<number, PromptCategory> => {
  const mapping: Record<number, PromptCategory> = {};
  
  // Assign 8 numbers to Truth or Dare
  const truthDareNumbers = new Set<number>();
  while (truthDareNumbers.size < 8) {
    truthDareNumbers.add(Math.floor(Math.random() * 20) + 1);
  }
  
  // Assign remaining numbers to Would You Rather and Wildcard
  const remainingNumbers = Array.from({length: 20}, (_, i) => i + 1)
    .filter(num => !truthDareNumbers.has(num));
  
  // Split remaining between Would You Rather and Wildcard
  const wouldYouRatherCount = Math.floor(remainingNumbers.length / 2);
  const wouldYouRatherNumbers = remainingNumbers.slice(0, wouldYouRatherCount);
  const wildcardNumbers = remainingNumbers.slice(wouldYouRatherCount);
  
  // Create mapping
  truthDareNumbers.forEach(num => {
    mapping[num] = Math.random() < 0.5 ? 'Truth' : 'Dare';
  });
  
  wouldYouRatherNumbers.forEach(num => {
    mapping[num] = 'WouldYouRather';
  });
  
  wildcardNumbers.forEach(num => {
    mapping[num] = 'Wildcard';
  });
  
  return mapping;
};

// Initialize game state
export const initializeGame = (players: Player[]): GameState => {
  return {
    round: 1,
    subset: 'A',
    intensityLevel: 'Playful',
    currentPrompt: null,
    activePlayer: players[0] || null,
    players: players.map(p => ({ ...p, hasPlayed: false, penaltyDrinks: 0, safeWordUsage: 0 })),
    safeWord: generateSafeWord(),
    isOverdriveMode: false,
    overdriveChance: 0.15,
    usedPrompts: [],
    usedNumbers: [],
    numberMapping: generateNumberMapping(),
    neverHaveIEverCounter: 0,
    gameStarted: false
  };
};

// Check if overdrive should activate
export const checkOverdriveActivation = (gameState: GameState): boolean => {
  if (gameState.round <= 6 || gameState.isOverdriveMode) return false;
  return Math.random() < gameState.overdriveChance;
};

// Get next player in sequence
export const getNextPlayer = (gameState: GameState): Player => {
  const unplayedPlayers = gameState.players.filter(p => !p.hasPlayed);
  
  if (unplayedPlayers.length === 0) {
    // Reset all players for next round
    return gameState.players[0];
  }
  
  return unplayedPlayers[0];
};

// Get available prompts for category and intensity
export const getAvailablePrompts = (
  category: PromptCategory, 
  intensity: IntensityLevel,
  usedPrompts: string[]
): Prompt[] => {
  const categoryPrompts = prompts[category] || [];
  return categoryPrompts.filter(prompt => 
    prompt.intensity === intensity && !usedPrompts.includes(prompt.id)
  );
};

// Select a random prompt
export const selectPrompt = (
  gameState: GameState, 
  selectedNumber: number
): PromptResponse => {
  const category = gameState.numberMapping[selectedNumber];
  const intensity = gameState.isOverdriveMode 
    ? getIntensityFromSubset('D') // Max intensity in overdrive
    : gameState.intensityLevel;
  
  let availablePrompts = getAvailablePrompts(category, intensity, gameState.usedPrompts);
  
  // Fallback to any available prompt in category if none found
  if (availablePrompts.length === 0) {
    const allCategoryPrompts = prompts[category] || [];
    availablePrompts = allCategoryPrompts.filter(p => !gameState.usedPrompts.includes(p.id));
  }
  
  // Final fallback
  if (availablePrompts.length === 0) {
    const fallbackPrompt: Prompt = {
      id: `fallback-${Date.now()}`,
      text: "Tell us something interesting about yourself that no one here knows.",
      category: 'Truth',
      intensity: 'Playful',
      subset: 'A'
    };
    return {
      prompt: fallbackPrompt,
      selectedNumber,
      category: 'Truth'
    };
  }
  
  const selectedPrompt = availablePrompts[Math.floor(Math.random() * availablePrompts.length)];
  
  // Determine if this number requires a drink (4 random wildcard numbers)
  const drinkNumbers = Object.entries(gameState.numberMapping)
    .filter(([_, cat]) => cat === 'Wildcard')
    .map(([num, _]) => parseInt(num))
    .slice(0, 4);
  
  return {
    prompt: selectedPrompt,
    selectedNumber,
    category,
    requiresDrink: drinkNumbers.includes(selectedNumber)
  };
};

// Personalize prompt for active player
export const personalizePrompt = (prompt: Prompt, player: Player, allPlayers: Player[]): string => {
  let personalizedText = prompt.text;
  
  // Replace player placeholders
  personalizedText = personalizedText.replace(/{player}/g, player.name);
  personalizedText = personalizedText.replace(/{playerName}/g, player.name);
  
  // Replace role if available
  if (player.role) {
    personalizedText = personalizedText.replace(/{role}/g, player.role);
  }
  
  // Replace other player references
  const otherPlayers = allPlayers.filter(p => p.id !== player.id);
  if (otherPlayers.length > 0) {
    const randomOtherPlayer = otherPlayers[Math.floor(Math.random() * otherPlayers.length)];
    personalizedText = personalizedText.replace(/{otherPlayer}/g, randomOtherPlayer.name);
  }
  
  return personalizedText;
};

// Handle safe word usage
export const handleSafeWord = (gameState: GameState, playerId: string): GameState => {
  const updatedPlayers = gameState.players.map(player => 
    player.id === playerId 
      ? { 
          ...player, 
          safeWordUsage: player.safeWordUsage + 1,
          penaltyDrinks: player.penaltyDrinks + (gameState.isOverdriveMode ? 2 : 1)
        }
      : player
  );
  
  return {
    ...gameState,
    players: updatedPlayers
  };
};

// Advance to next round
export const advanceRound = (gameState: GameState): GameState => {
  const nextRound = gameState.round + 1;
  const newSubset = getSubsetFromRound(nextRound);
  const newIntensity = getIntensityFromSubset(newSubset);
  
  // Check if overdrive should activate
  const shouldActivateOverdrive = checkOverdriveActivation(gameState);
  
  // Reset players for new round
  const resetPlayers = gameState.players.map(p => ({ ...p, hasPlayed: false }));
  
  // Generate new number mapping every few rounds
  const newNumberMapping = (nextRound % 3 === 0) 
    ? generateNumberMapping() 
    : gameState.numberMapping;
  
  // Increment Never Have I Ever counter
  const newNeverHaveIEverCounter = gameState.neverHaveIEverCounter + 1;
  
  return {
    ...gameState,
    round: nextRound,
    subset: newSubset,
    intensityLevel: newIntensity,
    players: resetPlayers,
    activePlayer: resetPlayers[0],
    currentPrompt: null,
    isOverdriveMode: shouldActivateOverdrive || gameState.isOverdriveMode,
    numberMapping: newNumberMapping,
    neverHaveIEverCounter: newNeverHaveIEverCounter,
    usedNumbers: []
  };
};

// Mark player as having played
export const markPlayerAsPlayed = (gameState: GameState, playerId: string): GameState => {
  const updatedPlayers = gameState.players.map(player =>
    player.id === playerId ? { ...player, hasPlayed: true } : player
  );
  
  return {
    ...gameState,
    players: updatedPlayers
  };
};

// Check if Never Have I Ever should trigger
export const shouldTriggerNeverHaveIEver = (gameState: GameState): boolean => {
  return gameState.neverHaveIEverCounter >= 2;
};

// Reset Never Have I Ever counter
export const resetNeverHaveIEverCounter = (gameState: GameState): GameState => {
  return {
    ...gameState,
    neverHaveIEverCounter: 0
  };
};
