
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGameMode } from '@/contexts/GameModeContext';

const TnTModule: React.FC = () => {
  const navigate = useNavigate();
  const { gameMode } = useGameMode();

  useEffect(() => {
    // Redirect if no game mode selected
    if (!gameMode || gameMode !== 'tnt') {
      navigate('/select-mode');
      return;
    }

    // Since TnT mode is selected, go directly to the lobby
    // Card pack selection will happen during game creation
    navigate('/play');
  }, [gameMode, navigate]);

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex justify-center items-center bg-tnt-charcoal">
      <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-tnt-hotPink"></div>
    </div>
  );
};

export default TnTModule;
