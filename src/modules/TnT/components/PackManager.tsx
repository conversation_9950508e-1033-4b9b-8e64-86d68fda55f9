
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { useNavigate } from 'react-router-dom';
import { getCardPacks, CardPack } from '@/services/packService';

interface PackManagerProps {
  onPacksSelected: (packIds: string[]) => void;
  availablePacks?: CardPack[];
  onStart?: () => void;
}

const PackManager: React.FC<PackManagerProps> = ({ 
  onPacksSelected, 
  availablePacks: initialPacks,
  onStart 
}) => {
  const [availablePacks, setAvailablePacks] = useState<CardPack[]>([]);
  const [selectedPacks, setSelectedPacks] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const loadPacks = async () => {
      try {
        setIsLoading(true);
        if (initialPacks) {
          setAvailablePacks(initialPacks);
          setIsLoading(false);
          return;
        }
        
        const packs = await getCardPacks();
        setAvailablePacks(packs);
        setIsLoading(false);
      } catch (error) {
        console.error("Failed to load card packs:", error);
        setIsLoading(false);
      }
    };
    
    loadPacks();
  }, [initialPacks]);

  const handlePackToggle = (pack: CardPack) => {
    setSelectedPacks(prevSelected => {
      const isCurrentlySelected = prevSelected.includes(pack.id);
      
      if (isCurrentlySelected) {
        return prevSelected.filter(id => id !== pack.id);
      } else {
        return [...prevSelected, pack.id];
      }
    });
  };

  const handleStartGame = () => {
    onPacksSelected(selectedPacks);
    if (onStart) onStart();
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading available packs...</div>;
  }

  return (
    <div className="p-6 bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack rounded-lg shadow-xl">
      <h2 className="text-3xl font-pacifico text-tnt-hotPink mb-6">Choose Your Card Packs</h2>
      
      <p className="text-tnt-whiteSmoke/80 mb-6">
        Select at least one pack to play with. Mix and match for more variety!
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        {availablePacks.map(pack => (
          <div 
            key={pack.id}
            className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
              selectedPacks.includes(pack.id) 
                ? 'border-tnt-hotPink bg-tnt-hotPink/10' 
                : 'border-tnt-whiteSmoke/20 hover:border-tnt-hotPink/50'
            }`}
            onClick={() => handlePackToggle(pack)}
          >
            <div className="flex items-center mb-2">
              <Checkbox 
                id={`pack-${pack.id}`}
                checked={selectedPacks.includes(pack.id)}
                onCheckedChange={() => handlePackToggle(pack)}
                className="mr-2"
              />
              <label 
                htmlFor={`pack-${pack.id}`}
                className="text-lg font-semibold text-tnt-whiteSmoke cursor-pointer"
              >
                {pack.packName}
              </label>
            </div>
            <p className="text-sm text-tnt-whiteSmoke/70 ml-6">{pack.description}</p>
            <div className="mt-2 ml-6 text-xs text-tnt-whiteSmoke/50">
              {pack.blackCards.length} prompts • {pack.whiteCards.length} answers
            </div>
          </div>
        ))}
      </div>
      
      <div className="flex justify-between items-center">
        <p className="text-sm text-tnt-whiteSmoke/60">
          {selectedPacks.length === 0 
            ? "Select at least one pack to continue" 
            : `${selectedPacks.length} pack${selectedPacks.length > 1 ? 's' : ''} selected`}
        </p>
        <Button
          onClick={handleStartGame}
          disabled={selectedPacks.length === 0}
          className="bg-tnt-hotPink hover:bg-tnt-hotPink/80 text-white"
        >
          Start Game
        </Button>
      </div>
    </div>
  );
};

export default PackManager;
