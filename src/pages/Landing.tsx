
import React from 'react';
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import TntLogo from "@/components/TntLogo";
import { ArrowRight } from "lucide-react";

const Landing = () => {
  return (
    <div className="min-h-screen flex flex-col bg-tnt-charcoal text-white relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-game z-0"></div>
      <div className="absolute inset-0 opacity-20 z-0 bg-[radial-gradient(circle_at_center,rgba(255,0,135,0.1)_0%,transparent_70%)]"></div>
      
      <div className="container mx-auto px-4 py-12 flex flex-col items-center justify-center flex-grow z-10 relative">
        <div className="w-full max-w-6xl mx-auto text-center">
          {/* Improved logo layout */}
          <div className="relative mb-8 inline-block">
            <div className="bg-tnt-gray/50 backdrop-blur-sm p-3 rounded-xl inline-block">
              <TntLogo size="xl" className="animate-float" />
            </div>
            
            {/* Word elements in the background with overlay effect */}
            <div className="absolute -top-8 -right-20 bg-tnt-overlay/30 backdrop-blur-sm p-4 rounded-lg text-2xl md:text-3xl font-poppins opacity-60 text-gray-300">
              Tempted
            </div>
            <div className="absolute -bottom-8 -left-20 bg-tnt-overlay/30 backdrop-blur-sm p-4 rounded-lg text-2xl md:text-3xl font-poppins opacity-60 text-gray-300">
              Twisted
            </div>
          </div>
          
          <h1 className="text-5xl sm:text-6xl font-poppins font-extrabold text-center mb-6">
            <span className="text-tnt-crimson text-shadow-contrast">Tempted</span>
            <span className="text-white">'</span>
            <span className="text-tnt-pink text-glow">n</span>
            <span className="text-white"> </span>
            <span className="text-tnt-crimson text-shadow-contrast">Twisted</span>
          </h1>
          
          <p className="text-lg md:text-xl mb-12 text-tnt-ivory max-w-2xl mx-auto">
            Light the fuse. Break the rules. The wild card game 
            that will have you laughing, blushing, and winning.
          </p>
          
          <div className="space-y-6 md:space-y-0 md:flex md:space-x-6 justify-center">
            <Button 
              asChild
              className="bg-tnt-crimson hover:bg-tnt-pink text-white font-poppins text-xl px-8 py-6 rounded-lg shadow-lg transition-all duration-300 animate-pulse-glow"
            >
              <Link to="/play" className="flex items-center">
                Start Playing <ArrowRight className="ml-2" />
              </Link>
            </Button>
            
            <Button 
              asChild
              variant="outline"
              className="border-2 border-white text-white hover:bg-white/10 font-poppins text-xl px-8 py-6 rounded-lg transition-all duration-300"
            >
              <Link to="/how-to-play">
                How to Play
              </Link>
            </Button>
          </div>
        </div>
      </div>
      
      <footer className="w-full bg-tnt-charcoal border-t border-white/10 text-white py-6 z-10">
        <div className="container mx-auto text-center">
          <p className="text-sm">
            Tempted 'n Twisted™ is for adult players 18+. Play responsibly and respectfully.
          </p>
          <p className="text-sm mt-2 text-gray-400">
            © {new Date().getFullYear()} Tempted 'n Twisted™. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
