
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'sonner';
import { useSinfulSecretsGame } from '@/hooks/useSinfulSecretsGame';
import SinfulSecretsPlayerInterface from '@/modules/SinfulSecrets/components/SinfulSecretsPlayerInterface';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const SinfulSecretsPlayer: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [gameCode, setGameCode] = useState(searchParams.get('code') || '');
  const [playerName, setPlayerName] = useState('');
  const [playerId, setPlayerId] = useState('');
  const [isJoined, setIsJoined] = useState(false);
  const [isJoining, setIsJoining] = useState(false);

  // Use the database hook
  const { joinGame } = useSinfulSecretsGame();

  useEffect(() => {
    // Check for stored session
    const storedPlayerId = sessionStorage.getItem('sinfulSecretsPlayerId');
    const storedPlayerName = sessionStorage.getItem('sinfulSecretsPlayerName');
    const storedGameCode = sessionStorage.getItem('sinfulSecretsGameCode');

    if (storedPlayerId && storedPlayerName && storedGameCode) {
      setPlayerId(storedPlayerId);
      setPlayerName(storedPlayerName);
      setGameCode(storedGameCode);
      setIsJoined(true);
    }
  }, []);

  const handleJoinGame = async (name: string, code: string) => {
    if (!name.trim() || !code.trim()) {
      toast.error('Please enter both your name and game code');
      return;
    }

    setIsJoining(true);
    try {
      // Join the game with correct parameters
      const newPlayerId = await joinGame(code, name, 'Other');
      setPlayerId(newPlayerId);
      setPlayerName(name);
      setGameCode(code);
      setIsJoined(true);

      // Store in session for persistence
      sessionStorage.setItem('sinfulSecretsPlayerId', newPlayerId);
      sessionStorage.setItem('sinfulSecretsPlayerName', name);
      sessionStorage.setItem('sinfulSecretsGameCode', code);

      toast.success(`Joined game ${code}!`);
    } catch (error) {
      toast.error('Failed to join game. Check your game code.');
      console.error('Join game error:', error);
    } finally {
      setIsJoining(false);
    }
  };

  // These handlers are no longer needed as the player interface handles them internally

  if (!isJoined) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-tnt-charcoal to-tnt-midnightBlack text-tnt-whiteSmoke p-4">
        <div className="text-center max-w-md w-full">
          <h1 className="text-4xl font-pacifico text-tnt-hotPink mb-6">Join Game</h1>
          <div className="bg-tnt-deepPurple/20 rounded-lg p-6 mb-6">
            <Input
              type="text"
              placeholder="Enter your name"
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              className="w-full p-3 rounded bg-tnt-charcoal border border-tnt-hotPink/30 text-white placeholder-gray-400 mb-4"
            />
            <Input
              type="text"
              placeholder="Enter game code"
              value={gameCode}
              onChange={(e) => setGameCode(e.target.value)}
              className="w-full p-3 rounded bg-tnt-charcoal border border-tnt-hotPink/30 text-white placeholder-gray-400 mb-4"
            />
            <Button
              onClick={() => handleJoinGame(playerName, gameCode)}
              disabled={isJoining || !playerName.trim() || !gameCode.trim()}
              className="w-full bg-tnt-hotPink hover:bg-tnt-pink text-white font-bold py-3 px-6 rounded-lg transition-colors"
            >
              {isJoining ? 'Joining...' : 'Join Game'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <SinfulSecretsPlayerInterface
      playerName={playerName}
      playerId={playerId}
      gameCode={gameCode}
    />
  );
};

export default SinfulSecretsPlayer;
