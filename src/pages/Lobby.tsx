
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import TntLogo from "@/components/TntLogo";
import JoinGameForm from "@/components/JoinGameForm";
import CreateGameForm from "@/components/CreateGameForm";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useGame } from "@/hooks/useGame";

const Lobby = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const gameCodeFromUrl = searchParams.get('code');
  const [activeTab, setActiveTab] = useState(gameCodeFromUrl ? "join" : "join");
  const { joinGame, createGame, isLoading } = useGame();
  
  const handleJoinGame = (gameCode: string, playerName: string) => {
    joinGame(gameCode, playerName);
  };
  
  const handleCreateGame = (hostName: string) => {
    createGame(hostName);
  };
  
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal via-tnt-charcoal to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center flex-grow">
        <div className="w-full max-w-md">
          <div className="mb-8 text-center">
            <TntLogo size="lg" className="mb-4" />
            <p className="text-tnt-ivory text-lg mb-8">
              Join an existing game or create a new one
            </p>
          </div>
          
          <Tabs
            defaultValue="join"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger 
                value="join" 
                className="text-lg font-poppins data-[state=active]:bg-tnt-crimson data-[state=active]:text-white"
              >
                Join Game
              </TabsTrigger>
              <TabsTrigger 
                value="create" 
                className="text-lg font-poppins data-[state=active]:bg-tnt-pink data-[state=active]:text-white"
              >
                Create Game
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="join" className="mt-0">
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-gray-700">
                <h2 className="text-2xl font-poppins font-bold mb-6 text-center">
                  Enter Game Details
                </h2>
                <JoinGameForm onJoin={handleJoinGame} loading={isLoading} initialGameCode={gameCodeFromUrl || ''} />
              </div>
            </TabsContent>
            
            <TabsContent value="create" className="mt-0">
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-gray-700">
                <h2 className="text-2xl font-poppins font-bold mb-6 text-center">
                  Create New Game
                </h2>
                <CreateGameForm onCreate={handleCreateGame} loading={isLoading} />
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="mt-8 text-center">
            <Button
              variant="link"
              onClick={() => navigate("/")}
              className="text-tnt-ivory hover:text-tnt-pink"
            >
              Back to Home
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Lobby;
