
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import TntLogo from "@/components/TntLogo";
import BlackCard from "@/components/game/BlackCard";
import PlayerHand from "@/components/game/PlayerHand";
import { LogOut } from 'lucide-react';
import { supabase } from "@/integrations/supabase/client";

// In a real app, this would come from the backend
const mockPrompt = "The worst thing to bring to a first date is __________.";
const mockWhiteCards = [
  "A questionable life choice.",
  "That thing you're too embarrassed to Google.",
  "Unexpected eye contact with a stranger.",
  "The smell of regret and pizza.",
  "That one friend who always takes things too far.",
  "The walk of shame, but make it fashion."
];

const PlayerGame = () => {
  const navigate = useNavigate();
  const [playerName, setPlayerName] = useState<string>("");
  const [gameCode, setGameCode] = useState<string>("");
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [cards, setCards] = useState(mockWhiteCards);
  const [currentPrompt, setCurrentPrompt] = useState<string>("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const storedPlayerName = sessionStorage.getItem("playerName");
    const storedGameCode = sessionStorage.getItem("gameCode");

    if (!storedPlayerName || !storedGameCode) {
      toast.error("Missing game information, returning to lobby");
      navigate("/play");
      return;
    }

    setPlayerName(storedPlayerName);
    setGameCode(storedGameCode);

    // Shuffle the cards for this player
    setCards([...mockWhiteCards].sort(() => Math.random() - 0.5));

    // Fetch current game state
    fetchGameState(storedGameCode);
  }, [navigate]);

  const fetchGameState = async (gameCode: string) => {
    try {
      // First get the game info
      const { data: game, error: gameError } = await supabase
        .from('games')
        .select('id, current_round, status')
        .eq('game_code', gameCode)
        .single();

      if (gameError) {
        console.error('Error fetching game:', gameError);
        toast.error('Failed to load game state');
        return;
      }

      if (!game) {
        setCurrentPrompt("Game not found");
        return;
      }

      // If game hasn't started or no current round, show waiting message
      if (game.status !== 'active' || !game.current_round) {
        setCurrentPrompt("Waiting for the host to start the round...");
        return;
      }

      // Get the current round's black card
      const { data: submissions, error: submissionError } = await supabase
        .from('round_submissions')
        .select(`
          black_card_id,
          black_cards!inner(card_text, pack_name)
        `)
        .eq('game_id', game.id)
        .eq('round_number', game.current_round)
        .is('player_id', null);

      if (submissionError) {
        console.error('Error fetching round submission:', submissionError);
        setCurrentPrompt("Waiting for the host to start the round...");
        return;
      }

      if (!submissions || submissions.length === 0) {
        setCurrentPrompt("Waiting for the host to start the round...");
        return;
      }

      // Get the first submission (should have the black card)
      const submission = submissions[0];
      const blackCardText = (submission.black_cards as any)?.card_text;

      if (blackCardText) {
        setCurrentPrompt(blackCardText);
      } else {
        setCurrentPrompt("Waiting for the host to start the round...");
      }

    } catch (error) {
      console.error('Error fetching game state:', error);
      toast.error('Failed to load game state');
    } finally {
      setLoading(false);
    }
  };
  
  const handlePlayCard = (card: string) => {
    // TODO: Implement actual card submission to backend
    toast.success("Card submitted successfully!");

    // Remove the played card from the hand
    setCards(cards.filter(c => c !== card));

    // Mark as submitted
    setHasSubmitted(true);

    // TODO: Listen for next round updates from backend
  };
  
  const handleLeaveGame = () => {
    if (confirm("Are you sure you want to leave the game?")) {
      sessionStorage.removeItem("playerName");
      sessionStorage.removeItem("gameCode");
      navigate("/play");
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal to-gray-900 text-white">
      <header className="bg-tnt-charcoal py-4 border-b border-gray-700">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <TntLogo size="sm" />
          <div className="text-right">
            <p className="text-sm text-tnt-ivory">Playing as <span className="font-bold text-tnt-pink">{playerName}</span></p>
            <p className="text-sm text-tnt-gold">Game Code: {gameCode}</p>
          </div>
        </div>
      </header>
      
      <div className="container mx-auto px-4 py-6 flex-grow">
        <div className="mb-6 bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 shadow-lg">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-tnt-pink mx-auto mb-4"></div>
              <p className="text-tnt-ivory">Loading game...</p>
            </div>
          ) : (
            <BlackCard prompt={currentPrompt} />
          )}
        </div>

        {!loading && (
          <PlayerHand
            cards={cards}
            onPlayCard={handlePlayCard}
            hasSubmitted={hasSubmitted}
          />
        )}
      </div>
      
      <footer className="bg-tnt-charcoal py-4 border-t border-gray-700">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Button 
            variant="ghost" 
            onClick={handleLeaveGame} 
            className="text-tnt-ivory hover:text-tnt-pink hover:bg-gray-800 gap-2"
          >
            <LogOut className="h-4 w-4" /> Leave Game
          </Button>
        </div>
      </footer>
    </div>
  );
};

export default PlayerGame;
