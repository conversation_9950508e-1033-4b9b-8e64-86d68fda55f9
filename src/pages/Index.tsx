
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import TntLogo from "@/components/TntLogo";

const Index = () => {
  const navigate = useNavigate();
  return <div className="min-h-screen flex flex-col bg-tnt-charcoal text-white relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-game z-0"></div>
      <div className="absolute inset-0 opacity-20 z-0 bg-[radial-gradient(circle_at_center,rgba(255,0,135,0.1)_0%,transparent_70%)]"></div>
      
      <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center flex-grow z-10 relative">
        <div className="mb-10">
          <TntLogo size="xl" className="animate-float" />
        </div>
        
        <h1 className="text-5xl sm:text-6xl font-poppins font-extrabold text-center mb-6">
          <span className="text-tnt-crimson text-shadow-contrast">Tempted</span>
          <span className="text-white">'</span>
          <span className="text-tnt-pink text-glow">n</span>
          <span className="text-white"> </span>
          <span className="text-tnt-crimson text-shadow-contrast">Twisted</span>
        </h1>
        
        <p className="text-xl font-montserrat text-tnt-ivory mb-12 text-center max-w-lg">
          Light the fuse. Break the rules. The wild card game 
          that will have you laughing, blushing, and winning.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-6 mt-8 w-full max-w-md mx-auto">
          <Button onClick={() => navigate("/select-mode")} className="bg-tnt-crimson hover:bg-tnt-pink text-white font-poppins font-bold text-xl py-6 px-12 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl animate-pulse-glow">
            Start Playing
          </Button>
          
          <Button onClick={() => navigate("/how-to-play")} variant="outline" className="border-2 border-white font-poppins font-medium text-lg py-6 px-12 rounded-lg transition-all duration-300 text-tnt-pink bg-tnt-ivory">
            How to Play
          </Button>
        </div>
      </div>
      
      <footer className="py-4 text-center text-tnt-ivory/60 font-montserrat text-sm z-10">
        &copy; {new Date().getFullYear()} Tempted 'n Twisted™. All rights reserved.
      </footer>


    </div>;
};
export default Index;
