
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import TntLogo from "@/components/TntLogo";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { Plus, Trash2, Edit, Search } from "lucide-react";

// Admin interfaces
interface BlackCard {
  id: string;
  card_text: string;
  category_id?: string;
}

interface WhiteCard {
  id: string;
  card_text: string;
  category_id?: string;
}

interface Wildcard {
  id: string;
  card_text: string;
  effect_type: string;
  category_id?: string;
}

interface Category {
  id: string;
  name: string;
  description?: string;
}

const Admin = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("black-cards");
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // State for each section
  const [blackCards, setBlackCards] = useState<BlackCard[]>([]);
  const [whiteCards, setWhiteCards] = useState<WhiteCard[]>([]);
  const [wildcards, setWildcards] = useState<Wildcard[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  
  // Form states
  const [blackCardText, setBlackCardText] = useState("");
  const [whiteCardText, setWhiteCardText] = useState("");
  const [wildcardText, setWildcardText] = useState("");
  const [wildcardEffectType, setWildcardEffectType] = useState("swap");
  const [categoryName, setCategoryName] = useState("");
  const [categoryDescription, setCategoryDescription] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);

  // Search states
  const [blackCardSearch, setBlackCardSearch] = useState("");
  const [whiteCardSearch, setWhiteCardSearch] = useState("");
  const [wildcardSearch, setWildcardSearch] = useState("");
  const [categorySearch, setCategorySearch] = useState("");
  
  // Mock authentication
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");

  // Example categories
  useEffect(() => {
    setCategories([
      { id: "1", name: "Standard", description: "Default card category" },
      { id: "2", name: "Adult", description: "18+ content" },
      { id: "3", name: "Party", description: "Fun party-themed cards" }
    ]);

    // Sample cards
    setBlackCards([
      { id: "1", card_text: "The worst thing to bring to a first date is __________.", category_id: "1" },
      { id: "2", card_text: "I knew it was time to leave when I saw __________.", category_id: "1" },
      { id: "3", card_text: "My secret talent is __________.", category_id: "3" }
    ]);

    setWhiteCards([
      { id: "1", card_text: "A questionable life choice.", category_id: "1" },
      { id: "2", card_text: "That thing you're too embarrassed to Google.", category_id: "2" },
      { id: "3", card_text: "Unexpected eye contact with a stranger.", category_id: "1" }
    ]);

    setWildcards([
      { id: "1", card_text: "Swap your hand with another player", effect_type: "swap", category_id: "3" },
      { id: "2", card_text: "Skip the next round", effect_type: "skip", category_id: "3" }
    ]);
  }, []);

  // Handle login
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (username === "admin" && password === "password") {
      setIsAuthenticated(true);
      toast.success("Logged in as admin");
    } else {
      toast.error("Invalid credentials");
    }
  };

  // Handlers for each card type
  const handleAddBlackCard = () => {
    if (!blackCardText.trim()) {
      toast.error("Please enter card text");
      return;
    }
    
    const newCard = {
      id: Date.now().toString(),
      card_text: blackCardText,
      category_id: selectedCategory
    };
    
    setBlackCards([...blackCards, newCard]);
    setBlackCardText("");
    toast.success("Black card added successfully");
  };
  
  const handleAddWhiteCard = () => {
    if (!whiteCardText.trim()) {
      toast.error("Please enter card text");
      return;
    }
    
    const newCard = {
      id: Date.now().toString(),
      card_text: whiteCardText,
      category_id: selectedCategory
    };
    
    setWhiteCards([...whiteCards, newCard]);
    setWhiteCardText("");
    toast.success("White card added successfully");
  };
  
  const handleAddWildcard = () => {
    if (!wildcardText.trim()) {
      toast.error("Please enter card text");
      return;
    }
    
    const newCard = {
      id: Date.now().toString(),
      card_text: wildcardText,
      effect_type: wildcardEffectType,
      category_id: selectedCategory
    };
    
    setWildcards([...wildcards, newCard]);
    setWildcardText("");
    toast.success("Wildcard added successfully");
  };
  
  const handleAddCategory = () => {
    if (!categoryName.trim()) {
      toast.error("Please enter category name");
      return;
    }
    
    const newCategory = {
      id: Date.now().toString(),
      name: categoryName,
      description: categoryDescription || undefined
    };
    
    setCategories([...categories, newCategory]);
    setCategoryName("");
    setCategoryDescription("");
    toast.success("Category added successfully");
  };
  
  // Delete handlers
  const handleDeleteBlackCard = (id: string) => {
    setBlackCards(blackCards.filter(card => card.id !== id));
    toast.success("Black card deleted");
  };
  
  const handleDeleteWhiteCard = (id: string) => {
    setWhiteCards(whiteCards.filter(card => card.id !== id));
    toast.success("White card deleted");
  };
  
  const handleDeleteWildcard = (id: string) => {
    setWildcards(wildcards.filter(card => card.id !== id));
    toast.success("Wildcard deleted");
  };
  
  const handleDeleteCategory = (id: string) => {
    setCategories(categories.filter(category => category.id !== id));
    toast.success("Category deleted");
  };

  // Filtering functions
  const filteredBlackCards = blackCards.filter(card => 
    card.card_text.toLowerCase().includes(blackCardSearch.toLowerCase())
  );

  const filteredWhiteCards = whiteCards.filter(card => 
    card.card_text.toLowerCase().includes(whiteCardSearch.toLowerCase())
  );

  const filteredWildcards = wildcards.filter(card => 
    card.card_text.toLowerCase().includes(wildcardSearch.toLowerCase())
  );

  const filteredCategories = categories.filter(category => 
    category.name.toLowerCase().includes(categorySearch.toLowerCase())
  );

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal via-tnt-charcoal to-gray-900 text-white">
        <div className="container mx-auto px-4 py-12 flex flex-col items-center justify-center flex-grow">
          <TntLogo size="lg" className="mb-8" />
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-gray-700 w-full max-w-md">
            <h1 className="text-3xl font-poppins font-bold text-tnt-pink mb-6 text-center">Admin Login</h1>
            
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <Label htmlFor="username">Username</Label>
                <Input 
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="bg-gray-700 border-gray-600"
                  placeholder="Enter username"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="password">Password</Label>
                <Input 
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="bg-gray-700 border-gray-600"
                  placeholder="Enter password"
                  required
                />
              </div>
              
              <Button type="submit" className="w-full bg-tnt-pink hover:bg-tnt-crimson">
                Login
              </Button>
            </form>
            
            <p className="text-sm text-center mt-4 text-gray-400">
              For demo: username: "admin", password: "password"
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal via-tnt-charcoal to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <TntLogo size="sm" />
          <h1 className="text-3xl font-poppins font-bold text-tnt-pink">Admin Dashboard</h1>
          <Button variant="outline" onClick={() => navigate("/")} className="text-tnt-ivory hover:text-tnt-pink border-gray-700">
            Exit
          </Button>
        </div>
        
        <Tabs
          defaultValue="black-cards"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid grid-cols-4 mb-8 max-w-3xl mx-auto">
            <TabsTrigger value="black-cards">Black Cards</TabsTrigger>
            <TabsTrigger value="white-cards">White Cards</TabsTrigger>
            <TabsTrigger value="wildcards">Wildcards</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
          </TabsList>
          
          {/* Black Cards Tab */}
          <TabsContent value="black-cards">
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-gray-700">
              <h2 className="text-2xl font-poppins font-bold mb-6">
                Manage Black Cards
              </h2>
              
              <div className="flex mb-6">
                <div className="flex-1 mr-4">
                  <Label htmlFor="black-card-text">Card Text</Label>
                  <Textarea
                    id="black-card-text"
                    value={blackCardText}
                    onChange={(e) => setBlackCardText(e.target.value)}
                    placeholder="Enter black card text (use _____ for blanks)"
                    className="h-24 bg-gray-700 border-gray-600 resize-none"
                  />
                </div>
                
                <div className="w-1/4">
                  <Label htmlFor="black-card-category">Category</Label>
                  <Select 
                    value={selectedCategory} 
                    onValueChange={setSelectedCategory}
                  >
                    <SelectTrigger className="bg-gray-700 border-gray-600">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Category</SelectItem>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <Button onClick={handleAddBlackCard} className="bg-tnt-pink hover:bg-tnt-crimson mb-6">
                <Plus className="mr-2" size={16} /> Add Black Card
              </Button>
              
              <div className="mb-6">
                <Label htmlFor="black-card-search">Search Cards</Label>
                <div className="relative">
                  <Input
                    id="black-card-search"
                    value={blackCardSearch}
                    onChange={(e) => setBlackCardSearch(e.target.value)}
                    placeholder="Search black cards..."
                    className="pl-10 bg-gray-700 border-gray-600"
                  />
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="text-xl mb-4">Existing Black Cards ({filteredBlackCards.length})</h3>
                
                <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
                  {filteredBlackCards.map(card => (
                    <div key={card.id} className="flex justify-between items-center bg-gray-700/50 p-3 rounded-lg">
                      <div>
                        <p>{card.card_text}</p>
                        <p className="text-xs text-gray-400">
                          {card.category_id && `Category: ${categories.find(c => c.id === card.category_id)?.name || 'Unknown'}`}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteBlackCard(card.id)}
                        className="text-tnt-pink hover:bg-gray-600"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  ))}
                  
                  {filteredBlackCards.length === 0 && (
                    <p className="text-center text-gray-400 py-4">No black cards found.</p>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
          
          {/* White Cards Tab */}
          <TabsContent value="white-cards">
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-gray-700">
              <h2 className="text-2xl font-poppins font-bold mb-6">
                Manage White Cards
              </h2>
              
              <div className="flex mb-6">
                <div className="flex-1 mr-4">
                  <Label htmlFor="white-card-text">Card Text</Label>
                  <Textarea
                    id="white-card-text"
                    value={whiteCardText}
                    onChange={(e) => setWhiteCardText(e.target.value)}
                    placeholder="Enter white card text"
                    className="h-24 bg-gray-700 border-gray-600 resize-none"
                  />
                </div>
                
                <div className="w-1/4">
                  <Label htmlFor="white-card-category">Category</Label>
                  <Select 
                    value={selectedCategory} 
                    onValueChange={setSelectedCategory}
                  >
                    <SelectTrigger className="bg-gray-700 border-gray-600">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Category</SelectItem>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <Button onClick={handleAddWhiteCard} className="bg-tnt-pink hover:bg-tnt-crimson mb-6">
                <Plus className="mr-2" size={16} /> Add White Card
              </Button>
              
              <div className="mb-6">
                <Label htmlFor="white-card-search">Search Cards</Label>
                <div className="relative">
                  <Input
                    id="white-card-search"
                    value={whiteCardSearch}
                    onChange={(e) => setWhiteCardSearch(e.target.value)}
                    placeholder="Search white cards..."
                    className="pl-10 bg-gray-700 border-gray-600"
                  />
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="text-xl mb-4">Existing White Cards ({filteredWhiteCards.length})</h3>
                
                <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
                  {filteredWhiteCards.map(card => (
                    <div key={card.id} className="flex justify-between items-center bg-gray-700/50 p-3 rounded-lg">
                      <div>
                        <p>{card.card_text}</p>
                        <p className="text-xs text-gray-400">
                          {card.category_id && `Category: ${categories.find(c => c.id === card.category_id)?.name || 'Unknown'}`}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteWhiteCard(card.id)}
                        className="text-tnt-pink hover:bg-gray-600"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  ))}
                  
                  {filteredWhiteCards.length === 0 && (
                    <p className="text-center text-gray-400 py-4">No white cards found.</p>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
          
          {/* Wildcards Tab */}
          <TabsContent value="wildcards">
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-gray-700">
              <h2 className="text-2xl font-poppins font-bold mb-6">
                Manage Wildcards
              </h2>
              
              <div className="flex mb-6">
                <div className="flex-1 mr-4">
                  <Label htmlFor="wildcard-text">Card Text</Label>
                  <Textarea
                    id="wildcard-text"
                    value={wildcardText}
                    onChange={(e) => setWildcardText(e.target.value)}
                    placeholder="Enter wildcard text"
                    className="h-24 bg-gray-700 border-gray-600 resize-none"
                  />
                </div>
                
                <div className="w-1/4 space-y-4">
                  <div>
                    <Label htmlFor="wildcard-effect">Effect Type</Label>
                    <Select value={wildcardEffectType} onValueChange={setWildcardEffectType}>
                      <SelectTrigger className="bg-gray-700 border-gray-600">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="swap">Swap</SelectItem>
                        <SelectItem value="skip">Skip</SelectItem>
                        <SelectItem value="draw">Draw</SelectItem>
                        <SelectItem value="challenge">Challenge</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="wildcard-category">Category</Label>
                    <Select 
                      value={selectedCategory} 
                      onValueChange={setSelectedCategory}
                    >
                      <SelectTrigger className="bg-gray-700 border-gray-600">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Category</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              
              <Button onClick={handleAddWildcard} className="bg-tnt-pink hover:bg-tnt-crimson mb-6">
                <Plus className="mr-2" size={16} /> Add Wildcard
              </Button>
              
              <div className="mb-6">
                <Label htmlFor="wildcard-search">Search Wildcards</Label>
                <div className="relative">
                  <Input
                    id="wildcard-search"
                    value={wildcardSearch}
                    onChange={(e) => setWildcardSearch(e.target.value)}
                    placeholder="Search wildcards..."
                    className="pl-10 bg-gray-700 border-gray-600"
                  />
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="text-xl mb-4">Existing Wildcards ({filteredWildcards.length})</h3>
                
                <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
                  {filteredWildcards.map(card => (
                    <div key={card.id} className="flex justify-between items-center bg-gray-700/50 p-3 rounded-lg">
                      <div>
                        <p>{card.card_text}</p>
                        <div className="text-xs text-gray-400 flex gap-2">
                          <span className="bg-gray-600 px-2 py-0.5 rounded-full">
                            {card.effect_type}
                          </span>
                          {card.category_id && (
                            <span>
                              Category: {categories.find(c => c.id === card.category_id)?.name || 'Unknown'}
                            </span>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteWildcard(card.id)}
                        className="text-tnt-pink hover:bg-gray-600"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  ))}
                  
                  {filteredWildcards.length === 0 && (
                    <p className="text-center text-gray-400 py-4">No wildcards found.</p>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
          
          {/* Categories Tab */}
          <TabsContent value="categories">
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 shadow-xl border border-gray-700">
              <h2 className="text-2xl font-poppins font-bold mb-6">
                Manage Categories
              </h2>
              
              <div className="flex mb-6">
                <div className="flex-1 mr-4">
                  <Label htmlFor="category-name">Category Name</Label>
                  <Input
                    id="category-name"
                    value={categoryName}
                    onChange={(e) => setCategoryName(e.target.value)}
                    placeholder="Enter category name"
                    className="bg-gray-700 border-gray-600"
                  />
                </div>
              </div>
              
              <div className="mb-6">
                <Label htmlFor="category-description">Description (Optional)</Label>
                <Textarea
                  id="category-description"
                  value={categoryDescription}
                  onChange={(e) => setCategoryDescription(e.target.value)}
                  placeholder="Enter category description"
                  className="h-24 bg-gray-700 border-gray-600 resize-none"
                />
              </div>
              
              <Button onClick={handleAddCategory} className="bg-tnt-pink hover:bg-tnt-crimson mb-6">
                <Plus className="mr-2" size={16} /> Add Category
              </Button>
              
              <div className="mb-6">
                <Label htmlFor="category-search">Search Categories</Label>
                <div className="relative">
                  <Input
                    id="category-search"
                    value={categorySearch}
                    onChange={(e) => setCategorySearch(e.target.value)}
                    placeholder="Search categories..."
                    className="pl-10 bg-gray-700 border-gray-600"
                  />
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="text-xl mb-4">Existing Categories ({filteredCategories.length})</h3>
                
                <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
                  {filteredCategories.map(category => (
                    <div key={category.id} className="flex justify-between items-center bg-gray-700/50 p-3 rounded-lg">
                      <div>
                        <p className="font-medium">{category.name}</p>
                        {category.description && (
                          <p className="text-sm text-gray-400">{category.description}</p>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteCategory(category.id)}
                        className="text-tnt-pink hover:bg-gray-600"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  ))}
                  
                  {filteredCategories.length === 0 && (
                    <p className="text-center text-gray-400 py-4">No categories found.</p>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Admin;
