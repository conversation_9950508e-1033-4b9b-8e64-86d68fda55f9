
import React from 'react';
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import TntLogo from "@/components/TntLogo";
import { ArrowLeft, ArrowRight } from "lucide-react";

const HowToPlay = () => {
  const steps = [
    {
      title: "Create or Join a Game",
      description: "One player creates a game as the host and gets a unique game code. Other players join using that code."
    },
    {
      title: "Black Card Prompts",
      description: "Each round, a Black Card with a prompt or fill-in-the-blank statement is displayed on the host screen."
    },
    {
      title: "Playing White Cards",
      description: "Players choose the funniest, wildest or most outrageous White Card from their hand to complete the prompt."
    },
    {
      title: "Judging & Winning",
      description: "The host chooses the winning response for each round. Win enough rounds to be crowned the Tempted 'n Twisted™ champion!"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-tnt-charcoal text-white relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-game z-0"></div>
      <div className="absolute inset-0 opacity-20 z-0 bg-[radial-gradient(circle_at_center,rgba(255,0,135,0.1)_0%,transparent_70%)]"></div>
      
      <div className="container mx-auto px-4 py-12 z-10 relative">
        <Link to="/" className="inline-flex items-center text-tnt-ivory hover:text-tnt-pink transition-colors mb-6">
          <ArrowLeft className="mr-2" size={20} />
          Back to Home
        </Link>
        
        <div className="mb-10 text-center">
          <TntLogo size="lg" className="mb-4" />
          <h1 className="text-3xl md:text-4xl font-bold text-tnt-ivory">How To Play</h1>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="glass-panel p-6 md:p-8">
            <p className="text-lg md:text-xl mb-8 text-tnt-ivory">
              Tempted 'n Twisted™ is a digital card game of outrageous combinations. Here's how it works:
            </p>
            
            <div className="space-y-8 mb-8">
              {steps.map((step, index) => (
                <div key={index} className="flex">
                  <div className="mr-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-tnt-crimson text-white font-bold text-lg">
                      {index + 1}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-poppins font-bold text-tnt-crimson mb-2">
                      {step.title}
                    </h3>
                    <p className="text-tnt-ivory">
                      {step.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="bg-tnt-overlay/30 backdrop-blur-sm rounded-lg p-6 border border-white/10 mb-8">
              <h3 className="text-xl font-poppins font-bold text-tnt-pink mb-4">
                Keep in Mind
              </h3>
              <ul className="list-disc list-inside space-y-2 text-tnt-ivory">
                <li>Tempted 'n Twisted™ is for adult players 18+ only</li>
                <li>The game is more fun with 4-8 players</li>
                <li>Keep your responses witty and wild, but always respect boundaries</li>
                <li>The best cards often use inside jokes and references to your friend group</li>
              </ul>
            </div>
            
            <div className="text-center">
              <Button 
                asChild
                className="bg-tnt-crimson hover:bg-tnt-pink text-white font-poppins text-xl px-8 py-6 rounded-lg shadow-lg transition-all duration-300 animate-pulse-glow"
              >
                <Link to="/play" className="flex items-center">
                  Ready to Play <ArrowRight className="ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <footer className="w-full bg-tnt-charcoal border-t border-white/10 text-white py-6 mt-auto z-10">
        <div className="container mx-auto text-center">
          <p className="text-sm">
            Tempted 'n Twisted™ is for adult players 18+. Play responsibly and respectfully.
          </p>
          <p className="text-sm mt-2 text-gray-400">
            © {new Date().getFullYear()} Tempted 'n Twisted™. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default HowToPlay;
