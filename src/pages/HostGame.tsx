
import React, { useEffect } from 'react';
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useGameState } from "@/hooks/useGameState";
import AIChat from "@/components/AIChat";

// Import our components
import GameHeader from "@/components/host/GameHeader";
import GameFooter from "@/components/host/GameFooter";
import WaitingLobby from "@/components/host/WaitingLobby";
import AdminControlButton from "@/components/host/AdminControlButton";
import HostLineDisplay from "@/components/host/HostLineDisplay";
import CardPackSelector from "@/components/host/CardPackSelector";
import GameBoard from "@/components/game/GameBoard";
import ResultsDisplay from "@/components/game/ResultsDisplay";


const mockChatMessages = [
  { id: '1', sender: 'host', text: 'Welcome to Tempted \'n Twisted! I\'ll be your AI host tonight. I\'m here to guide you through the game and make your experience more entertaining.', timestamp: new Date() },
  { id: '2', sender: 'player', text: 'This is going to be fun!', timestamp: new Date() },
  { id: '3', sender: 'host', text: 'Indeed! Let\'s get started with our first round. Remember, the spicier the answers, the more fun everyone has!', timestamp: new Date() },
  { id: '4', sender: 'host', text: 'For this first round, I\'ll choose something easy to get us warmed up.', timestamp: new Date() }
];

const HostGame = () => {
  const navigate = useNavigate();
  const {
    gameState,
    isLoading,
    selectCardPacks,
    startGame,
    selectWinner,
    letAISelectWinner,
    nextRound,
    toggleAIJudging,
    togglePrivateGame,
    refreshGameState,
    addPlayer
  } = useGameState();
  
  const [hostName, setHostName] = React.useState<string>("");
  const [gameCode, setGameCode] = React.useState<string>("");
  const [showPackSelector, setShowPackSelector] = React.useState(true);
  const [chatMessages, setChatMessages] = React.useState(mockChatMessages);
  const [selectedPacks, setSelectedPacks] = React.useState<string[]>([]);
  
  useEffect(() => {
    const session = sessionStorage.getItem('playerName');
    const code = sessionStorage.getItem('gameCode');

    if (!session || !code) {
      toast.error("Missing host information, returning to lobby");
      navigate("/play");
      return;
    }

    setHostName(session);
    setGameCode(code);
  }, [navigate]);

  // Poll for game state updates (new players joining, etc.)
  useEffect(() => {
    if (!gameState || gameState.phase !== 'waiting') return;

    const interval = setInterval(() => {
      refreshGameState();
    }, 3000); // Poll every 3 seconds

    return () => clearInterval(interval);
  }, [gameState, refreshGameState]);
  
  const handlePacksSelected = async (packIds: string[]) => {
    setSelectedPacks(packIds);
    if (gameState) {
      await selectCardPacks(packIds);
      setShowPackSelector(false);
    }
  };
  
  const handleStartGame = () => {
    if (selectedPacks.length === 0) {
      toast.error('Please select at least one card pack');
      return;
    }
    
    startGame();
  };
  
  const handleSelectWinner = (submission: {player: string; card: string}) => {
    if (gameState?.aiJudging) {
      letAISelectWinner();
    } else {
      selectWinner(submission);
    }
  };
  
  const handleStartNextRound = () => {
    nextRound();
  };
  
  const handleTogglePrivateGame = () => {
    togglePrivateGame();
  };
  
  const handleEndGame = () => {
    if (confirm("Are you sure you want to end the game?")) {
      sessionStorage.removeItem("gameState");
      sessionStorage.removeItem("playerName");
      sessionStorage.removeItem("gameCode");
      navigate("/play");
    }
  };

  const handleAddTestPlayer = () => {
    const testPlayerNames = ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve'];
    const currentPlayerCount = gameState?.players.length || 0;
    const nextPlayerName = testPlayerNames[currentPlayerCount - 1] || `Player${currentPlayerCount}`;
    const testPlayerId = `test-player-${Date.now()}`;

    addPlayer(testPlayerId, nextPlayerName);
  };
  
  const sendChatMessage = async (message: string) => {
    const newMessage = {
      id: Date.now().toString(),
      sender: 'player',
      text: message,
      timestamp: new Date()
    };
    
    setChatMessages(prev => [...prev, newMessage]);
    
    setTimeout(() => {
      const aiResponse = {
        id: (Date.now() + 1).toString(),
        sender: 'host',
        text: `I received your message: "${message}". Let's continue with the game!`,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };
  
  const isDevMode = process.env.NODE_ENV === 'development' || localStorage.getItem('devMode') === 'true';
  
  if (!gameState) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }
  
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-tnt-charcoal to-black text-white relative">
      <GameHeader hostName={hostName} gameCode={gameCode} />
      
      <AdminControlButton gameId={gameState?.gameId || (isDevMode ? 'dev-game-id' : undefined)} />
      
      <div className="container mx-auto px-4 py-4 flex-grow flex flex-col">
        {gameState.isPrivateGame && (
          <HostLineDisplay 
            gamePhase={gameState.phase}
            roundNumber={gameState.roundNumber}
            settings={{
              aiPersona: "You are the fun, flirty host of Tempted 'n Twisted, a spicy party game. Your tone is playful, mischievous, and a bit suggestive, but never crude or explicit. You guide players through the game with enthusiasm and humor.",
              responseLength: "medium",
              turnOrder: "sequential",
              escalationSpeed: "normal",
              miniGameFrequency: 3,
              promptDifficultyScaling: true,
              isPrivateGame: true
            }}
            winner={gameState.winner}
            currentPrompt={gameState.currentPrompt}
            className="mb-6"
          />
        )}
        
        {gameState.phase !== "waiting" && gameState.isPrivateGame && (
          <div className="w-full mb-6">
            <div className="max-w-4xl mx-auto h-64">
              <AIChat 
                gameId={gameState?.gameId || 'dev-game-id'}
                playerId="host"
                playerName={hostName}
                onSendMessage={sendChatMessage}
                messages={chatMessages}
              />
            </div>
          </div>
        )}
        
        {gameState.phase === "waiting" && (
          <>
            {showPackSelector ? (
              <CardPackSelector
                onPacksSelected={handlePacksSelected}
                initialSelectedPacks={selectedPacks}
              />
            ) : (
              <WaitingLobby 
                gameCode={gameCode}
                players={gameState.players.map(p => p.name)}
                isPrivateGame={gameState.isPrivateGame}
                onTogglePrivateGame={handleTogglePrivateGame}
                onStartGame={handleStartGame}
              />
            )}
          </>
        )}
        
        {(gameState.phase === "playing" || gameState.phase === "judging") && (
          <GameBoard 
            gameState={gameState}
            onSelectWinner={gameState.phase === "judging" ? handleSelectWinner : undefined}
          />
        )}
        
        {gameState.phase === "results" && gameState.winner && (
          <ResultsDisplay 
            roundNumber={gameState.roundNumber}
            winner={gameState.winner}
            prompt={gameState.currentPrompt}
            onStartNextRound={handleStartNextRound}
          />
        )}
      </div>
      
      <GameFooter
        players={gameState.players.map(p => p.name)}
        onEndGame={handleEndGame}
      />


    </div>
  );
};

export default HostGame;
