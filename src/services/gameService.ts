import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";

// Type assertion helper to bypass corrupted Database types
const supabaseAny = supabase as any;

// Generate a random 6-character game code
export const generateGameCode = (): string => {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

// Define interface for game options
export interface GameOptions {
  maxRounds: number;
  maxScore: number;
  maxPlayers: number;
  aiJudging: boolean;
  submissionTimer: number;
  judgingTimer: number;
}

// Create a new game
export const createGame = async (hostName: string, options: GameOptions) => {
  try {
    // Generate a unique game code
    const gameCode = generateGameCode();
    
    // Insert the game
    const { data: gameData, error: gameError } = await supabaseAny
      .from('games')
      .insert({
        game_code: gameCode,
        max_rounds: options.maxRounds,
        max_score: options.maxScore,
        max_players: options.maxPlayers,
        ai_judging: options.aiJudging,
        submission_timer: options.submissionTimer,
        judging_timer: options.judgingTimer,
        status: 'waiting'
      })
      .select('id, game_code')
      .single();

    if (gameError) throw gameError;
    
    const deviceToken = uuidv4();
    
    // Create host player
    const { data: playerData, error: playerError } = await supabaseAny
      .from('players')
      .insert({
        game_id: gameData.id,
        player_name: hostName,
        is_host: true,
        device_token: deviceToken
      })
      .select('id')
      .single();
      
    if (playerError) throw playerError;
    
    // Update the game with the host ID
    const { error: updateError } = await supabaseAny
      .from('games')
      .update({ host_id: playerData.id })
      .eq('id', gameData.id);
      
    if (updateError) throw updateError;
    
    return {
      gameId: gameData.id,
      gameCode: gameData.game_code,
      playerId: playerData.id,
      deviceToken
    };
  } catch (error) {
    console.error('Error creating game:', error);
    throw error;
  }
};

// Join an existing game
export const joinGame = async (gameCode: string, playerName: string) => {
  try {
    // Find the game by code
    const { data: gameData, error: gameError } = await supabaseAny
      .from('games')
      .select('id, status, max_players')
      .eq('game_code', gameCode)
      .single();
      
    if (gameError) throw new Error("Game not found");
    if (gameData.status !== 'waiting') throw new Error("Game already started");
    
    // Check if the game is full
    const { count, error: countError } = await supabaseAny
      .from('players')
      .select('id', { count: 'exact', head: true })
      .eq('game_id', gameData.id);
      
    if (countError) throw countError;
    if (count && count >= gameData.max_players) throw new Error("Game is full");
    
    // Create player
    const deviceToken = uuidv4();
    const { data: playerData, error: playerError } = await supabaseAny
      .from('players')
      .insert({
        game_id: gameData.id,
        player_name: playerName,
        device_token: deviceToken
      })
      .select('id')
      .single();
      
    if (playerError) throw playerError;
    
    return {
      gameId: gameData.id,
      gameCode,
      playerId: playerData.id,
      deviceToken
    };
  } catch (error) {
    console.error('Error joining game:', error);
    throw error;
  }
};

// Get game state
export const getGameState = async (gameId: string) => {
  try {
    const { data, error } = await supabaseAny
      .from('games')
      .select(`
        id, game_code, status, current_round, max_rounds, max_score, 
        max_players, ai_judging, submission_timer, judging_timer,
        players(id, player_name, score, is_host, is_connected)
      `)
      .eq('id', gameId)
      .single();
      
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Error getting game state:', error);
    throw error;
  }
};

// Start game
export const startGame = async (gameId: string) => {
  try {
    const { error } = await supabaseAny
      .from('games')
      .update({ 
        status: 'active',
        current_round: 1
      })
      .eq('id', gameId);
      
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Error starting game:', error);
    throw error;
  }
};

// Get cards for a round
export const getRoundCards = async (gameId: string, round: number) => {
  try {
    // Get a random black card
    const { data: blackCard, error: blackCardError } = await supabaseAny
      .from('black_cards')
      .select('id, card_text')
      .order('RANDOM()')
      .limit(1)
      .single();
      
    if (blackCardError) throw blackCardError;
    
    return {
      blackCard
    };
  } catch (error) {
    console.error('Error getting round cards:', error);
    throw error;
  }
};

// Deal white cards to a player
export const dealWhiteCardsToPlayer = async (playerId: string, count: number = 5) => {
  try {
    // Get random white cards
    const { data: whiteCards, error: whiteCardsError } = await supabaseAny
      .from('white_cards')
      .select('id, card_text')
      .order('RANDOM()')
      .limit(count);
      
    if (whiteCardsError) throw whiteCardsError;
    
    // Add cards to player's hand
    const playerCards = whiteCards.map((card: any) => ({
      player_id: playerId,
      white_card_id: card.id
    }));
    
    const { error: insertError } = await supabaseAny
      .from('player_cards')
      .insert(playerCards);
      
    if (insertError) throw insertError;
    
    return whiteCards;
  } catch (error) {
    console.error('Error dealing white cards:', error);
    throw error;
  }
};

// Get player's hand
export const getPlayerHand = async (playerId: string) => {
  try {
    const { data, error } = await supabaseAny
      .from('player_cards')
      .select(`
        id,
        white_card_id,
        white_cards(id, card_text)
      `)
      .eq('player_id', playerId)
      .is('round_used', null);
      
    if (error) throw error;
    
    return data.map((item: any) => ({
      id: item.id,
      cardId: item.white_card_id,
      text: item.white_cards.card_text
    }));
  } catch (error) {
    console.error('Error getting player hand:', error);
    throw error;
  }
};

// Submit card for a round
export const submitCard = async (gameId: string, roundNumber: number, playerId: string, blackCardId: string, whiteCardId: string) => {
  try {
    // Create submission
    const { error: submissionError } = await supabaseAny
      .from('round_submissions')
      .insert({
        game_id: gameId,
        round_number: roundNumber,
        player_id: playerId,
        black_card_id: blackCardId,
        white_card_id: whiteCardId
      });
      
    if (submissionError) throw submissionError;
    
    // Mark card as used
    const { error: updateError } = await supabaseAny
      .from('player_cards')
      .update({ round_used: roundNumber })
      .eq('player_id', playerId)
      .eq('white_card_id', whiteCardId);
      
    if (updateError) throw updateError;
    
    return true;
  } catch (error) {
    console.error('Error submitting card:', error);
    throw error;
  }
};

// Get round submissions
export const getRoundSubmissions = async (gameId: string, roundNumber: number) => {
  try {
    const { data, error } = await supabaseAny
      .from('round_submissions')
      .select(`
        id,
        player_id, 
        white_card_id,
        white_cards(card_text),
        is_winner,
        players(player_name)
      `)
      .eq('game_id', gameId)
      .eq('round_number', roundNumber);
      
    if (error) throw error;
    
    return data.map((submission: any) => ({
      id: submission.id,
      playerId: submission.player_id,
      playerName: submission.players.player_name,
      whiteCardId: submission.white_card_id,
      whiteCardText: submission.white_cards.card_text,
      isWinner: submission.is_winner
    }));
  } catch (error) {
    console.error('Error getting round submissions:', error);
    throw error;
  }
};

// Pick winner for a round
export const pickWinner = async (submissionId: string, gameId: string, playerId: string) => {
  try {
    // Update the submission to mark it as winner
    const { error: updateError } = await supabaseAny
      .from('round_submissions')
      .update({ is_winner: true })
      .eq('id', submissionId);
      
    if (updateError) throw updateError;
    
    // Increment the player's score
    const { data: submission, error: submissionError } = await supabaseAny
      .from('round_submissions')
      .select('player_id')
      .eq('id', submissionId)
      .single();
      
    if (submissionError) throw submissionError;
    
    // First get the current score
    const { data: playerData, error: getPlayerError } = await supabaseAny
      .from('players')
      .select('score')
      .eq('id', submission.player_id)
      .single();
      
    if (getPlayerError) throw getPlayerError;
    
    // Then update with incremented score
    const newScore = (playerData.score || 0) + 1;
    
    const { error: playerError } = await supabaseAny
      .from('players')
      .update({ score: newScore })
      .eq('id', submission.player_id);
      
    if (playerError) throw playerError;
    
    return true;
  } catch (error) {
    console.error('Error picking winner:', error);
    throw error;
  }
};

// Start next round
export const startNextRound = async (gameId: string) => {
  try {
    // First get the current round
    const { data: gameData, error: getGameError } = await supabaseAny
      .from('games')
      .select('current_round')
      .eq('id', gameId)
      .single();
      
    if (getGameError) throw getGameError;
    
    // Then update with incremented round
    const newRound = (gameData.current_round || 0) + 1;
    
    const { data, error } = await supabaseAny
      .from('games')
      .update({ current_round: newRound })
      .eq('id', gameId)
      .select('current_round')
      .single();
      
    if (error) throw error;
    
    return data.current_round;
  } catch (error) {
    console.error('Error starting next round:', error);
    throw error;
  }
};

// End game
export const endGame = async (gameId: string) => {
  try {
    const { error } = await supabaseAny
      .from('games')
      .update({ status: 'completed' })
      .eq('id', gameId);
      
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Error ending game:', error);
    throw error;
  }
};
