
import { supabase } from "@/integrations/supabase/client";
import originalPack from '@/data/packs/original-pack.json';
import spicyPack from '@/data/packs/spicy-pack.json';
import partyPack from '@/data/packs/party-pack.json';
import afterDarkPack from '@/data/packs/after-dark-pack.json';
import daredevilPack from '@/data/packs/daredevil-pack.json';

export interface CardPack {
  id: string;
  packName: string;
  description: string;
  category: string;
  blackCards: string[];
  whiteCards: string[];
  isCustom?: boolean;
  creatorId?: string;
  isPublic?: boolean;
}

export interface CustomCardPack {
  id: string;
  creator_id: string;
  name: string;
  description: string | null;
  black_cards: string[];
  white_cards: string[];
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

// Load card packs from our JSON files
const staticCardPacks: CardPack[] = [
  originalPack,
  spicyPack,
  partyPack,
  afterDarkPack,
  daredevilPack
];

// Convert custom pack from database format to CardPack format
const convertCustomPack = (customPack: any): CardPack => ({
  id: customPack.id,
  packName: customPack.name,
  description: customPack.description || '',
  category: 'custom',
  blackCards: Array.isArray(customPack.black_cards) ? customPack.black_cards : [],
  whiteCards: Array.isArray(customPack.white_cards) ? customPack.white_cards : [],
  isCustom: true,
  creatorId: customPack.creator_id,
  isPublic: customPack.is_public
});

// Get available card packs (static + user's custom + public custom)
export const getCardPacks = async (): Promise<CardPack[]> => {
  try {
    // Get current session first
    const { data: { session } } = await supabase.auth.getSession();
    const userId = session?.user?.id;
    
    // Get user's custom decks and public custom decks
    const { data: customPacks, error } = await supabase
      .from('custom_card_packs')
      .select('*')
      .or(userId ? `creator_id.eq.${userId},is_public.eq.true` : 'is_public.eq.true');

    if (error) {
      console.error('Error fetching custom packs:', error);
      return staticCardPacks;
    }

    const customCardPacks = customPacks?.map(convertCustomPack) || [];
    return [...staticCardPacks, ...customCardPacks];
  } catch (error) {
    console.error('Error in getCardPacks:', error);
    return staticCardPacks;
  }
};

// Get specific card pack
export const getCardPack = async (packId: string): Promise<CardPack | null> => {
  // Check static packs first
  const staticPack = staticCardPacks.find(p => p.id === packId);
  if (staticPack) return staticPack;

  // Check custom packs
  try {
    const { data: customPack, error } = await supabase
      .from('custom_card_packs')
      .select('*')
      .eq('id', packId)
      .single();

    if (error || !customPack) return null;
    return convertCustomPack(customPack);
  } catch (error) {
    console.error('Error fetching custom pack:', error);
    return null;
  }
};

// Save a new custom card pack
export const saveCustomCardPack = async (
  name: string,
  description: string,
  blackCards: string[],
  whiteCards: string[],
  isPublic: boolean = false
): Promise<CardPack | null> => {
  try {
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Session error:', sessionError);
      throw new Error('Failed to verify authentication status');
    }
    
    if (!session?.user) {
      console.error('No authenticated user found in session');
      throw new Error('User must be authenticated to save custom decks');
    }

    console.log('Saving deck for user:', session.user.id);

    const { data, error } = await supabase
      .from('custom_card_packs')
      .insert([{
        creator_id: session.user.id,
        name,
        description,
        black_cards: blackCards,
        white_cards: whiteCards,
        is_public: isPublic
      }])
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      throw error;
    }
    
    return convertCustomPack(data);
  } catch (error) {
    console.error('Error saving custom pack:', error);
    throw error;
  }
};

// Update an existing custom card pack
export const updateCustomCardPack = async (
  packId: string,
  updates: Partial<{
    name: string;
    description: string;
    black_cards: string[];
    white_cards: string[];
    is_public: boolean;
  }>
): Promise<CardPack | null> => {
  try {
    const { data, error } = await supabase
      .from('custom_card_packs')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', packId)
      .select()
      .single();

    if (error) throw error;
    return convertCustomPack(data);
  } catch (error) {
    console.error('Error updating custom pack:', error);
    throw error;
  }
};

// Delete a custom card pack
export const deleteCustomCardPack = async (packId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('custom_card_packs')
      .delete()
      .eq('id', packId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting custom pack:', error);
    return false;
  }
};

// Get user's custom card packs
export const getUserCustomCardPacks = async (): Promise<CardPack[]> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) return [];

    const { data: customPacks, error } = await supabase
      .from('custom_card_packs')
      .select('*')
      .eq('creator_id', session.user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return customPacks?.map(convertCustomPack) || [];
  } catch (error) {
    console.error('Error fetching user custom packs:', error);
    return [];
  }
};

// Get public custom card packs
export const getPublicCustomCardPacks = async (): Promise<CardPack[]> => {
  try {
    const { data: customPacks, error } = await supabase
      .from('custom_card_packs')
      .select('*')
      .eq('is_public', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return customPacks?.map(convertCustomPack) || [];
  } catch (error) {
    console.error('Error fetching public custom packs:', error);
    return [];
  }
};

// Get combined cards from multiple packs
export const getCombinedCards = async (packIds: string[]): Promise<{blackCards: string[], whiteCards: string[]}> => {
  const result = {
    blackCards: [] as string[],
    whiteCards: [] as string[]
  };
  
  // Get all packs (static and custom)
  const allPacks = await getCardPacks();
  const packs = allPacks.filter(pack => packIds.includes(pack.id));
  
  // Combine all cards
  packs.forEach(pack => {
    result.blackCards.push(...pack.blackCards);
    result.whiteCards.push(...pack.whiteCards);
  });
  
  return result;
};
