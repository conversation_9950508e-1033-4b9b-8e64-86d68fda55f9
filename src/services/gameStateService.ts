
import { CardPack, getCombinedCards } from "./packService";

export type GamePhase = "waiting" | "playing" | "judging" | "results";

export interface Player {
  id: string;
  name: string;
  score: number;
  hand: string[];
  isHost: boolean;
  hasSubmitted?: boolean;
  currentSubmission?: string;
}

export interface GameState {
  gameId: string;
  gameCode: string;
  hostId: string;
  phase: GamePhase;
  roundNumber: number;
  currentPrompt: string;
  players: Player[];
  submissions: Array<{player: string; card: string}>;
  winner: {player: string; card: string} | null;
  blackCardDeck: string[];
  whiteCardDeck: string[];
  selectedPacks: string[];
  isPrivateGame: boolean;
  aiJudging: boolean;
}

/**
 * Initialize a new game state
 */
export const initializeGameState = (
  gameId: string,
  gameCode: string,
  hostId: string,
  hostName: string,
  isPrivateGame: boolean = false
): GameState => {
  return {
    gameId,
    gameCode,
    hostId,
    phase: "waiting",
    roundNumber: 1,
    currentPrompt: "",
    players: [{
      id: hostId,
      name: hostN<PERSON>,
      score: 0,
      hand: [],
      isHost: true
    }],
    submissions: [],
    winner: null,
    blackCardDeck: [],
    whiteCardDeck: [],
    selectedPacks: [],
    isPrivateGame,
    aiJudging: false
  };
};

/**
 * Load selected card packs into the game state
 */
export const loadCardPacks = async (gameState: GameState, packIds: string[]): Promise<GameState> => {
  const { blackCards, whiteCards } = await getCombinedCards(packIds);
  
  // Shuffle the cards
  const shuffledBlackCards = shuffleArray([...blackCards]);
  const shuffledWhiteCards = shuffleArray([...whiteCards]);
  
  return {
    ...gameState,
    blackCardDeck: shuffledBlackCards,
    whiteCardDeck: shuffledWhiteCards,
    selectedPacks: packIds
  };
};

/**
 * Draw cards from the white card deck
 */
export const drawWhiteCards = (gameState: GameState, playerId: string, count: number): {gameState: GameState, cards: string[]} => {
  const newGameState = {...gameState};
  const cards: string[] = [];
  
  // Find the player
  const playerIndex = newGameState.players.findIndex(p => p.id === playerId);
  if (playerIndex === -1) return {gameState: newGameState, cards};
  
  // Draw cards
  for (let i = 0; i < count; i++) {
    if (newGameState.whiteCardDeck.length === 0) break;
    
    const card = newGameState.whiteCardDeck.pop();
    if (card) {
      cards.push(card);
      newGameState.players[playerIndex].hand.push(card);
    }
  }
  
  return {gameState: newGameState, cards};
};

/**
 * Draw a black card from the deck
 */
export const drawBlackCard = (gameState: GameState): {gameState: GameState, prompt: string} => {
  const newGameState = {...gameState};
  
  if (newGameState.blackCardDeck.length === 0) {
    return {gameState: newGameState, prompt: "No more prompts available!"};
  }
  
  const prompt = newGameState.blackCardDeck.pop() || "Default prompt";
  newGameState.currentPrompt = prompt;
  
  return {gameState: newGameState, prompt};
};

/**
 * Submit a card for the current round
 */
export const submitCard = (
  gameState: GameState, 
  playerId: string, 
  card: string
): GameState => {
  const newGameState = {...gameState};
  
  // Find the player
  const playerIndex = newGameState.players.findIndex(p => p.id === playerId);
  if (playerIndex === -1) return newGameState;
  
  // Find the player's name
  const playerName = newGameState.players[playerIndex].name;
  
  // Remove the card from hand
  const handIndex = newGameState.players[playerIndex].hand.indexOf(card);
  if (handIndex !== -1) {
    newGameState.players[playerIndex].hand.splice(handIndex, 1);
  }
  
  // Add to submissions
  newGameState.submissions.push({player: playerName, card});
  
  // Mark player as having submitted
  newGameState.players[playerIndex].hasSubmitted = true;
  newGameState.players[playerIndex].currentSubmission = card;
  
  return newGameState;
};

/**
 * Check if all players have submitted
 */
export const haveAllPlayersSubmitted = (gameState: GameState): boolean => {
  return gameState.players.every(player => player.isHost || player.hasSubmitted);
};

/**
 * AI judging to pick a winner
 */
export const pickAIWinner = (gameState: GameState): {gameState: GameState, winner: {player: string; card: string}} => {
  const newGameState = {...gameState};
  
  // If no submissions, return current state
  if (newGameState.submissions.length === 0) {
    return {gameState: newGameState, winner: {player: "No one", card: "No submissions"}};
  }
  
  // Simple AI: Pick a random submission
  const winnerIndex = Math.floor(Math.random() * newGameState.submissions.length);
  const winner = newGameState.submissions[winnerIndex];
  newGameState.winner = winner;
  
  // Update player score
  const playerIndex = newGameState.players.findIndex(p => p.name === winner.player);
  if (playerIndex !== -1) {
    newGameState.players[playerIndex].score += 1;
  }
  
  return {gameState: newGameState, winner};
};

/**
 * Pick a winner manually
 */
export const pickWinner = (gameState: GameState, submission: {player: string; card: string}): GameState => {
  const newGameState = {...gameState};
  newGameState.winner = submission;
  
  // Update player score
  const playerIndex = newGameState.players.findIndex(p => p.name === submission.player);
  if (playerIndex !== -1) {
    newGameState.players[playerIndex].score += 1;
  }
  
  return newGameState;
};

/**
 * Move to the next round
 */
export const startNextRound = (gameState: GameState): GameState => {
  const newGameState = {
    ...gameState,
    phase: "playing" as GamePhase,
    roundNumber: gameState.roundNumber + 1,
    submissions: [],
    winner: null,
  };
  
  // Reset player submissions
  newGameState.players = newGameState.players.map(player => ({
    ...player,
    hasSubmitted: false,
    currentSubmission: undefined
  }));
  
  // Draw a new black card
  const { gameState: updatedState, prompt } = drawBlackCard(newGameState);
  
  return updatedState;
};

/**
 * Utility to shuffle an array
 */
export const shuffleArray = <T>(array: T[]): T[] => {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};
