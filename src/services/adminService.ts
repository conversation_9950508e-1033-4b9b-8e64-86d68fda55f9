
import { supabase } from "@/integrations/supabase/client";
import { AdminSettings } from "@/components/AdminControlPanel";

// Type assertion helper to bypass corrupted Database types
const supabaseAny = supabase as any;

// Get all black cards
export const getBlackCards = async (categoryId?: string) => {
  try {
    let query = supabaseAny
      .from('black_cards')
      .select('id, card_text, category_id, created_at');
    
    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }
    
    const { data, error } = await query;
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting black cards:', error);
    throw error;
  }
};

// Get all white cards
export const getWhiteCards = async (categoryId?: string) => {
  try {
    let query = supabaseAny
      .from('white_cards')
      .select('id, card_text, category_id, created_at');
    
    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }
    
    const { data, error } = await query;
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting white cards:', error);
    throw error;
  }
};

// Get all wildcards
export const getWildcards = async (categoryId?: string) => {
  try {
    let query = supabaseAny
      .from('wildcards')
      .select('id, card_text, effect_type, category_id, created_at');
    
    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }
    
    const { data, error } = await query;
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting wildcards:', error);
    throw error;
  }
};

// Get all categories
export const getCategories = async () => {
  try {
    const { data, error } = await supabaseAny
      .from('card_categories')
      .select('id, name, description');
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting categories:', error);
    throw error;
  }
};

// Add a black card
export const addBlackCard = async (cardText: string, categoryId?: string) => {
  try {
    const { data, error } = await supabaseAny
      .from('black_cards')
      .insert({ card_text: cardText, category_id: categoryId })
      .select('id')
      .single();
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding black card:', error);
    throw error;
  }
};

// Add a white card
export const addWhiteCard = async (cardText: string, categoryId?: string) => {
  try {
    const { data, error } = await supabaseAny
      .from('white_cards')
      .insert({ card_text: cardText, category_id: categoryId })
      .select('id')
      .single();
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding white card:', error);
    throw error;
  }
};

// Add a wildcard
export const addWildcard = async (cardText: string, effectType: string, categoryId?: string) => {
  try {
    const { data, error } = await supabaseAny
      .from('wildcards')
      .insert({ 
        card_text: cardText, 
        effect_type: effectType,
        category_id: categoryId 
      })
      .select('id')
      .single();
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding wildcard:', error);
    throw error;
  }
};

// Delete a card (any type)
export const deleteCard = async (tableName: 'black_cards' | 'white_cards' | 'wildcards', cardId: string) => {
  try {
    const { error } = await supabaseAny
      .from(tableName)
      .delete()
      .eq('id', cardId);
    
    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting ${tableName.replace('_', ' ')}:`, error);
    throw error;
  }
};

// Save admin preset
export const savePreset = async (presetName: string, settings: AdminSettings) => {
  try {
    const settingsJson = JSON.parse(JSON.stringify(settings));
    
    const { data, error } = await supabaseAny
      .from('admin_presets')
      .insert({
        preset_name: presetName,
        settings_json: settingsJson
      })
      .select('id')
      .single();
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error saving preset:', error);
    throw error;
  }
};

// Load all presets
export const loadPresets = async () => {
  try {
    const { data, error } = await supabaseAny
      .from('admin_presets')
      .select('id, preset_name')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    return data?.map((preset: any) => ({
      id: preset.id,
      name: preset.preset_name
    })) || [];
  } catch (error) {
    console.error('Error loading presets:', error);
    throw error;
  }
};

// Load a specific preset
export const loadPreset = async (presetId: string): Promise<AdminSettings> => {
  try {
    const { data, error } = await supabaseAny
      .from('admin_presets')
      .select('settings_json')
      .eq('id', presetId)
      .single();
    
    if (error) throw error;
    
    const settingsJson = data.settings_json;
    
    const settings: AdminSettings = {
      aiPersona: (settingsJson as any)?.aiPersona as string || "",
      responseLength: ((settingsJson as any)?.responseLength as "short" | "medium" | "long") || "medium",
      turnOrder: ((settingsJson as any)?.turnOrder as "random" | "sequential" | "host") || "sequential",
      escalationSpeed: ((settingsJson as any)?.escalationSpeed as "normal" | "fast" | "slow") || "normal",
      miniGameFrequency: ((settingsJson as any)?.miniGameFrequency as number) || 3,
      promptDifficultyScaling: ((settingsJson as any)?.promptDifficultyScaling as boolean) || false,
      isPrivateGame: ((settingsJson as any)?.isPrivateGame as boolean) || false
    };
    
    return settings;
  } catch (error) {
    console.error('Error loading preset:', error);
    throw error;
  }
};

// Update game settings
export const updateGameSettings = async (gameId: string, settings: AdminSettings) => {
  try {
    const settingsJson = JSON.parse(JSON.stringify(settings));
    
    const { error } = await supabaseAny
      .from('games')
      .update({
        settings: settingsJson,
        updated_at: new Date().toISOString()
      })
      .eq('id', gameId);
    
    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error updating game settings:', error);
    throw error;
  }
};

// Force escalation in the game
export const forceEscalation = async (gameId: string) => {
  try {
    const { data, error } = await supabaseAny
      .from('games')
      .select('settings')
      .eq('id', gameId)
      .single();
    
    if (error) throw error;
    
    const currentSettings = data?.settings as any || {};
    const currentLevel = (currentSettings.escalationLevel as number) || 1;
    const newLevel = Math.min(currentLevel + 1, 4);
    
    const updatedSettings = {
      ...(currentSettings as object),
      escalationLevel: newLevel
    };
    
    const { error: updateError } = await supabaseAny
      .from('games')
      .update({
        settings: updatedSettings
      })
      .eq('id', gameId);
    
    if (updateError) throw updateError;
    
    return true;
  } catch (error) {
    console.error('Error forcing escalation:', error);
    throw error;
  }
};

// Force a mini-game in the next round
export const forceMiniGame = async (gameId: string) => {
  try {
    const { data, error } = await supabaseAny
      .from('games')
      .select('settings')
      .eq('id', gameId)
      .single();
    
    if (error) throw error;
    
    const currentSettings = data?.settings as any || {};
    
    const updatedSettings = {
      ...(currentSettings as object),
      forceMiniGame: true,
      miniGameType: 'random'
    };
    
    const { error: updateError } = await supabaseAny
      .from('games')
      .update({
        settings: updatedSettings
      })
      .eq('id', gameId);
    
    if (updateError) throw updateError;
    
    return true;
  } catch (error) {
    console.error('Error forcing mini-game:', error);
    throw error;
  }
};
