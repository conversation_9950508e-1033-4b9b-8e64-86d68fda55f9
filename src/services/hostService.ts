import { AdminSettings } from "@/components/AdminControlPanel";

export type GamePhase = "waiting" | "playing" | "judging" | "results";
export type LineType = "roundStart" | "waiting" | "judging" | "winner" | "escalation" | "flavor" | "slowResponse" | "fallback";
export type EscalationLevel = "mild" | "medium" | "spicy" | "wild" | "unhinged";

interface HostLineTemplate {
  text: string;
  type: LineType;
  escalationLevel?: EscalationLevel;
  tags?: string[];
}

// Base templates - these would typically be stored in a database
const hostLineTemplates: HostLineTemplate[] = [
  // Round Start Lines
  { text: "Round {round_number} is starting! Get ready to bring your A-game!", type: "roundStart", escalationLevel: "mild" },
  { text: "Let's kick off round {round_number}! Show me what you've got!", type: "roundStart", escalationLevel: "mild" },
  { text: "Round {round_number} is heating up! Time to get a little wild!", type: "roundStart", escalationLevel: "medium" },
  { text: "Oh honey, round {round_number} is where things get juicy!", type: "roundStart", escalationLevel: "spicy" },
  { text: "Round {round_number}! Let's see who's feeling extra naughty tonight!", type: "roundStart", escalationLevel: "wild" },
  { text: "It's round {round_number} and I'm ready to see your darkest secrets!", type: "roundStart", escalationLevel: "unhinged" },

  // Waiting Lines
  { text: "Waiting for everyone to submit their cards. Take your time... but not too much!", type: "waiting", escalationLevel: "mild" },
  { text: "Still waiting on a few players. What's taking so long? Performance anxiety?", type: "waiting", escalationLevel: "medium" },
  { text: "{player_name} is typing... or having an existential crisis. Either way, we're waiting!", type: "waiting", escalationLevel: "spicy" },
  { text: "Tick tock! Some of you are slow with your fingers. Not a great quality in a player...", type: "waiting", escalationLevel: "wild" },

  // Judging Lines
  { text: "Let's see what we've got here! Time to judge!", type: "judging", escalationLevel: "mild" },
  { text: "Judging time! I've got my critic hat on and I'm not afraid to use it.", type: "judging", escalationLevel: "medium" },
  { text: "These submissions are... interesting. Let's pick a winner!", type: "judging", escalationLevel: "spicy" },
  { text: "Oh my, these cards are getting spicy! Let's see who wins this round!", type: "judging", escalationLevel: "wild" },
  
  // Winner Lines
  { text: "Congratulations, {player_name}! You've won this round!", type: "winner", escalationLevel: "mild" },
  { text: "{player_name} takes the win! Was it skill or just dumb luck?", type: "winner", escalationLevel: "medium" },
  { text: "{player_name} is our winner! I didn't know you had it in you!", type: "winner", escalationLevel: "spicy" },
  { text: "{player_name} wins! Looks like someone's been practicing their naughty wordplay!", type: "winner", escalationLevel: "wild" },
  { text: "Holy $#@%! {player_name} just blew everyone away with that answer!", type: "winner", escalationLevel: "unhinged" },

  // Escalation Lines
  { text: "Things are heating up! Let's take it up a notch!", type: "escalation", escalationLevel: "medium" },
  { text: "Time to spice things up! The gloves are coming off!", type: "escalation", escalationLevel: "spicy" },
  { text: "Oh, we're going THERE now! The filter is officially gone!", type: "escalation", escalationLevel: "wild" },
  { text: "All bets are off! Time to get truly unhinged!", type: "escalation", escalationLevel: "unhinged" },

  // Random Flavor Lines
  { text: "Is it hot in here or is it just me?", type: "flavor", escalationLevel: "medium" },
  { text: "I'm living for this chaos right now!", type: "flavor", escalationLevel: "medium" },
  { text: "Don't make me come over there and judge you personally...", type: "flavor", escalationLevel: "spicy" },
  { text: "Anyone else need a cold shower after that round?", type: "flavor", escalationLevel: "wild" },
  
  // Slow Response Lines
  { text: "We're waiting... Did someone fall asleep?", type: "slowResponse", escalationLevel: "medium" },
  { text: "Taking your sweet time, aren't you? The anticipation is killing me!", type: "slowResponse", escalationLevel: "medium" },
  
  // Fallback Lines
  { text: "Let's keep the party going!", type: "fallback", escalationLevel: "mild" },
  { text: "This game is just getting started!", type: "fallback", escalationLevel: "mild" },
  { text: "Show me what you've got!", type: "fallback", escalationLevel: "medium" },
  { text: "I'm ready for some spicy answers!", type: "fallback", escalationLevel: "spicy" }
];

// Keep track of recently used lines to avoid repetition
let recentlyUsedLines: string[] = [];

/**
 * Generate a suitable host line based on game phase and settings
 */
export const getHostLine = (
  phase: GamePhase, 
  settings: AdminSettings,
  roundNumber: number,
  winner?: {player: string; card: string} | null,
  currentPrompt?: string
): string => {
  // Determine the line type based on the game phase
  let lineType: LineType;
  switch(phase) {
    case "waiting": lineType = "waiting"; break;
    case "playing": lineType = "roundStart"; break;
    case "judging": lineType = "judging"; break;
    case "results": lineType = "winner"; break;
    default: lineType = "fallback";
  }
  
  // Calculate escalation level based on round number and settings
  const escalationLevels: EscalationLevel[] = ["mild", "medium", "spicy", "wild", "unhinged"];
  let escalationIndex = Math.min(
    Math.floor(roundNumber / (settings.escalationSpeed === "fast" ? 1 : settings.escalationSpeed === "normal" ? 2 : 3)),
    escalationLevels.length - 1
  );
  const currentEscalationLevel = escalationLevels[escalationIndex];
  
  // Filter templates by type and escalation level
  let eligibleLines = hostLineTemplates.filter(template => 
    template.type === lineType && 
    (template.escalationLevel === currentEscalationLevel || template.escalationLevel === "mild")
  );
  
  // If no matching lines, fall back to generic lines
  if (eligibleLines.length === 0) {
    eligibleLines = hostLineTemplates.filter(template => template.type === "fallback");
  }
  
  // Filter out recently used lines unless we have no choice
  const freshLines = eligibleLines.filter(line => !recentlyUsedLines.includes(line.text));
  if (freshLines.length > 0) {
    eligibleLines = freshLines;
  }
  
  // Select a random line from eligible options
  const selectedLine = eligibleLines[Math.floor(Math.random() * eligibleLines.length)];
  
  // Add to recently used lines, keeping the list to a reasonable size
  recentlyUsedLines.push(selectedLine.text);
  if (recentlyUsedLines.length > 5) {
    recentlyUsedLines.shift();
  }
  
  // Perform token substitutions
  let processedLine = selectedLine.text;
  
  // Replace tokens
  processedLine = processedLine.replace(/\{round_number\}/g, roundNumber.toString());
  
  if (winner && lineType === "winner") {
    processedLine = processedLine.replace(/\{player_name\}/g, winner.player);
  }
  
  return processedLine;
};

/**
 * Force escalation to the next level
 */
export const forceEscalation = (gameId: string) => {
  console.log("Forcing escalation for game:", gameId);
  // In a real implementation, this would update a game state in the database
  return true;
};

/**
 * Trigger a mini-game for the next round
 */
export const forceMiniGame = (gameId: string) => {
  console.log("Forcing mini-game for game:", gameId);
  // In a real implementation, this would update a game state in the database
  return true;
};

/**
 * Load host personas from storage or database
 */
export const loadHostPersonas = async () => {
  // This would typically load from a database
  // For now, return mock data
  return [
    { id: "1", name: "Flirty Firecracker", settings: getSamplePersona("flirty") },
    { id: "2", name: "Sassy Judge", settings: getSamplePersona("sassy") },
    { id: "3", name: "Party Animal", settings: getSamplePersona("party") }
  ];
};

/**
 * Get a sample persona based on type
 */
export const getSamplePersona = (type: string): AdminSettings => {
  switch(type) {
    case "flirty":
      return {
        aiPersona: "You are the flirty, chaotic host of Tempted 'n Twisted. You love to tease players and make suggestive comments, though you never cross the line into explicit content. You're playful, energetic, and always ready with a witty comeback.",
        responseLength: "medium",
        turnOrder: "sequential",
        escalationSpeed: "normal",
        miniGameFrequency: 3,
        promptDifficultyScaling: true,
        isPrivateGame: true
      };
    case "sassy":
      return {
        aiPersona: "You are the sassy, no-nonsense judge of Tempted 'n Twisted. You don't suffer fools gladly and you're not afraid to call out boring answers. You're quick-witted, slightly intimidating, but fair in your judgments.",
        responseLength: "short",
        turnOrder: "random",
        escalationSpeed: "fast",
        miniGameFrequency: 4,
        promptDifficultyScaling: true,
        isPrivateGame: true
      };
    case "party":
      return {
        aiPersona: "You are the ultimate party animal host of Tempted 'n Twisted. You're loud, enthusiastic, and always trying to get everyone excited. You cheer loudly for good answers and encourage everyone to let loose and have fun.",
        responseLength: "long",
        turnOrder: "host",
        escalationSpeed: "slow",
        miniGameFrequency: 2,
        promptDifficultyScaling: false,
        isPrivateGame: true
      };
    default:
      return {
        aiPersona: "You are the fun, flirty host of Tempted 'n Twisted, a spicy party game. Your tone is playful, mischievous, and a bit suggestive, but never crude or explicit. You guide players through the game with enthusiasm and humor.",
        responseLength: "medium",
        turnOrder: "sequential",
        escalationSpeed: "normal",
        miniGameFrequency: 3,
        promptDifficultyScaling: true,
        isPrivateGame: false
      };
  }
};

/**
 * Save a host persona preset
 */
export const saveHostPersona = async (name: string, settings: AdminSettings) => {
  console.log("Saving host persona:", name, settings);
  // In a real implementation, this would save to a database
  return { id: Date.now().toString(), name, settings };
};

/**
 * Load a specific host persona
 */
export const loadHostPersona = async (id: string) => {
  console.log("Loading host persona:", id);
  // In a real implementation, this would load from a database
  // For now, return a mock persona
  return getSamplePersona("flirty");
};
