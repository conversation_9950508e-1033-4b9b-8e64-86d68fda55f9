
// src/utils/devUtils.ts

import { supabase } from "@/integrations/supabase/client";
import { generateGameCode } from "@/services/gameService";

// Create a development game with test players
export const createDevGame = async () => {
  try {
    const gameCode = `DEV${Math.floor(Math.random() * 900) + 100}`; // 6 chars max
    
    // Create the game
    const { data: game, error: gameError } = await supabase
      .from('games')
      .insert({
        game_code: gameCode,
        max_rounds: 10,
        max_score: 7,
        max_players: 8,
        ai_judging: false,
        submission_timer: 60,
        judging_timer: 45,
        status: 'waiting'
      })
      .select('id, game_code')
      .single();
      
    if (gameError) throw gameError;
    
    // Create the host player
    const { data: hostPlayer, error: hostError } = await supabase
      .from('players')
      .insert({
        game_id: game.id,
        player_name: 'DevHost',
        is_host: true,
        device_token: 'dev-token-host'
      })
      .select('id')
      .single();
      
    if (hostError) throw hostError;
    
    // Update the game with the host ID
    const { error: updateError } = await supabase
      .from('games')
      .update({ host_id: hostPlayer.id })
      .eq('id', game.id);
      
    if (updateError) throw updateError;
    
    // Add some test players
    const testPlayers = [
      { game_id: game.id, player_name: 'DevPlayer1', device_token: 'dev-token-DevPlayer1' },
      { game_id: game.id, player_name: 'DevPlayer2', device_token: 'dev-token-DevPlayer2' },
      { game_id: game.id, player_name: 'DevPlayer3', device_token: 'dev-token-DevPlayer3' }
    ];
    
    const { error: playersError } = await supabase
      .from('players')
      .insert(testPlayers);
      
    if (playersError) throw playersError;
    
    return {
      gameId: game.id,
      gameCode: game.game_code,
      hostId: hostPlayer.id,
      hostName: 'DevHost'
    };
  } catch (error) {
    console.error('Error creating dev game:', error);
    throw error;
  }
};

// Deal cards for a development game
export const dealDevCards = async (gameId: string) => {
  try {
    // Get the players in the game
    const { data: players, error: playersError } = await supabase
      .from('players')
      .select('id')
      .eq('game_id', gameId);
      
    if (playersError) throw playersError;
    if (!players || players.length === 0) throw new Error('No players found in game');
    
    // Get a random black card - Fix: Don't use .asc with RANDOM()
    const { data: blackCard, error: blackCardError } = await supabase
      .from('black_cards')
      .select('id')
      .order('RANDOM()')
      .limit(1)
      .single();
      
    if (blackCardError) throw blackCardError;
    
    // Assign white cards to each player
    for (const player of players) {
      // Get random white cards - Fix: Don't use .asc with RANDOM()
      const { data: whiteCards, error: whiteCardsError } = await supabase
        .from('white_cards')
        .select('id')
        .order('RANDOM()')
        .limit(5);
        
      if (whiteCardsError) throw whiteCardsError;
      if (!whiteCards || whiteCards.length === 0) throw new Error('No white cards found');
      
      // Create player cards
      const playerCards = whiteCards.map(card => ({
        player_id: player.id,
        white_card_id: card.id
      }));
      
      const { error: insertError } = await supabase
        .from('player_cards')
        .insert(playerCards);
        
      if (insertError) throw insertError;
    }
    
    return { blackCardId: blackCard.id };
  } catch (error) {
    console.error('Error dealing dev cards:', error);
    throw error;
  }
};

// Start a development game
export const startDevGame = async (gameId: string) => {
  try {
    const { error } = await supabase
      .from('games')
      .update({
        status: 'active',
        current_round: 1
      })
      .eq('id', gameId);
      
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Error starting dev game:', error);
    throw error;
  }
};

// Create submissions for a development game
export const createDevSubmissions = async (gameId: string, roundNumber: number, blackCardId: string) => {
  try {
    // Get all players except the host
    const { data: players, error: playersError } = await supabase
      .from('players')
      .select('id')
      .eq('game_id', gameId)
      .eq('is_host', false);
      
    if (playersError) throw playersError;
    if (!players || players.length === 0) throw new Error('No non-host players found');
    
    // For each player, get one of their white cards
    for (const player of players) {
      // Get a card from player's hand
      const { data: playerCard, error: cardError } = await supabase
        .from('player_cards')
        .select('white_card_id')
        .eq('player_id', player.id)
        .is('round_used', null)
        .limit(1)
        .single();
        
      if (cardError) throw cardError;
      
      // Create submission
      const { error: submissionError } = await supabase
        .from('round_submissions')
        .insert({
          game_id: gameId,
          round_number: roundNumber,
          player_id: player.id,
          black_card_id: blackCardId,
          white_card_id: playerCard.white_card_id
        });
        
      if (submissionError) throw submissionError;
      
      // Mark card as used
      const { error: updateError } = await supabase
        .from('player_cards')
        .update({ round_used: roundNumber })
        .eq('player_id', player.id)
        .eq('white_card_id', playerCard.white_card_id);
        
      if (updateError) throw updateError;
    }
    
    return true;
  } catch (error) {
    console.error('Error creating dev submissions:', error);
    throw error;
  }
};
