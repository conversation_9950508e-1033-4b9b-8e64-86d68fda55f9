
/**
 * Get stored game session information from sessionStorage
 */
export const getGameSession = () => {
  return {
    gameId: sessionStorage.getItem('gameId'),
    gameCode: sessionStorage.getItem('gameCode'),
    playerId: sessionStorage.getItem('playerId'),
    playerName: sessionStorage.getItem('playerName'),
    deviceToken: sessionStorage.getItem('deviceToken'),
    isHost: sessionStorage.getItem('isHost') === 'true'
  };
};

/**
 * Clear the game session from sessionStorage
 */
export const clearGameSession = () => {
  sessionStorage.removeItem('gameId');
  sessionStorage.removeItem('gameCode');
  sessionStorage.removeItem('playerId');
  sessionStorage.removeItem('playerName');
  sessionStorage.removeItem('deviceToken');
  sessionStorage.removeItem('isHost');
};
