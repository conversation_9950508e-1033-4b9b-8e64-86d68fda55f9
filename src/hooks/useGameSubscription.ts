
import { useEffect, useState } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { getGameSession } from '@/utils/gameHelpers';

type TableName = 'games' | 'players' | 'round_submissions' | 'player_cards' | 'black_cards' | 'white_cards';

export const useGameSubscription = (tableName: TableName, filter: Record<string, any>) => {
  const [data, setData] = useState<any[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        // Use type assertion to bypass corrupted types
        const { data: initialData, error: initialError } = await (supabase as any)
          .from(tableName)
          .select('*')
          .match(filter);

        if (initialError) throw initialError;
        setData(initialData);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error'));
        console.error(`Error fetching initial ${tableName} data:`, err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();

    // Set up the subscription
    const subscription = supabase
      .channel(`${tableName}-changes`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: tableName,
          filter: `game_id=eq.${filter.game_id}`
        },
        (payload) => {
          // Handle different events
          if (payload.eventType === 'INSERT') {
            setData((currentData) => currentData ? [...currentData, payload.new] : [payload.new]);
          } else if (payload.eventType === 'UPDATE') {
            setData((currentData) => 
              currentData?.map(item => item.id === payload.new.id ? payload.new : item) || null);
          } else if (payload.eventType === 'DELETE') {
            setData((currentData) => 
              currentData?.filter(item => item.id !== payload.old.id) || null);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [tableName, JSON.stringify(filter)]);

  return { data, isLoading, error };
};
