
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from "sonner";
import * as gameService from '@/services/gameService';

export interface GameOptions {
  maxRounds: number;
  maxScore: number;
  maxPlayers: number;
  aiJudging: boolean;
  submissionTimer: number;
  judgingTimer: number;
}

export const useGame = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const createGame = async (hostName: string, options?: Partial<GameOptions>) => {
    setIsLoading(true);
    try {
      const defaultOptions = {
        maxRounds: 10,
        maxScore: 7,
        maxPlayers: 8,
        aiJudging: false,
        submissionTimer: 60,
        judgingTimer: 45
      };
      
      const mergedOptions = { ...defaultOptions, ...options };
      
      const result = await gameService.createGame(hostName, mergedOptions);
      
      // Store game data in session storage
      sessionStorage.setItem('gameId', result.gameId);
      sessionStorage.setItem('gameCode', result.gameCode);
      sessionStorage.setItem('playerId', result.playerId);
      sessionStorage.setItem('playerName', hostName);
      sessionStorage.setItem('deviceToken', result.deviceToken);
      sessionStorage.setItem('isHost', 'true');
      
      toast.success(`Game created! Your game code is ${result.gameCode}`);
      navigate('/host');
      return result;
    } catch (error) {
      toast.error(`Failed to create game: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const joinGame = async (gameCode: string, playerName: string) => {
    setIsLoading(true);
    try {
      const result = await gameService.joinGame(gameCode, playerName);
      
      // Store game data in session storage
      sessionStorage.setItem('gameId', result.gameId);
      sessionStorage.setItem('gameCode', result.gameCode);
      sessionStorage.setItem('playerId', result.playerId);
      sessionStorage.setItem('playerName', playerName);
      sessionStorage.setItem('deviceToken', result.deviceToken);
      sessionStorage.setItem('isHost', 'false');
      
      toast.success(`Joined game ${result.gameCode} as ${playerName}`);
      navigate('/player');
      return result;
    } catch (error) {
      toast.error(`Failed to join game: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  
  return {
    createGame,
    joinGame,
    isLoading
  };
};
