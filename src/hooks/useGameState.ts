
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import {
  GameState,
  GamePhase,
  initializeGameState,
  loadCardPacks,
  drawWhiteCards,
  drawBlackCard,
  submitCard,
  haveAllPlayersSubmitted,
  pickWinner,
  pickA<PERSON><PERSON>inner,
  startNextRound
} from '@/services/gameStateService';
import { getGameSession } from '@/utils/gameHelpers';
import * as gameService from '@/services/gameService';

// Number of cards each player should have in their hand
const CARDS_PER_HAND = 5;

// Initialize game from session storage or fetch from database
const getInitialGameState = async (): Promise<GameState | null> => {
  const session = getGameSession();

  if (!session.gameId || !session.gameCode || !session.playerName) {
    return null;
  }

  // Check for existing game state in session first
  const savedState = sessionStorage.getItem('gameState');
  if (savedState) {
    try {
      const parsedState = JSON.parse(savedState) as GameState;
      // Validate that the saved state matches current session
      if (parsedState.gameId === session.gameId && parsedState.gameCode === session.gameCode) {
        return parsedState;
      }
    } catch (e) {
      console.error('Failed to parse saved game state', e);
    }
  }

  // Try to fetch game state from database
  try {
    const dbGameState = await gameService.getGameState(session.gameId);
    if (dbGameState) {
      // Convert database game state to local game state format
      const gameState = initializeGameState(
        session.gameId,
        session.gameCode,
        session.playerId || 'unknown',
        session.playerName,
        false
      );

      // Update with database values
      gameState.phase = dbGameState.status === 'waiting' ? 'waiting' :
                       dbGameState.status === 'active' ? 'playing' : 'waiting';
      gameState.roundNumber = dbGameState.current_round || 1;
      gameState.players = dbGameState.players?.map(p => ({
        id: p.id,
        name: p.player_name,
        score: p.score || 0,
        hand: [],
        isHost: p.is_host || false,
        hasSubmitted: false
      })) || [gameState.players[0]]; // Keep at least the host

      return gameState;
    }
  } catch (error) {
    console.error('Failed to fetch game state from database:', error);
  }

  // Fallback: create new game state
  return initializeGameState(
    session.gameId,
    session.gameCode,
    session.playerId || 'unknown',
    session.playerName,
    false
  );
};

export const useGameState = () => {
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize game state on mount
  useEffect(() => {
    const initializeState = async () => {
      setIsLoading(true);
      try {
        const initialState = await getInitialGameState();
        setGameState(initialState);
      } catch (error) {
        console.error('Failed to initialize game state:', error);
        toast.error('Failed to load game state');
      } finally {
        setIsLoading(false);
      }
    };

    initializeState();
  }, []);

  // Save game state to session storage whenever it changes
  useEffect(() => {
    if (gameState) {
      sessionStorage.setItem('gameState', JSON.stringify(gameState));
    }
  }, [gameState]);
  
  // Select card packs and initialize decks
  const selectCardPacks = useCallback(async (packIds: string[]) => {
    if (!gameState) return;
    
    setIsLoading(true);
    try {
      const updatedState = await loadCardPacks(gameState, packIds);
      setGameState(updatedState);
      toast.success(`${packIds.length} card ${packIds.length === 1 ? 'pack' : 'packs'} loaded successfully`);
    } catch (error) {
      console.error('Failed to load card packs:', error);
      toast.error('Failed to load card packs');
    } finally {
      setIsLoading(false);
    }
  }, [gameState]);
  
  // Start the game
  const startGame = useCallback(async () => {
    if (!gameState) return;

    // In dev mode, allow starting with just the host for testing
    const isDevMode = process.env.NODE_ENV === 'development' || localStorage.getItem('devMode') === 'true';
    const minPlayers = isDevMode ? 1 : 2;

    if (gameState.players.length < minPlayers) {
      toast.error(`Need at least ${minPlayers} player${minPlayers > 1 ? 's' : ''} to start`);
      return;
    }
    if (gameState.blackCardDeck.length === 0 || gameState.whiteCardDeck.length === 0) {
      toast.error('Please select at least one card pack before starting');
      return;
    }

    setIsLoading(true);

    try {
      // Update database first
      await gameService.startGame(gameState.gameId);

      // Set game phase to playing
      let updatedState = {
        ...gameState,
        phase: 'playing' as GamePhase
      };

      // Draw a black card from database filtered by selected packs
      if (!gameState.selectedPacks || gameState.selectedPacks.length === 0) {
        setGameState(updatedState);
        toast.error('No card packs selected! Please select card packs first.');
        return;
      }

      // Query database for black cards from selected packs
      const { data: blackCard, error: blackCardError } = await supabase
        .from('black_cards')
        .select('id, card_text, pack_id, pack_name')
        .in('pack_id', gameState.selectedPacks)
        .limit(1)
        .single();

      if (blackCardError || !blackCard) {
        setGameState(updatedState);
        toast.error('No cards available from selected packs!');
        return;
      }

      // Set the current prompt from the database card
      updatedState.currentPrompt = blackCard.card_text;

      // Store the black card in round_submissions so players can see it
      const { error: insertError } = await supabase
        .from('round_submissions')
        .insert({
          game_id: gameState.gameId,
          round_number: updatedState.roundNumber,
          black_card_id: blackCard.id,
          player_id: null,
          white_card_id: null
        });

      if (insertError) {
        console.error('Failed to insert round submission:', insertError);
        toast.error('Failed to store round data');
        return;
      }

      // Deal cards to all players
      for (const player of updatedState.players) {
        const { gameState: stateWithCards } = drawWhiteCards(updatedState, player.id, CARDS_PER_HAND);
        updatedState = stateWithCards;
      }

      setGameState(updatedState);
      toast.success('Game started!');
    } catch (error) {
      console.error('Failed to start game:', error);
      toast.error('Failed to start game');
    } finally {
      setIsLoading(false);
    }
  }, [gameState]);
  
  // Submit a card on behalf of a player
  const playCard = useCallback((playerId: string, card: string) => {
    if (!gameState) return;
    if (gameState.phase !== 'playing') {
      toast.error('Cards can only be submitted during the playing phase');
      return;
    }
    
    try {
      const updatedState = submitCard(gameState, playerId, card);
      
      // Check if all players have submitted
      if (haveAllPlayersSubmitted(updatedState)) {
        updatedState.phase = 'judging';
      }
      
      setGameState(updatedState);
      toast.success('Card submitted successfully');
    } catch (error) {
      console.error('Failed to submit card:', error);
      toast.error('Failed to submit card');
    }
  }, [gameState]);
  
  // Add a player to the game
  const addPlayer = useCallback(async (playerId: string, playerName: string) => {
    if (!gameState) return;
    if (gameState.phase !== 'waiting') {
      toast.error('Players can only join during the waiting phase');
      return;
    }

    // Check if player is already in the game
    if (gameState.players.some(p => p.id === playerId)) {
      return;
    }

    try {
      // This would typically be handled by the joinGame service
      // For now, just update local state
      const updatedState = {
        ...gameState,
        players: [
          ...gameState.players,
          {
            id: playerId,
            name: playerName,
            score: 0,
            hand: [],
            isHost: false
          }
        ]
      };

      setGameState(updatedState);
      toast.success(`${playerName} joined the game`);
    } catch (error) {
      console.error('Failed to add player:', error);
      toast.error('Failed to add player');
    }
  }, [gameState]);
  
  // Select a winner manually
  const selectWinner = useCallback((submission: {player: string; card: string}) => {
    if (!gameState) return;
    if (gameState.phase !== 'judging') {
      toast.error('Winners can only be selected during the judging phase');
      return;
    }
    
    const updatedState = pickWinner(gameState, submission);
    updatedState.phase = 'results';
    
    setGameState(updatedState);
  }, [gameState]);
  
  // Let AI select a winner
  const letAISelectWinner = useCallback(() => {
    if (!gameState) return;
    if (gameState.phase !== 'judging') {
      toast.error('Winners can only be selected during the judging phase');
      return;
    }
    
    const { gameState: updatedState } = pickAIWinner(gameState);
    updatedState.phase = 'results';
    
    setGameState(updatedState);
  }, [gameState]);
  
  // Start the next round
  const nextRound = useCallback(() => {
    if (!gameState) return;
    if (gameState.phase !== 'results') {
      toast.error('Next round can only be started after results are shown');
      return;
    }
    
    const updatedState = startNextRound(gameState);
    
    // Deal new cards to all players who need them
    for (const player of updatedState.players) {
      if (player.hand.length < CARDS_PER_HAND) {
        const cardsToDraw = CARDS_PER_HAND - player.hand.length;
        const { gameState: stateWithCards } = drawWhiteCards(updatedState, player.id, cardsToDraw);
        Object.assign(updatedState, stateWithCards);
      }
    }
    
    setGameState(updatedState);
  }, [gameState]);
  
  // Toggle AI judging
  const toggleAIJudging = useCallback(() => {
    if (!gameState) return;
    
    const updatedState = {
      ...gameState,
      aiJudging: !gameState.aiJudging
    };
    
    setGameState(updatedState);
    toast.success(updatedState.aiJudging 
      ? 'AI judging enabled' 
      : 'AI judging disabled');
  }, [gameState]);
  
  // Toggle private game mode
  const togglePrivateGame = useCallback(() => {
    if (!gameState) return;

    const updatedState = {
      ...gameState,
      isPrivateGame: !gameState.isPrivateGame
    };

    setGameState(updatedState);
    toast.success(updatedState.isPrivateGame
      ? 'Switched to Private Game mode with AI host'
      : 'Switched to Public Game mode');
  }, [gameState]);

  // Refresh game state from database
  const refreshGameState = useCallback(async () => {
    if (!gameState) return;

    try {
      const dbGameState = await gameService.getGameState(gameState.gameId);
      if (dbGameState) {
        // Update player list from database
        const updatedPlayers = dbGameState.players?.map(p => ({
          id: p.id,
          name: p.player_name,
          score: p.score || 0,
          hand: gameState.players.find(existing => existing.id === p.id)?.hand || [],
          isHost: p.is_host || false,
          hasSubmitted: gameState.players.find(existing => existing.id === p.id)?.hasSubmitted || false
        })) || gameState.players;

        const updatedState = {
          ...gameState,
          players: updatedPlayers,
          phase: dbGameState.status === 'waiting' ? 'waiting' as GamePhase :
                 dbGameState.status === 'active' ? 'playing' as GamePhase : gameState.phase,
          roundNumber: dbGameState.current_round || gameState.roundNumber
        };

        setGameState(updatedState);
      }
    } catch (error) {
      console.error('Failed to refresh game state:', error);
    }
  }, [gameState]);

  return {
    gameState,
    isLoading,
    selectCardPacks,
    startGame,
    playCard,
    addPlayer,
    selectWinner,
    letAISelectWinner,
    nextRound,
    toggleAIJudging,
    togglePrivateGame,
    refreshGameState
  };
};
