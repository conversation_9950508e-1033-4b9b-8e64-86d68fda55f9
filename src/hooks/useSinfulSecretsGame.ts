import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface SinfulSecretsPlayer {
  id: string;
  player_name: string;
  gender: 'M' | 'F' | 'Other';
  is_host: boolean;
  has_played: boolean;
  penalty_drinks: number;
  safe_word_usage: number;
  score: number;
  is_active: boolean;
}

export interface SinfulSecretsGame {
  id: string;
  game_code: string;
  host_name: string;
  status: 'waiting' | 'active' | 'completed';
  current_round: number;
  current_player_index: number;
  intensity_level: 'Playful' | 'Sensual' | 'Daring' | 'Bold';
  safe_word: string;
  game_phase: 'card-submission' | 'card-judging' | 'sinful-secrets';
  is_card_phase: boolean;
  ai_persona: string;
}

export interface CardSubmission {
  id: string;
  player_id: string;
  player_name: string;
  card_text: string;
  is_winner: boolean;
}

export interface Challenge {
  id: string;
  player_id: string;
  challenge_type: 'Truth' | 'Dare' | 'WouldYouRather' | 'Wildcard';
  selected_number: number;
  challenge_text?: string;
  player_choice?: 'Truth' | 'Dare';
  is_completed: boolean;
  used_safe_word: boolean;
}

export interface ChatMessage {
  id: string;
  sender: string;
  message_text: string;
  message_type: 'chat' | 'system' | 'ai_response';
  created_at: string;
}

export const useSinfulSecretsGame = (gameCode?: string) => {
  const [game, setGame] = useState<SinfulSecretsGame | null>(null);
  const [players, setPlayers] = useState<SinfulSecretsPlayer[]>([]);
  const [cardSubmissions, setCardSubmissions] = useState<CardSubmission[]>([]);
  const [currentChallenge, setCurrentChallenge] = useState<Challenge | null>(null);
  const [currentPrompt, setCurrentPrompt] = useState<string>('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create a new game
  const createGame = useCallback(async (hostName: string): Promise<string> => {
    setIsLoading(true);
    try {
      // Generate unique game code
      const gameCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      
      const { data: gameData, error: gameError } = await supabase
        .from('sinful_secrets_games')
        .insert({
          game_code: gameCode,
          host_name: hostName,
          safe_word: generateSafeWord()
        })
        .select()
        .single();

      if (gameError) throw gameError;

      // Host is not a player - no player record needed
      setGame(gameData);
      toast.success(`Game created! Code: ${gameCode}`);
      return gameCode;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to create game';
      setError(errorMsg);
      toast.error(errorMsg);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Join an existing game
  const joinGame = useCallback(async (gameCode: string, playerName: string, gender: 'M' | 'F' | 'Other' = 'Other'): Promise<string> => {
    setIsLoading(true);
    try {
      // Find the game
      const { data: gameData, error: gameError } = await supabase
        .from('sinful_secrets_games')
        .select('*')
        .eq('game_code', gameCode)
        .eq('status', 'waiting')
        .single();

      if (gameError || !gameData) {
        throw new Error('Game not found or already started');
      }

      // Add player to game
      const { data: playerData, error: playerError } = await supabase
        .from('sinful_secrets_players')
        .insert({
          game_id: gameData.id,
          player_name: playerName,
          gender: gender
        })
        .select()
        .single();

      if (playerError) throw playerError;

      toast.success(`Joined game ${gameCode}!`);
      return playerData.id;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to join game';
      setError(errorMsg);
      toast.error(errorMsg);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Submit a card during card phase
  const submitCard = useCallback(async (playerId: string, cardText: string) => {
    if (!game) return;

    try {
      const { error } = await supabase
        .from('sinful_secrets_card_submissions')
        .insert({
          game_id: game.id,
          player_id: playerId,
          round_number: game.current_round,
          card_text: cardText
        });

      if (error) throw error;
      toast.success('Card submitted!');
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to submit card';
      toast.error(errorMsg);
    }
  }, [game]);

  // Generate a prompt for card submission phase
  const generatePrompt = useCallback((): string => {
    const prompts = [
      "What's the most embarrassing thing that happened to you on a date?",
      "Complete this sentence: 'I've never told anyone, but I...'",
      "What's your biggest turn-on that would surprise people?",
      "Describe your most awkward intimate moment.",
      "What's the wildest place you've ever been intimate?",
      "What's your secret fantasy you've never shared?",
      "What's the most scandalous thing in your browser history?",
      "Complete: 'I get instantly attracted when someone...'",
      "What's your guilty pleasure that you're embarrassed about?",
      "What's the boldest thing you've ever done to get someone's attention?",
      "What's your most embarrassing bedroom confession?",
      "Complete: 'My friends would be shocked if they knew I...'",
      "What's the naughtiest text you've ever sent?",
      "Describe your most memorable kiss.",
      "What's your biggest relationship red flag that you ignore?"
    ];

    return prompts[Math.floor(Math.random() * prompts.length)];
  }, []);

  // Select challenge number
  const selectChallengeNumber = useCallback(async (playerId: string, number: number) => {
    if (!game) return;

    try {
      const challengeType = getChallengeType(number);
      
      const { data, error } = await supabase
        .from('sinful_secrets_challenges')
        .insert({
          game_id: game.id,
          player_id: playerId,
          round_number: game.current_round,
          challenge_type: challengeType,
          selected_number: number
        })
        .select()
        .single();

      if (error) throw error;
      setCurrentChallenge(data);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to select challenge';
      toast.error(errorMsg);
    }
  }, [game]);

  // Load game data
  useEffect(() => {
    if (!gameCode) return;

    const loadGameData = async () => {
      setIsLoading(true);
      try {
        // Load game
        const { data: gameData, error: gameError } = await supabase
          .from('sinful_secrets_games')
          .select('*')
          .eq('game_code', gameCode)
          .single();

        if (gameError) throw gameError;
        setGame(gameData);

        // Generate prompt if game is active and no prompt exists
        if (gameData.status === 'active' && gameData.game_phase === 'card-submission' && !currentPrompt) {
          const newPrompt = generatePrompt();
          setCurrentPrompt(newPrompt);
        }

        // Load players
        const { data: playersData, error: playersError } = await supabase
          .from('sinful_secrets_players')
          .select('*')
          .eq('game_id', gameData.id)
          .order('joined_at');

        if (playersError) throw playersError;
        setPlayers(playersData);

        // Load current round submissions
        const { data: submissionsData, error: submissionsError } = await supabase
          .from('sinful_secrets_card_submissions')
          .select(`
            *,
            sinful_secrets_players(player_name)
          `)
          .eq('game_id', gameData.id)
          .eq('round_number', gameData.current_round);

        if (submissionsError) throw submissionsError;
        console.log('Raw submissions data:', submissionsData);
        const processedSubmissions = submissionsData.map(sub => ({
          ...sub,
          player_name: sub.is_deck_card ? 'Random Card' : (sub.sinful_secrets_players?.player_name || 'Unknown Player')
        }));
        console.log('Processed submissions:', processedSubmissions);
        setCardSubmissions(processedSubmissions);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load game');
      } finally {
        setIsLoading(false);
      }
    };

    loadGameData();
  }, [gameCode]);

  // Set up real-time subscriptions after game is loaded
  useEffect(() => {
    if (!game || !gameCode) return;

    console.log('Setting up subscriptions for game:', game.id, 'gameCode:', gameCode);

    const loadGameData = async () => {
      try {
        // Load players
        const { data: playersData, error: playersError } = await supabase
          .from('sinful_secrets_players')
          .select('*')
          .eq('game_id', game.id)
          .order('joined_at');

        if (playersError) throw playersError;
        setPlayers(playersData);

        // Load current round submissions
        const { data: submissionsData, error: submissionsError } = await supabase
          .from('sinful_secrets_card_submissions')
          .select(`
            *,
            sinful_secrets_players(player_name)
          `)
          .eq('game_id', game.id)
          .eq('round_number', game.current_round);

        if (submissionsError) throw submissionsError;
        const processedSubmissions = submissionsData.map(sub => ({
          ...sub,
          player_name: sub.is_deck_card ? 'Random Card' : (sub.sinful_secrets_players?.player_name || 'Unknown Player')
        }));
        setCardSubmissions(processedSubmissions);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to reload game data');
      }
    };

    // Set up real-time subscriptions
    const gameSubscription = supabase
      .channel(`sinful-secrets-game-${gameCode}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sinful_secrets_games',
        filter: `game_code=eq.${gameCode}`
      }, (payload) => {
        console.log('Game update received:', payload);
        if (payload.eventType === 'UPDATE') {
          setGame(payload.new as SinfulSecretsGame);
        }
      })
      .subscribe((status) => {
        console.log('Game subscription status:', status);
        if (status === 'SUBSCRIBED') {
          console.log('Game subscription active');
        }
      });

    const playersSubscription = supabase
      .channel(`sinful-secrets-players-${gameCode}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sinful_secrets_players',
        filter: `game_id=eq.${game.id}`
      }, (payload) => {
        console.log('Players update received:', payload);
        // Handle player changes in real-time
        if (payload.eventType === 'INSERT') {
          setPlayers(prev => [...prev, payload.new as SinfulSecretsPlayer]);
        } else if (payload.eventType === 'UPDATE') {
          setPlayers(prev => prev.map(p => p.id === payload.new.id ? payload.new as SinfulSecretsPlayer : p));
        } else if (payload.eventType === 'DELETE') {
          setPlayers(prev => prev.filter(p => p.id !== payload.old.id));
        }
      })
      .subscribe((status) => {
        console.log('Players subscription status:', status);
        if (status === 'SUBSCRIBED') {
          console.log('Players subscription active for game_id:', game.id);
        }
      });

    const submissionsSubscription = supabase
      .channel(`sinful-secrets-submissions-${gameCode}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sinful_secrets_card_submissions',
        filter: `game_id=eq.${game.id}`
      }, async (payload) => {
        console.log('Submissions update received:', payload);
        // Reload submissions data when changes occur
        try {
          const { data: submissionsData, error: submissionsError } = await supabase
            .from('sinful_secrets_card_submissions')
            .select(`
              *,
              sinful_secrets_players(player_name)
            `)
            .eq('game_id', game.id)
            .eq('round_number', game.current_round);

          if (submissionsError) throw submissionsError;
          const processedSubmissions = submissionsData.map(sub => ({
            ...sub,
            player_name: sub.is_deck_card ? 'Random Card' : (sub.sinful_secrets_players?.player_name || 'Unknown Player')
          }));
          setCardSubmissions(processedSubmissions);

          // Check if all players have submitted and auto-transition to judging
          const playerSubmissions = processedSubmissions.filter(sub => !sub.is_deck_card);
          console.log(`Phase transition check: ${playerSubmissions.length} player submissions out of ${players.length} players`);
          console.log('Current game phase:', game.game_phase);
          console.log('All submissions:', processedSubmissions);

          if (game.game_phase === 'card-submission' && playerSubmissions.length === players.length) {
            console.log('All players have submitted cards, adding random card and transitioning to judging phase');

            // Add a random white card from the deck
            await addRandomWhiteCard(game.id, game.current_round);

            // Select a random player to be the judge (not the host)
            const eligibleJudges = players.filter(p => !p.is_host);
            if (eligibleJudges.length === 0) {
              console.error('No eligible judges found!');
              return;
            }

            const randomJudge = eligibleJudges[Math.floor(Math.random() * eligibleJudges.length)];
            console.log('Selected judge:', randomJudge);

            const { error: phaseError } = await supabase
              .from('sinful_secrets_games')
              .update({
                game_phase: 'card-judging',
                current_judge_id: randomJudge.id
              })
              .eq('id', game.id);

            if (phaseError) {
              console.error('Failed to transition to judging phase:', phaseError);
            }
          }
        } catch (err) {
          console.error('Failed to reload submissions:', err);
        }
      })
      .subscribe((status) => {
        console.log('Submissions subscription status:', status);
      });

    // Also set up polling as a fallback for real-time updates
    let pollingInterval: NodeJS.Timeout | null = null;
    pollingInterval = setInterval(async () => {
      console.log('Polling for game updates...');
      try {
        // Poll for game state changes
        const { data: gameData, error: gameError } = await supabase
          .from('sinful_secrets_games')
          .select('*')
          .eq('game_code', gameCode)
          .single();

        if (gameError) throw gameError;
        if (gameData && JSON.stringify(gameData) !== JSON.stringify(game)) {
          console.log('Game state changed via polling:', gameData);
          setGame(gameData);
        }

        // Poll for player updates
        const { data: playersData, error: playersError } = await supabase
          .from('sinful_secrets_players')
          .select('*')
          .eq('game_id', game.id)
          .order('joined_at');

        if (playersError) throw playersError;
        if (JSON.stringify(playersData) !== JSON.stringify(players)) {
          console.log('Players changed via polling:', playersData);
          setPlayers(playersData);
        }
      } catch (err) {
        console.error('Polling error:', err);
      }
    }, 3000); // Poll every 3 seconds as fallback

    return () => {
      supabase.removeChannel(gameSubscription);
      supabase.removeChannel(playersSubscription);
      supabase.removeChannel(submissionsSubscription);
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [game, gameCode]);

  return {
    game,
    players,
    cardSubmissions,
    currentChallenge,
    currentPrompt,
    chatMessages,
    isLoading,
    error,
    createGame,
    joinGame,
    submitCard,
    selectChallengeNumber
  };
};

// Helper functions
const generateSafeWord = (): string => {
  const words = ['Pineapple', 'Butterfly', 'Rainbow', 'Unicorn', 'Sparkle', 'Moonbeam'];
  return words[Math.floor(Math.random() * words.length)];
};

const addRandomWhiteCard = async (gameId: string, roundNumber: number) => {
  try {
    // Get a random white card from the database
    const { data: whiteCard, error: cardError } = await supabase
      .from('white_cards')
      .select('card_text')
      .limit(1)
      .single();

    if (cardError) {
      console.error('Failed to get random white card:', cardError);
      // Fallback to a hardcoded card if database fails
      const fallbackCards = [
        "A really awkward silence",
        "My secret shame",
        "Something that would make my mother cry",
        "The forbidden fruit",
        "A moment of pure chaos"
      ];
      const randomCard = fallbackCards[Math.floor(Math.random() * fallbackCards.length)];

      // Add it as a submission without player_id (deck card)
      const { error: insertError } = await supabase
        .from('sinful_secrets_card_submissions')
        .insert({
          game_id: gameId,
          player_id: null, // No player ID for deck cards
          round_number: roundNumber,
          card_text: randomCard,
          is_deck_card: true
        });

      if (insertError) throw insertError;
      console.log('Added fallback random white card:', randomCard);
      return;
    }

    // Add it as a submission without player_id (deck card)
    const { error: insertError } = await supabase
      .from('sinful_secrets_card_submissions')
      .insert({
        game_id: gameId,
        player_id: null, // No player ID for deck cards
        round_number: roundNumber,
        card_text: whiteCard.card_text,
        is_deck_card: true
      });

    if (insertError) throw insertError;
    console.log('Added random white card:', whiteCard.card_text);
  } catch (error) {
    console.error('Failed to add random white card:', error);
  }
};

const getChallengeType = (number: number): 'Truth' | 'Dare' | 'WouldYouRather' | 'Wildcard' => {
  if (number <= 8) return Math.random() < 0.5 ? 'Truth' : 'Dare';
  if (number <= 12) return Math.random() < 0.5 ? 'Truth' : 'Dare';
  if (number <= 16) return 'WouldYouRather';
  return 'Wildcard';
};
