import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface SinfulSecretsPlayer {
  id: string;
  player_name: string;
  gender: 'M' | 'F' | 'Other';
  is_host: boolean;
  has_played: boolean;
  penalty_drinks: number;
  safe_word_usage: number;
  score: number;
  is_active: boolean;
}

export interface SinfulSecretsGame {
  id: string;
  game_code: string;
  host_name: string;
  status: 'waiting' | 'active' | 'completed';
  current_round: number;
  current_player_index: number;
  intensity_level: 'Playful' | 'Sensual' | 'Daring' | 'Bold';
  safe_word: string;
  game_phase: 'card-submission' | 'card-judging' | 'sinful-secrets';
  is_card_phase: boolean;
  ai_persona: string;
}

export interface CardSubmission {
  id: string;
  player_id: string;
  player_name: string;
  card_text: string;
  is_winner: boolean;
}

export interface Challenge {
  id: string;
  player_id: string;
  challenge_type: 'Truth' | 'Dare' | 'WouldYouRather' | 'Wildcard';
  selected_number: number;
  challenge_text?: string;
  player_choice?: 'Truth' | 'Dare';
  is_completed: boolean;
  used_safe_word: boolean;
}

export interface ChatMessage {
  id: string;
  sender: string;
  message_text: string;
  message_type: 'chat' | 'system' | 'ai_response';
  created_at: string;
}

export const useSinfulSecretsGame = (gameCode?: string) => {
  const [game, setGame] = useState<SinfulSecretsGame | null>(null);
  const [players, setPlayers] = useState<SinfulSecretsPlayer[]>([]);
  const [cardSubmissions, setCardSubmissions] = useState<CardSubmission[]>([]);
  const [currentChallenge, setCurrentChallenge] = useState<Challenge | null>(null);
  const [currentPrompt, setCurrentPrompt] = useState<string>('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create a new game
  const createGame = useCallback(async (hostName: string): Promise<string> => {
    setIsLoading(true);
    try {
      // Generate unique game code
      const gameCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      
      const { data: gameData, error: gameError } = await supabase
        .from('sinful_secrets_games')
        .insert({
          game_code: gameCode,
          host_name: hostName,
          safe_word: generateSafeWord()
        })
        .select()
        .single();

      if (gameError) throw gameError;

      // Host is not a player - no player record needed
      setGame(gameData);
      toast.success(`Game created! Code: ${gameCode}`);
      return gameCode;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to create game';
      setError(errorMsg);
      toast.error(errorMsg);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Join an existing game
  const joinGame = useCallback(async (gameCode: string, playerName: string, gender: 'M' | 'F' | 'Other' = 'Other'): Promise<string> => {
    setIsLoading(true);
    try {
      // Find the game
      const { data: gameData, error: gameError } = await supabase
        .from('sinful_secrets_games')
        .select('*')
        .eq('game_code', gameCode)
        .eq('status', 'waiting')
        .single();

      if (gameError || !gameData) {
        throw new Error('Game not found or already started');
      }

      // Add player to game
      const { data: playerData, error: playerError } = await supabase
        .from('sinful_secrets_players')
        .insert({
          game_id: gameData.id,
          player_name: playerName,
          gender: gender
        })
        .select()
        .single();

      if (playerError) throw playerError;

      toast.success(`Joined game ${gameCode}!`);
      return playerData.id;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to join game';
      setError(errorMsg);
      toast.error(errorMsg);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Submit a card during card phase
  const submitCard = useCallback(async (playerId: string, cardText: string) => {
    if (!game) return;

    try {
      const { error } = await supabase
        .from('sinful_secrets_card_submissions')
        .insert({
          game_id: game.id,
          player_id: playerId,
          round_number: game.current_round,
          card_text: cardText
        });

      if (error) throw error;
      toast.success('Card submitted!');
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to submit card';
      toast.error(errorMsg);
    }
  }, [game]);

  // Generate a prompt for card submission phase
  const generatePrompt = useCallback((): string => {
    const prompts = [
      "What's the most embarrassing thing that happened to you on a date?",
      "Complete this sentence: 'I've never told anyone, but I...'",
      "What's your biggest turn-on that would surprise people?",
      "Describe your most awkward intimate moment.",
      "What's the wildest place you've ever been intimate?",
      "What's your secret fantasy you've never shared?",
      "What's the most scandalous thing in your browser history?",
      "Complete: 'I get instantly attracted when someone...'",
      "What's your guilty pleasure that you're embarrassed about?",
      "What's the boldest thing you've ever done to get someone's attention?",
      "What's your most embarrassing bedroom confession?",
      "Complete: 'My friends would be shocked if they knew I...'",
      "What's the naughtiest text you've ever sent?",
      "Describe your most memorable kiss.",
      "What's your biggest relationship red flag that you ignore?"
    ];

    return prompts[Math.floor(Math.random() * prompts.length)];
  }, []);

  // Select challenge number
  const selectChallengeNumber = useCallback(async (playerId: string, number: number) => {
    if (!game) return;

    try {
      const challengeType = getChallengeType(number);
      
      const { data, error } = await supabase
        .from('sinful_secrets_challenges')
        .insert({
          game_id: game.id,
          player_id: playerId,
          round_number: game.current_round,
          challenge_type: challengeType,
          selected_number: number
        })
        .select()
        .single();

      if (error) throw error;
      setCurrentChallenge(data);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to select challenge';
      toast.error(errorMsg);
    }
  }, [game]);

  // Load game data
  useEffect(() => {
    if (!gameCode) return;

    const loadGameData = async () => {
      setIsLoading(true);
      try {
        // Load game
        const { data: gameData, error: gameError } = await supabase
          .from('sinful_secrets_games')
          .select('*')
          .eq('game_code', gameCode)
          .single();

        if (gameError) throw gameError;
        setGame(gameData);

        // Generate prompt if game is active and no prompt exists
        if (gameData.status === 'active' && gameData.game_phase === 'card-submission' && !currentPrompt) {
          const newPrompt = generatePrompt();
          setCurrentPrompt(newPrompt);
        }

        // Load players
        const { data: playersData, error: playersError } = await supabase
          .from('sinful_secrets_players')
          .select('*')
          .eq('game_id', gameData.id)
          .order('joined_at');

        if (playersError) throw playersError;
        setPlayers(playersData);

        // Load current round submissions
        const { data: submissionsData, error: submissionsError } = await supabase
          .from('sinful_secrets_card_submissions')
          .select(`
            *,
            sinful_secrets_players!inner(player_name)
          `)
          .eq('game_id', gameData.id)
          .eq('round_number', gameData.current_round);

        if (submissionsError) throw submissionsError;
        console.log('Raw submissions data:', submissionsData);
        const processedSubmissions = submissionsData.map(sub => ({
          ...sub,
          player_name: sub.sinful_secrets_players.player_name
        }));
        console.log('Processed submissions:', processedSubmissions);
        setCardSubmissions(processedSubmissions);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load game');
      } finally {
        setIsLoading(false);
      }
    };

    loadGameData();
  }, [gameCode]);

  // Set up real-time subscriptions after game is loaded
  useEffect(() => {
    if (!game || !gameCode) return;

    const loadGameData = async () => {
      try {
        // Load players
        const { data: playersData, error: playersError } = await supabase
          .from('sinful_secrets_players')
          .select('*')
          .eq('game_id', game.id)
          .order('joined_at');

        if (playersError) throw playersError;
        setPlayers(playersData);

        // Load current round submissions
        const { data: submissionsData, error: submissionsError } = await supabase
          .from('sinful_secrets_card_submissions')
          .select(`
            *,
            sinful_secrets_players!inner(player_name)
          `)
          .eq('game_id', game.id)
          .eq('round_number', game.current_round);

        if (submissionsError) throw submissionsError;
        const processedSubmissions = submissionsData.map(sub => ({
          ...sub,
          player_name: sub.sinful_secrets_players.player_name
        }));
        setCardSubmissions(processedSubmissions);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to reload game data');
      }
    };

    // Set up real-time subscriptions
    const gameSubscription = supabase
      .channel(`sinful-secrets-game-${gameCode}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sinful_secrets_games',
        filter: `game_code=eq.${gameCode}`
      }, (payload) => {
        console.log('Game update received:', payload);
        if (payload.eventType === 'UPDATE') {
          setGame(payload.new as SinfulSecretsGame);
        }
      })
      .subscribe((status) => {
        console.log('Game subscription status:', status);
      });

    const playersSubscription = supabase
      .channel(`sinful-secrets-players-${gameCode}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sinful_secrets_players',
        filter: `game_id=eq.${game.id}`
      }, (payload) => {
        console.log('Players update received:', payload);
        // Reload players when changes occur
        loadGameData();
      })
      .subscribe((status) => {
        console.log('Players subscription status:', status);
      });

    const submissionsSubscription = supabase
      .channel(`sinful-secrets-submissions-${gameCode}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sinful_secrets_card_submissions',
        filter: `game_id=eq.${game.id}`
      }, (payload) => {
        console.log('Submissions update received:', payload);
        // Reload data when submissions change
        loadGameData();
      })
      .subscribe((status) => {
        console.log('Submissions subscription status:', status);
      });

    return () => {
      supabase.removeChannel(gameSubscription);
      supabase.removeChannel(playersSubscription);
      supabase.removeChannel(submissionsSubscription);
    };
  }, [game, gameCode]);

  return {
    game,
    players,
    cardSubmissions,
    currentChallenge,
    currentPrompt,
    chatMessages,
    isLoading,
    error,
    createGame,
    joinGame,
    submitCard,
    selectChallengeNumber
  };
};

// Helper functions
const generateSafeWord = (): string => {
  const words = ['Pineapple', 'Butterfly', 'Rainbow', 'Unicorn', 'Sparkle', 'Moonbeam'];
  return words[Math.floor(Math.random() * words.length)];
};

const getChallengeType = (number: number): 'Truth' | 'Dare' | 'WouldYouRather' | 'Wildcard' => {
  if (number <= 8) return Math.random() < 0.5 ? 'Truth' : 'Dare';
  if (number <= 12) return Math.random() < 0.5 ? 'Truth' : 'Dare';
  if (number <= 16) return 'WouldYouRather';
  return 'Wildcard';
};
