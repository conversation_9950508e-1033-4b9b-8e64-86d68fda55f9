import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { getWhiteCardOptions } from '@/modules/SinfulSecrets/services/hybridGameFlow';

interface PlayerHandCard {
  id: string;
  card_text: string;
  card_position: number;
}

export const usePlayerHand = (playerId: string, gameId: string) => {
  const [playerHand, setPlayerHand] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize or load player hand
  useEffect(() => {
    if (!playerId || !gameId) return;

    const initializePlayerHand = async () => {
      setIsLoading(true);
      try {
        // Check if player already has a hand
        const { data: existingHand, error: fetchError } = await supabase
          .from('sinful_secrets_player_hands')
          .select('*')
          .eq('player_id', playerId)
          .eq('game_id', gameId)
          .order('card_position');

        if (fetchError) throw fetchError;

        if (existingHand && existingHand.length > 0) {
          // Player already has a hand, load it
          const handCards = existingHand.map(card => card.card_text);
          setPlayerHand(handCards);
          console.log('Loaded existing player hand:', handCards);
        } else {
          // Create new hand for player
          const allWhiteCards = getWhiteCardOptions();
          const shuffledCards = allWhiteCards.sort(() => Math.random() - 0.5);
          const newHand = shuffledCards.slice(0, 7); // Give 7 cards

          // Save hand to database
          const handData = newHand.map((cardText, index) => ({
            player_id: playerId,
            game_id: gameId,
            card_text: cardText,
            card_position: index
          }));

          const { error: insertError } = await supabase
            .from('sinful_secrets_player_hands')
            .insert(handData);

          if (insertError) throw insertError;

          setPlayerHand(newHand);
          console.log('Created new player hand:', newHand);
        }
      } catch (err) {
        console.error('Failed to initialize player hand:', err);
        setError(err instanceof Error ? err.message : 'Failed to load hand');
        
        // Fallback to random cards if database fails
        const allWhiteCards = getWhiteCardOptions();
        const fallbackHand = allWhiteCards.sort(() => Math.random() - 0.5).slice(0, 7);
        setPlayerHand(fallbackHand);
      } finally {
        setIsLoading(false);
      }
    };

    initializePlayerHand();
  }, [playerId, gameId]);

  // Remove a card from hand (when submitted)
  const removeCardFromHand = async (cardText: string) => {
    try {
      // Remove from database
      const { error: deleteError } = await supabase
        .from('sinful_secrets_player_hands')
        .delete()
        .eq('player_id', playerId)
        .eq('game_id', gameId)
        .eq('card_text', cardText);

      if (deleteError) throw deleteError;

      // Remove from local state
      setPlayerHand(prev => prev.filter(card => card !== cardText));
      console.log('Removed card from hand:', cardText);
    } catch (err) {
      console.error('Failed to remove card from hand:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove card');
    }
  };

  // Add a new card to hand (replacement)
  const addCardToHand = async (cardText: string, position: number) => {
    try {
      // Add to database
      const { error: insertError } = await supabase
        .from('sinful_secrets_player_hands')
        .insert({
          player_id: playerId,
          game_id: gameId,
          card_text: cardText,
          card_position: position
        });

      if (insertError) throw insertError;

      // Add to local state
      setPlayerHand(prev => {
        const newHand = [...prev];
        newHand[position] = cardText;
        return newHand;
      });
      console.log('Added card to hand:', cardText);
    } catch (err) {
      console.error('Failed to add card to hand:', err);
      setError(err instanceof Error ? err.message : 'Failed to add card');
    }
  };

  // Replace a submitted card with a new one
  const replaceSubmittedCard = async (submittedCard: string) => {
    try {
      // Get a new random card that's not already in hand
      const allWhiteCards = getWhiteCardOptions();
      const availableCards = allWhiteCards.filter(card => !playerHand.includes(card));
      
      if (availableCards.length === 0) {
        console.warn('No more unique cards available');
        return;
      }

      const newCard = availableCards[Math.floor(Math.random() * availableCards.length)];
      const cardPosition = playerHand.indexOf(submittedCard);

      if (cardPosition === -1) {
        console.warn('Submitted card not found in hand');
        return;
      }

      // Update database - replace the card
      const { error: updateError } = await supabase
        .from('sinful_secrets_player_hands')
        .update({ card_text: newCard })
        .eq('player_id', playerId)
        .eq('game_id', gameId)
        .eq('card_position', cardPosition);

      if (updateError) throw updateError;

      // Update local state
      setPlayerHand(prev => {
        const newHand = [...prev];
        newHand[cardPosition] = newCard;
        return newHand;
      });

      console.log('Replaced card:', submittedCard, 'with:', newCard);
    } catch (err) {
      console.error('Failed to replace card:', err);
      setError(err instanceof Error ? err.message : 'Failed to replace card');
    }
  };

  // Burn entire hand (refresh all cards)
  const burnHand = async () => {
    try {
      // Delete all current cards
      const { error: deleteError } = await supabase
        .from('sinful_secrets_player_hands')
        .delete()
        .eq('player_id', playerId)
        .eq('game_id', gameId);

      if (deleteError) throw deleteError;

      // Generate new hand
      const allWhiteCards = getWhiteCardOptions();
      const newHand = allWhiteCards.sort(() => Math.random() - 0.5).slice(0, 7);

      // Save new hand to database
      const handData = newHand.map((cardText, index) => ({
        player_id: playerId,
        game_id: gameId,
        card_text: cardText,
        card_position: index
      }));

      const { error: insertError } = await supabase
        .from('sinful_secrets_player_hands')
        .insert(handData);

      if (insertError) throw insertError;

      setPlayerHand(newHand);
      console.log('Burned hand, new cards:', newHand);
    } catch (err) {
      console.error('Failed to burn hand:', err);
      setError(err instanceof Error ? err.message : 'Failed to burn hand');
    }
  };

  return {
    playerHand,
    isLoading,
    error,
    removeCardFromHand,
    addCardToHand,
    replaceSubmittedCard,
    burnHand
  };
};
