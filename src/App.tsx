
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { GameModeProvider } from "./contexts/GameModeContext";
import Index from "./pages/Index";
import Lobby from "./pages/Lobby";
import PlayerGame from "./pages/PlayerGame";
import HostGame from "./pages/HostGame";
import HowToPlay from "./pages/HowToPlay";
import Admin from "./pages/Admin";
import NotFound from "./pages/NotFound";
import SinfulSecretsPlayer from "./pages/SinfulSecretsPlayer";
import ModeSelector from "./modules/ModeSelector";
import TnTModule from "./modules/TnT/TnTModule";
import SinfulSecretsModule from "./modules/SinfulSecrets/SinfulSecretsModule";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <GameModeProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/play" element={<Lobby />} />
            <Route path="/select-mode" element={<ModeSelector />} />
            <Route path="/modules/TnT" element={<TnTModule />} />
            <Route path="/modules/SinfulSecrets" element={<SinfulSecretsModule />} />
            <Route path="/player" element={<PlayerGame />} />
            <Route path="/sinful-secrets-player" element={<SinfulSecretsPlayer />} />
            <Route path="/host" element={<HostGame />} />
            <Route path="/how-to-play" element={<HowToPlay />} />
            <Route path="/admin" element={<Admin />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </GameModeProvider>
  </QueryClientProvider>
);

export default App;
