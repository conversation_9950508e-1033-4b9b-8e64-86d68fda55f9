
// This service worker can be customized later
// For now, it's a simple file that pre-caches key assets

const CACHE_NAME = 'tempted-n-twisted-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.json',
];

// Installation event
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache);
      })
  );
  
  // Force activation
  self.skipWaiting();
});

// Activation event
self.addEventListener('activate', (event) => {
  // Clean up old caches
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Take control immediately
  self.clients.claim();
});

// Fetch event - network first, fall back to cache
self.addEventListener('fetch', (event) => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') return;

  // Skip browser-sync and socket.io requests
  if (event.request.url.includes('browser-sync') ||
      event.request.url.includes('socket.io')) return;

  // Skip chrome extension URLs
  if (event.request.url.startsWith('chrome-extension://')) return;

  // Skip moz-extension URLs (Firefox)
  if (event.request.url.startsWith('moz-extension://')) return;
  
  event.respondWith(
    fetch(event.request)
      .then((response) => {
        // If the response was good, clone it and store it in the cache
        if (response.status === 200) {
          const responseClone = response.clone();
          caches.open(CACHE_NAME)
            .then((cache) => {
              cache.put(event.request, responseClone);
            });
        }
        return response;
      })
      .catch(() => {
        // If the network fails, try to get it from the cache
        return caches.match(event.request)
          .then((cachedResponse) => {
            if (cachedResponse) {
              return cachedResponse;
            }
            // If not in cache, return the offline page
            return caches.match('/offline.html');
          });
      })
  );
});
