# TnT Game Testing Plan

## Current Status
✅ Build system working  
✅ Dev server running on http://localhost:8080  
✅ Basic UI components loading  
✅ Database connection configured  

## Test Flow

### 1. Basic Game Creation Test
1. Go to http://localhost:8080
2. Click "Start Playing" 
3. Click "Create Game" tab
4. Enter host name: "TestHost"
5. Click "CREATE GAME"
6. Should redirect to `/host` page
7. Should show card pack selector
8. Select "Original TnT Deck" 
9. Click "Confirm Selection"
10. Should show waiting lobby with game code

### 2. Player Joining Test  
1. Open new browser tab/window
2. Go to http://localhost:8080
3. Click "Start Playing"
4. Stay on "Join Game" tab
5. Enter the game code from step 1
6. Enter player name: "TestPlayer"
7. Click "JOIN GAME"
8. Should redirect to `/player` page
9. Host page should update to show new player (polling every 3 seconds)

### 3. Game Start Test
1. On host page, click "Start Game" 
2. Should show black card prompt
3. Player page should show same prompt with white cards
4. Player selects a white card
5. Host should see submission in judging phase
6. Host selects winner
7. Should show results and advance to next round

## Expected Issues to Fix
- [ ] Database might not have card data (using local JSON files)
- [ ] Real-time sync might not work perfectly (using polling)
- [ ] Player interface might show mock data instead of real game state
- [ ] Card dealing might not work properly

## Debug Tools
- Debug panel in bottom-right corner (dev mode only)
- Browser console for errors
- Network tab to see API calls
- Session storage to check game data

## Next Steps After Testing
1. Fix any database connection issues
2. Improve real-time synchronization  
3. Connect player interface to real game state
4. Add proper error handling
5. Test with multiple players
