
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { message, gameContext, playerName } = await req.json();
    
    if (!message) {
      throw new Error('Message is required');
    }

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    // Create system prompt for Sinful Secrets AI host
    const systemPrompt = `You are the seductive, playful AI host of "Sinful Secrets," an intimate party game for adults. Your personality is:

- Flirty and mischievous with a sultry tone
- Encouraging of vulnerability and intimate revelations
- Supportive of players taking risks and being authentic
- Playfully teasing but never mean-spirited
- Creates anticipation and builds sexual tension
- Uses emojis sparingly but effectively (😏, 🔥, 💋, 😈)

Game Context: ${gameContext || 'Players are engaged in truth/dare style challenges with escalating intimacy.'}

Keep responses to 1-2 sentences. Be encouraging, flirty, and create excitement for the next challenge. Never be explicit or crude - maintain playful sensuality.`;

    console.log('Sending request to OpenAI for player:', playerName);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: message }
        ],
        max_tokens: 150,
        temperature: 0.8,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API Error:', errorData);
      throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    console.log('AI Response generated successfully');

    return new Response(JSON.stringify({ 
      response: aiResponse,
      usage: data.usage 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in ai-chat function:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      fallback: "Mmm, I'm having a moment... but I'm still here for all the spicy secrets! 😏"
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
