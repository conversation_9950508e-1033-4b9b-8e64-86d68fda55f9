
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { prompt, deckType = 'sinful-secrets', cardCount = { black: 8, white: 15 } } = await req.json();
    
    if (!prompt) {
      throw new Error('Prompt is required');
    }

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    // Create system prompt for Cards Against Humanity style deck generation
    const systemPrompt = `You are a creative card game designer specializing in Cards Against Humanity style content. Create cards that are:

- Irreverent, cheeky, and mildly provocative (but not offensive)
- Perfect for adult party games with friends
- Witty with unexpected humor and double entendres
- Relatable to modern life and relationships
- Suitable for the "Sinful Secrets" game atmosphere (flirty, spicy, fun)

BLACK CARDS (prompts/scenarios):
- Should be incomplete sentences with blanks (__________) for players to fill
- Should set up scenarios that allow for funny, spicy, or revealing responses
- Examples: "The most embarrassing thing in my browser history is __________." or "My dating profile would be more honest if it mentioned __________."

WHITE CARDS (responses):
- Should be phrases, actions, or concepts that can complete the black cards
- Should be versatile enough to work with multiple prompts
- Should range from mildly spicy to cheeky but always fun
- Examples: "Sliding into someone's DMs at 2 AM", "The walk of shame", "Pretending to be asleep when your roommate brings someone home"

User's theme: ${prompt}

Generate exactly ${cardCount.black} black cards and ${cardCount.white} white cards. Format your response as a JSON object with "blackCards" and "whiteCards" arrays.`;

    console.log('Generating custom deck for prompt:', prompt);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Create a Cards Against Humanity style deck about: ${prompt}` }
        ],
        max_tokens: 2000,
        temperature: 0.8,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API Error:', errorData);
      throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    // Parse the JSON response
    let parsedCards;
    try {
      parsedCards = JSON.parse(aiResponse);
    } catch (parseError) {
      console.error('Failed to parse AI response:', aiResponse);
      // Fallback: try to extract cards using regex if JSON parsing fails
      const blackCardMatches = aiResponse.match(/"blackCards":\s*\[(.*?)\]/s);
      const whiteCardMatches = aiResponse.match(/"whiteCards":\s*\[(.*?)\]/s);
      
      if (blackCardMatches && whiteCardMatches) {
        try {
          parsedCards = {
            blackCards: JSON.parse(`[${blackCardMatches[1]}]`),
            whiteCards: JSON.parse(`[${whiteCardMatches[1]}]`)
          };
        } catch (fallbackError) {
          throw new Error('AI generated invalid card format');
        }
      } else {
        throw new Error('AI response format is invalid');
      }
    }

    // Validate the response structure
    if (!parsedCards.blackCards || !parsedCards.whiteCards || 
        !Array.isArray(parsedCards.blackCards) || !Array.isArray(parsedCards.whiteCards)) {
      throw new Error('Invalid card deck structure from AI');
    }

    console.log('AI Deck generated successfully');
    console.log(`Generated ${parsedCards.blackCards.length} black cards and ${parsedCards.whiteCards.length} white cards`);

    return new Response(JSON.stringify(parsedCards), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in ai-deck-generator function:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      fallback: "Failed to generate custom deck. Please try again with a different description."
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
